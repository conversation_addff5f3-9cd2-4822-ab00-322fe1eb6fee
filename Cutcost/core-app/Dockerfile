FROM dunglas/frankenphp

COPY custom.ini /usr/local/etc/php/conf.d/99-custom.ini

RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash -

RUN apt-get update && apt-get install -y \
    supervisor \
    libjpeg-dev \
    libpng-dev \
    libzip-dev \
    libsodium-dev \
    git \
    libcurl4-openssl-dev \
    curl \
    libonig-dev \
    nodejs \
    libxml2-dev \
    && docker-php-ext-configure gd --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
    pdo_mysql \
    pcntl \
    exif \
    gd \
    zip \
    sodium \
    bcmath \
    mbstring \
    intl \
    curl \
    xml \
    dom \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# supervisor for multiprocess queue and websockets
RUN mkdir -p /var/log/supervisor
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

WORKDIR /var/www

COPY . /var/www

RUN mkdir -p /var/www/storage/logs && \
    chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache && \
    chmod -R 775 /var/www/storage /var/www/bootstrap/cache

COPY ./entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

RUN composer install --no-dev --optimize-autoloader --no-interaction

RUN composer require laravel/octane


ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]