<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearAppCache extends Command
{
    protected $signature = 'app-cache:clear';
    protected $description = 'Clear app cache';

    private $translations = ['en', 'lt', 'ru', 'pl', 'de', 'es'];

    public function handle()
    {
        $this->clearCacheForIndustries();
        $this->clearCacheForCountries();
        $this->clearCacheForCurrencies();
        $this->clearCacheForLocales();
        $this->clearCacheForSalesBuilderRules();

        foreach ($this->translations as $locale) {
            $this->clearCacheForLocale($locale);
        }
    }

    private function clearCacheForLocales()
    {
        if (!Cache::has('locales')) {
            $this->info('No cache found for locations');
        } else {
            Cache::forget('locales');
            $this->info('Locales cache cleared');
        }
    }

    private function clearCacheForCurrencies()
    {
        if (!Cache::has('currencies')) {
            $this->info('No cache found for currencies');
        } else {
            Cache::forget('currencies');
            $this->info('Currencies cache cleared');
        }
    }

    private function clearCacheForIndustries()
    {
        if (!Cache::has('industries')) {
            $this->info('No cache found for industries');
        } else {
            Cache::forget('industries');
            $this->info('Industries cache cleared');
        }
    }

    private function clearCacheForCountries()
    {
        if (!Cache::has('countries')) {
            $this->info('No cache found for countries');
        } else {
            Cache::forget('countries');
            $this->info('Countries cache cleared');
        }
    }

    private function clearCacheForLocale($locale)
    {
        $key = "translations_$locale";
        if (Cache::forget($key)) {
            $this->info("Translations cache cleared for translation: $locale");
        } else {
            $this->info("No cache found for translation: $locale");
        }
    }
}
