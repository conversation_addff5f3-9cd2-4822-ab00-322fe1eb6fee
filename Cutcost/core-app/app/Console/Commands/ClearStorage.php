<?php

namespace App\Console\Commands;

use App\Shared\Models\Media;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ClearStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-storage:clear';

    public function handle()
    {
        Media::whereNotNull('temp_uuid')->pluck('path')->each(function ($path) {
            Storage::disk('public')->delete($path);
        });
        Media::whereNotNull('temp_uuid')->delete();
    }
}
