<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Shared\Services\SitemapService;

class GenerateSitemap extends Command
{
    protected $signature = 'sitemap:generate';
    protected $description = 'Generate sitemap.xml';

    public function __construct(private SitemapService $sitemapService)
    {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info('⏳ Generating sitemap.xml...');

        $this->sitemapService->generate();

        $this->info('✅ sitemap.xml generated!');
        return Command::SUCCESS;
    }
}
