<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SyncJsonKeys extends Command
{
    protected $signature = 'json:sync 
        {path=resources/lang : Корневая папка локалей} 
        {--base=en : Базовая локаль} 
        {--union : Объединять ключи из всех локалей} 
        {--copy-base : Копировать значения из базовой локали (без этого флага ставятся пустые строки)}';

    protected $description = 'Синхронизирует ключи во всех JSON-файлах локалей';

    public function handle()
    {
        $root = rtrim($this->argument('path'), DIRECTORY_SEPARATOR);
        $baseLocale = $this->option('base');
        $useUnion = (bool) $this->option('union');
        $copyBaseValues = (bool) $this->option('copy-base');

        $basePath = $root . DIRECTORY_SEPARATOR . $baseLocale;
        if (!File::exists($basePath)) {
            $this->error("Базовая директория не найдена: {$basePath}");
            return 1;
        }

        // Собираем список локалей
        $locales = array_map('basename', File::directories($root));
        if (!in_array($baseLocale, $locales, true)) {
            $this->error("Базовая локаль {$baseLocale} отсутствует в {$root}");
            return 1;
        }

        // Собираем список относительных путей JSON-файлов (из всех локалей при --union,
        // иначе — только из базовой)
        $relativeFiles = [];
        $scanLocales = $useUnion ? $locales : [$baseLocale];

        foreach ($scanLocales as $loc) {
            $dir = $root . DIRECTORY_SEPARATOR . $loc;
            foreach (File::allFiles($dir) as $file) {
                if ($file->getExtension() !== 'json') {
                    continue;
                }
                $relativeFiles[] = $file->getRelativePathname(); // например: messages/ui.json
            }
        }
        $relativeFiles = array_values(array_unique($relativeFiles));

        $totalUpdated = 0;

        foreach ($relativeFiles as $relative) {
            // Загружаем данные всех локалей для этого относительного пути
            $dataByLocale = [];
            foreach ($locales as $loc) {
                $abs = $root . DIRECTORY_SEPARATOR . $loc . DIRECTORY_SEPARATOR . $relative;
                $dataByLocale[$loc] = $this->readJson($abs);
            }

            if ($useUnion) {
                // UNION-режим: строим "шаблон" ключей по всем локалям (типо-схему),
                // затем добавляем недостающие ключи ВСЕМ.
                $unionTemplate = [];
                foreach ($dataByLocale as $loc => $data) {
                    $this->deepAddMissingKeys($unionTemplate, $data, true); // копируем структуру/типы
                }
                // Превращаем значения шаблона в плейсхолдеры ('' / [] / {}), чтобы в целевых локалях
                // не утекали реальные строки из других языков
                $unionPlaceholders = $this->placeholderify($unionTemplate);

                foreach ($locales as $loc) {
                    $before = $dataByLocale[$loc];
                    // Если просили копировать базовые значения, сначала добавим ключи из базы с её значениями
                    if ($copyBaseValues && $baseLocale !== $loc) {
                        $this->deepAddMissingKeys(
                            $dataByLocale[$loc],
                            $dataByLocale[$baseLocale] ?? [],
                            true // копируем значения из базы
                        );
                    }
                    // Теперь добавим весь союз ключей плейсхолдерами (ничего не перезатираем)
                    $this->deepAddMissingKeys($dataByLocale[$loc], $unionPlaceholders, false);

                    if ($before !== $dataByLocale[$loc]) {
                        $this->writeJson($root . DIRECTORY_SEPARATOR . $loc . DIRECTORY_SEPARATOR . $relative, $dataByLocale[$loc]);
                        $this->info("Обновлён: {$loc}/{$relative}");
                        $totalUpdated++;
                    }
                }
            } else {
                // Базовый режим: добавляем ключи только из базы во все остальные
                $baseData = $dataByLocale[$baseLocale] ?? [];
                $template = $copyBaseValues ? $baseData : $this->placeholderify($baseData);

                foreach ($locales as $loc) {
                    if ($loc === $baseLocale) continue;

                    $before = $dataByLocale[$loc];
                    $this->deepAddMissingKeys($dataByLocale[$loc], $template, $copyBaseValues);

                    if ($before !== $dataByLocale[$loc]) {
                        $this->writeJson($root . DIRECTORY_SEPARATOR . $loc . DIRECTORY_SEPARATOR . $relative, $dataByLocale[$loc]);
                        $this->info("Обновлён: {$loc}/{$relative}");
                        $totalUpdated++;
                    }
                }
            }
        }

        $this->info("✅ Синхронизация завершена. Обновлено файлов: {$totalUpdated}");
        return 0;
    }

    /* ==== ВСПОМОГАТЕЛЬНЫЕ ==== */

    protected function readJson(string $path): array
    {
        if (!File::exists($path)) {
            return [];
        }
        $raw = File::get($path);
        $data = json_decode($raw, true);

        if (json_last_error() !== JSON_ERROR_NONE || !is_array($data)) {
            $this->warn("Не удалось распарсить JSON, используем пустой: {$path}");
            return [];
        }
        return $data;
    }

    protected function writeJson(string $path, array $data): void
    {
        File::ensureDirectoryExists(dirname($path));
        File::put(
            $path,
            json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)
        );
    }

    /**
     * Рекурсивно добавляет недостающие ключи из $template в $target.
     * Если $copyValues = true — копирует значения шаблона,
     * иначе — только структуру (значения уже должны быть плейсхолдерами).
     */
    protected function deepAddMissingKeys(array &$target, array $template, bool $copyValues): void
    {
        foreach ($template as $key => $tplVal) {
            if (!array_key_exists($key, $target)) {
                $target[$key] = $copyValues ? $tplVal : $tplVal;
                continue;
            }

            // Если и там, и там ассоциативные массивы — спускаемся глубже
            if (is_array($tplVal) && is_array($target[$key]) && $this->isAssoc($tplVal) && $this->isAssoc($target[$key])) {
                $this->deepAddMissingKeys($target[$key], $template[$key], $copyValues);
            }
        }
    }

    /**
     * Превращает структуру в плейсхолдеры:
     * - строки/числа/булево/null -> ''
     * - списки -> []
     * - ассоц. объекты -> рекурсивно, листья — ''
     */
    protected function placeholderify($value)
    {
        if (is_array($value)) {
            if ($this->isAssoc($value)) {
                $out = [];
                foreach ($value as $k => $v) {
                    $out[$k] = $this->placeholderify($v);
                }
                return $out;
            }
            // список (0..N-1)
            return [];
        }
        // скаляры -> пустая строка
        return '';
    }

    protected function isAssoc(array $arr): bool
    {
        if ($arr === []) return false;
        return array_keys($arr) !== range(0, count($arr) - 1);
    }
}
