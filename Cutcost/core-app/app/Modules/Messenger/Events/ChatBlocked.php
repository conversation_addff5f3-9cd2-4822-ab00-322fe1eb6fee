<?php

namespace App\Modules\Messenger\Events;

use App\Modules\Messenger\Models\Chat;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ChatBlocked implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        private Chat $chat,
        private ?int $blockedBy = null,
    ) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('messenger-chat.' . $this->chat->id),
        ];
    }

    public function broadcastAs(): string
    {
        return 'chat.blocked';
    }

    public function broadcastWith(): array
    {
        return [
            'chat_id' => $this->chat->id,
            'blocked_by' => $this->blockedBy,
        ];
    }
}
