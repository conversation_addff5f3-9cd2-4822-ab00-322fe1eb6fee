<?php

namespace App\Modules\Messenger\Events;

use App\Modules\Messenger\Models\Chat;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageDeleted implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        private Chat $chat,
        private array $data,
    ) {}


    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('messenger-chat.' . $this->chat->id),
        ];
    }

    public function broadcastAs(): string
    {
        return 'message.deleted';
    }

    public function broadcastWith(): array
    {
        return [
            'prior_message' => $this->data['prior_message'],
            'message_id' => $this->data['message_id'],
            'chat_id' => $this->chat->id,
        ];
    }
}
