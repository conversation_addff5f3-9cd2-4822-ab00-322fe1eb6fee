<?php

namespace App\Modules\Messenger\Events;

use App\Modules\Messenger\Models\Chat;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        private Chat $chat,
        private array $message,
    ) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('messenger-chat.' . $this->chat->id),
        ];
    }

    public function broadcastAs(): string
    {
        return 'message.updated';
    }

    public function broadcastWith(): array
    {
        return [
            'message' => $this->message,
            'chat_id' => $this->chat->id,
        ];
    }
}
