<?php

namespace App\Modules\Messenger\Exceptions;

use App\Modules\Messenger\Traits\MessengerTrait;
use Exception;

class ChatException extends Exception
{

    public function report(): void
    {
        // nothing to log, this exception under app control
        // Log::info('Chat info: ' . $this->getMessage());
    }

    public function render()
    {
        return self::toChats()->with('warning', $this->getMessage());
    }
}
