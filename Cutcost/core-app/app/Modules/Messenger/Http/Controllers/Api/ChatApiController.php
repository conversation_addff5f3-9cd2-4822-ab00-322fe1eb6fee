<?php

namespace App\Modules\Messenger\Http\Controllers\Api;

use App\Modules\Messenger\Enums\ChatTypes;
use App\Modules\Messenger\Http\Resources\MessageResource;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatRepository;
use App\Modules\Messenger\Repositories\MessageRepository;
use App\Modules\Messenger\Services\ChatService;
use App\Modules\Messenger\Services\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ChatApiController
{

    public function paginateMessages(
        Chat $chat,
        // if first load, then false, else message id
        int|bool $cursor,
        Request $request,
        MessageRepository $messageRepository,
        ChatService $chatService,
        MessageService $messageService,
    ): JsonResponse {

        $user = $request->user();

        $messages = $chatService
            ->getTransformedChatMessages($chat, $cursor, $messageRepository);

        $messages = $messageService->read($chat, $user, $messages);

        return response()->json([
            'messages' => MessageResource::collection($messages),
        ]);
    }

    // or remove for user
    public function quitChat(
        Chat $chat,
        Request $request,
        ChatService $chatService,
        ChatRepository $chatRepository,
    ): JsonResponse {

        $user = $request->user();

        // we cant quit notes chat
        if ($chat->type === ChatTypes::NOTES->value) abort(403);

        if (
            $chat->type === ChatTypes::GROUP->value
            && $chat->created_by === $user->id
        ) {
            // delete chat for all users
            $chatRepository->forceQuitAllUsers($chat);
            $chat->delete();
        } else {
            // soft quit for user
            $chatService->quitChat($user, $chat, $chatRepository);
        }

        return response()->json(['status', 'success']);
    }

    public function blockChat(
        Chat $chat,
        Request $request,
        ChatService $chatService,
    ): JsonResponse {

        $user = $request->user();

        if ($chat->type !== ChatTypes::CHAT->value) abort(403);

        $chatService->blockChat($user, $chat);

        return response()->json([
            'status' => 'success',
            'blocked_by' => $user->id,
        ]);
    }

    public function unBlockChat(
        Chat $chat,
        ChatService $chatService,
    ): JsonResponse {

        if ($chat->type !== ChatTypes::CHAT->value) abort(403);

        $chatService->unBlockChat($chat);

        return response()->json([
            'status' => 'success',
            'blocked_by' => null
        ]);
    }
}
