<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Http\Controllers\Api;

use App\Modules\Messenger\Http\Controllers\ChatController;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatsRepository;
use App\Modules\Messenger\Services\ChatsService;
use App\Shared\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

// use Illuminate\Support\Facades\DB;
// use Illuminate\Support\Facades\Log;

/**
 * :TODO make tests with query log
 */
class ChatsApiController
{
    public function getChats(
        User $user,
        Request $request,
        // make this classes as singleton for every request 
        // :TODO check data leak in octane !!!
        ChatsService $chatsService,
        ChatsRepository $chatsRepository
    ): JsonResponse {

        $filters = $request->validate([
            'mode' => ['nullable', 'string', 'max:128'],
        ]);

        // $queries = [];
        // DB::listen(function ($query) use (&$queries) {
        //     $queries[] = [
        //         'sql' => $query->sql,
        //         'bindings' => $query->bindings,
        //         'time' => $query->time,
        //     ];
        // });

        $user->load(['companies:user_id,id,slug,name', 'chats', 'roles']);

        $chatsService->checkNotes($user, $chatsRepository);

        $chats = $chatsService->getTransformedChatsData(
            $user,
            $filters,
            $chatsRepository
        );

        // Log::info([
        //     'total_query_count' => count($queries),
        //     'total_query_time_ms' => array_sum(array_column($queries, 'time')),
        //     'all_queries' => $queries,
        // ]);

        return response()->json($chats);
    }
}
