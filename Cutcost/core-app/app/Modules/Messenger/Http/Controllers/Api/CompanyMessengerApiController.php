<?php

namespace App\Modules\Messenger\Http\Controllers\Api;

use App\Modules\Partner\Http\Resources\LocationResource;
use App\Modules\Partner\Models\Company;

class CompanyMessengerApiController
{
    public function getCompanyInfo(Company $company)
    {
        $company->load('industries');

        $locations = $company->locations()->active()->get();

        return response()->json([
            'company' => $company,
            'locations' => LocationResource::collection($locations),
            'subscribers' => $company->subscribers()->count(),
        ]);
    }
}
