<?php

namespace App\Modules\Messenger\Http\Controllers\Api;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatRepository;
use App\Shared\Http\Resources\UserMiniResource;
use App\Shared\Models\User;
use App\Shared\Services\FileDriverService;
use App\Shared\Services\MediaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GroupApiController
{
    public function addUser(
        Chat $chat,
        Request $request,
        ChatRepository $chatRepository,
    ): JsonResponse {

        try {
            $validated = $request->validate([
                'nickname' => ['required', 'string', 'between:2,32'],
            ]);

            $user = User::where('nickname', $validated['nickname'])->firstOrFail();

            $creator = $request->user();

            // gate for admin only
            if ($chat->created_by !== $creator->id) abort(403);

            $chatRepository->joinChat($user, $chat);

            return response()->json([
                'status' => 'success',
                'message' => __('app.user_added'),
                'user' => new UserMiniResource($user),
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['status' => 'error', 'message' => __('app.user_not_found')], 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['status' => 'error', 'message' => $e->validator->errors()->first()], 422);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }

    public function removeUser(
        Chat $chat,
        User $user,
        Request $request,
        ChatRepository $chatRepository,
    ): JsonResponse {

        try {
            $creator = $request->user();

            if ($chat->created_by !== $creator->id) abort(403);

            $chatRepository->forceQuitChat($user, $chat);

            return response()->json(['status' => 'success', 'message' => __('app.user_removed')]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }

    public function updateGroupAvatar(
        Request $request,
        Chat $chat,
        MediaService $MediaService,
    ): JsonResponse {

        try {
            $request->validate([
                'avatar' => ['required', 'image', 'mimes:png,jpg,jpeg,gif', 'max:10000'],
            ]);

            $MediaService->deleteIfExists($chat->avatar);

            $chat->avatar = $MediaService->storeIn($request->file('avatar'), 'avatars/chats');

            $chat->save();

            return response()->json(['status' => 'success', 'message' => __('app.avatar_updated')]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }

    public function updateGroupName(
        Request $request,
        Chat $chat,
    ): JsonResponse {

        try {
            $request->validate([
                'name' => ['required', 'string', 'between:2,32'],
            ]);

            $chat->name = $request->input('name');
            $chat->save();

            return response()->json(['status' => 'success', 'message' => __('app.name_updated')]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }
}
