<?php

namespace App\Modules\Messenger\Http\Controllers\Api;

use App\Modules\Messenger\Http\Requests\MessageRequest;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Models\Message;
use App\Modules\Messenger\Services\ChatService;
use App\Modules\Messenger\Services\MessageService;
use App\Shared\Services\MediaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

// no message to the client from json response
// cause all data will send via sockets

class MessageApiController
{
    public function sendMessage(
        Chat $chat,
        MessageRequest $messageRequest,
        MessageService $messageService,
        ChatService $chatService,
        MediaService $mediaService,
    ): JsonResponse {
        try {

            $validated = $messageRequest->safe()->all();
            $user = $messageRequest->user();

            // gate
            if ($chat->blocked_by) abort(403);
            if (! $chatService->isUserInChat($user, $chat)) abort(403);

            $messageService->send(
                $chat,
                $user,
                $validated,
                $mediaService
            );

            return response()->json(['status', 'success']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }

    public function updateMessage(
        MessageRequest $messageRequest,
        Chat $chat,
        Message $message,
        MessageService $messageService,
        MediaService $mediaService,
    ): JsonResponse {

        $user = $messageRequest->user();

        // gate 
        if ($chat->blocked_by || $chat->company_id) abort(403);
        if ($message->user_id !== $user->id) abort(403);

        $validated = $messageRequest->safe()->all();

        try {
            $messageService->update(
                $message,
                $chat,
                $validated,
                $mediaService
            );
            return response()->json(['status', 'success']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }

    public function destroyMessage(
        Chat $chat,
        Message $message,
        MessageService $messageService,
    ): JsonResponse {
        $user = auth()->user();

        try {
            // gate
            if ($chat->blocked_by || $chat->company_id) abort(403);
            if ($message->user_id !== $user->id) abort(403);

            $messageService->destroy(
                $message,
                $chat,
            );
            return response()->json(['status', 'success']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }

    public function readMessage(
        Message $message,
        Request $request,
        MessageService $messageService
    ): JsonResponse {
        try {

            $user = $request->user();

            // no gate needed cause we are reading messages

            $messageService->read(
                $message->chat,
                $user,
                collect([$message]),
            );

            return response()->json(['status', 'success']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['status' => 'error', 'message' => __('messages.errors.wrong')], 500);
        }
    }
}
