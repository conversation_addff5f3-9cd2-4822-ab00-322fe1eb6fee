<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Http\Controllers;

use App\Modules\Messenger\Repositories\ChatRepository;
use App\Modules\Messenger\Repositories\ChatsRepository;
use App\Modules\Messenger\Services\ChatService;
use App\Modules\Messenger\Services\ChatsService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Response;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ChatController 
{
    public function index(
        Request $request,
        ChatsService $chatsService,
        ChatsRepository $chatsRepository
    ): Response|RedirectResponse {

        $chatsService->checkNotes($request->user(), $chatsRepository);

        return Inertia::render('Messenger/Chats', [
            'companies' => $request->user()->companies()
                ->select('slug', 'name', 'id')->get(),
        ]);
    }

    public function chat(
        Request $request,
        ChatService $chatService,
        ChatRepository $chatRepository,
        ChatsService $chatsService,
        ChatsRepository $chatsRepository
    ) {
        $validated = $request->validate([
            // user by nickname, company by slug, chat by id
            'type' => ['required', 'string', 'in:nickname,slug,id'],
            // type value
            'key' => ['required', 'string', 'max:128'],
        ]);

        $type = $validated['type'];
        $key = $validated['key'];

        $user = $request->user()
            ->loadMissing('roles', 
            // property company
            'companies:id,slug,name,user_id', 'chats');

        $chatsService->checkNotes($user, $chatsRepository);


        $chat = $chatService
            ->getTransformedChatData($user, $chatRepository, $type, $key);

        try {
            return Inertia::render('Messenger/ShowChat', [
                'chat' => $chat,
                'companies' => $user->companies,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return self::toChats()->with('info', __('messages.errors.wrong', ['subject' => 'chat']));
        }
    }
}
