<?php

namespace App\Modules\Messenger\Http\Controllers;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatsRepository;
use App\Modules\Messenger\Services\GroupService;
use App\Modules\Messenger\Services\MessageService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class GroupController 
{

    public function updateGroupAvatar(Request $request, Chat $group)
    {
        if ($group->creator_id !== auth()->id()) abort(403);

        return $this->updateAvatarPhoto($request, $group);
    }

    public function createGroup(
        Request $request,
        ChatsRepository $chatsRepository,
        GroupService $groupService,
        MessageService $messageService,
    ): RedirectResponse {

        $validated = $request->validate([
            'name' => ['required', 'string', 'between:2,32'],
            'is_public' => ['required', 'boolean'],
        ]);

        $user = $request->user();

        $chat = $groupService->createGroup($user, $validated, $chatsRepository);

        $messageService->send($chat, $user, [
            'content' => 'group_created',
            'type' => 'notification',
        ], null);

        return redirect(action(
            [ChatController::class, 'chat'],
            [
                'type' => 'id',
                'key' => $chat->id,
            ]
        ));
    }
}
