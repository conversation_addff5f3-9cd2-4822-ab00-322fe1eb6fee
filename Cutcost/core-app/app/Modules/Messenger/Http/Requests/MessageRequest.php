<?php

namespace App\Modules\Messenger\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MessageRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'content' => [
                'nullable',
                'required_without:upload',
                'string',
                'between:1,3000'
            ],
            'upload.temp_uuid' => 'nullable|string|uuid',
            'upload.deleted_ids' => 'nullable|array',
            'replied_content' => ['nullable', 'string', 'between:1,100'],
        ];
    }
}
