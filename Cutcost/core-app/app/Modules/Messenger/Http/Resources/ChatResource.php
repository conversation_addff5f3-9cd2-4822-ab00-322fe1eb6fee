<?php

namespace App\Modules\Messenger\Http\Resources;

use App\Modules\Messenger\Models\Chat;
use App\Shared\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Modules\Messenger\Enums\ChatTypes;
use App\Modules\Partner\Models\Company;
use App\Shared\Http\Resources\UserMiniResource;
use Illuminate\Support\Facades\Log;

// use Illuminate\Support\Facades\Log;

final class ChatResource extends JsonResource
{
    public function __construct(
        Chat $resource,
        private User $user,
        private ?User $friend = null,
        // company loads as relation for chats and from getChatByCompanySlug for chat 
        private ?Company $companyForChat = null,
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        // init 
        $friend = $this->friend;
        $avatar = '';
        $chatName = '';

        $company = $this->relationLoaded('company') ?
            $this->company :
            $this->companyForChat;

        // default show friend if company dont exists
        $showFriendInsteadOfCompany = empty($company);

        if (
            isset($company, $this->user?->companies)
            && in_array($company->id, $this->user->companies->pluck('id')->all(), false)
        ) {
            $friend = $this->users->where('id', '!=', $this->user->id)->first();
            $showFriendInsteadOfCompany = true;
        }

        // not loaded for chat, cause messages loads separately
        $lastMessage = $this->relationLoaded('lastMessage')
            ? $this->lastMessage
            : null;

        if ($this->type === ChatTypes::NOTES->value) {
            $avatar = $this->user->avatar;
            $chatName = 'Notes'; // translate
        }

        if ($this->type === ChatTypes::CHAT->value) {

            // for chat with company
            if ($company && !$showFriendInsteadOfCompany) {
                $chatName = $company->name;
                $avatar = $company->avatar;
                $company->load('bot');
            }

            // with friend
            if ($friend && $showFriendInsteadOfCompany) {
                $chatName = $friend->display_name;
                $avatar = $friend->avatar;
            }
        }

        if ($this->type === ChatTypes::GROUP->value) {
            $chatName = $this->name;
            $avatar = $this->avatar;
        }

        return [
            'id'                => $this->id, // uuid
            'created_at'        => $this->created_at,
            'type'              => $this->type, // chat, group, notes
            'name'              => $chatName,
            'created_by'        => $this->created_by,
            'blocked_by'        => $this->blocked_by,
            'avatar'            => $avatar,
            'last_message'      => new MessageResource($lastMessage),
            'is_public'         => $this->is_public,
            'new_messages'      => isset($this->new_messages_count)
                ? $this->new_messages_count : null,
            'company'           => $company,
            'friend'            => new UserMiniResource($friend),
            'users'             => $this->relationLoaded('users')
                ? UserMiniResource::collection($this->users) : [],
        ];
    }
}
