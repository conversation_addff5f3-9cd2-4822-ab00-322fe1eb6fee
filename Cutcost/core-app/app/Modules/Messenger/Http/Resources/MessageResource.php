<?php

namespace App\Modules\Messenger\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Shared\Http\Resources\UserMiniResource;

class MessageResource extends JsonResource
{

    protected array $overrides = [];

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'content' => $this->content,
            'edited' => $this->edited,
            'type' => $this->type,
            'status' => $this->status,
            'user' => $this->whenLoaded(
                'user',
                new UserMiniResource($this->user),
                []
            ),
            'sent_date' => $this->created_at,
            'created_at' => $this->created_at->format('H:i'),
            'date' => $this->created_at->format('Y-m-d'),
            'media' => $this->whenLoaded('media'),
            'replied_content' => $this->replied_content,
        ];
    }
}
