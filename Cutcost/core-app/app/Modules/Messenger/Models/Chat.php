<?php

namespace App\Modules\Messenger\Models;

use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class Chat extends Model
{
    use HasUuids;

    protected $fillable = [
        // chat', 'group', 'notes
        'type',
        'name',
        'company_id',
        'blocked_by',
        'created_by',
        'is_public',
        'avatar',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'chat_user', 'chat_id', 'user_id')
            ->withTimestamps();
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    // last message for chats page
    public function lastMessage()
    {
        return $this->hasOne(Message::class)->latestOfMany();
    }
}
