<?php

namespace App\Modules\Messenger\Models;

use App\Shared\Models\Media;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'content',
        'user_id',
        // audio, text, notification, model, report
        'type',
        'chat_id',
        'status',
        'replied_content',
        'edited',
    ];

    protected $casts = [
        'edited' => 'boolean',
    ];

    public function media()
    {
        return $this->morphMany(Media::class, 'model')->orderBy('order_column');
    }

    public function chat()
    {
        return $this->belongsTo(Chat::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
