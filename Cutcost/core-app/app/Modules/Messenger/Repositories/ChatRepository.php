<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Repositories;

use App\Modules\Messenger\Enums\ChatTypes;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;

final class ChatRepository
{
    public function getChatByUserNickname(User $user, string $nickname): ?array
    {
        // find friend chats | sort by pivot
        $friend = User::where('nickname', $nickname)
            // !$friend abort 404
            ->firstOrFail();


        $chat = Chat::where('type', ChatTypes::CHAT->value)
            ->whereHas(
                'users',
                fn($q) => $q->where('user_id', $user->id)
            )
            // and
            ->whereHas(
                'users',
                fn($q) => $q->where('user_id', $friend->id)
            )
            ->where('company_id', null)
            // must be only 2 users in chat
            ->withCount('users')
            ->having('users_count', 2)
            ->first();

        return [$chat, $friend];
    }

    public function getChatById(User $user, string $id): ?array
    {
        $chat = Chat::where('id', $id)->first();

        if (!$chat) return abort(404);

        // load participants for group
        if ($chat && $chat->type === ChatTypes::GROUP->value) {
            $chat->load('users:id,avatar,nickname,display_name');
        }

        $company = null;
        $friend = null;

        if ($chat?->company_id) {
            $company = $chat->company;
        }

        if ($chat->type === ChatTypes::CHAT->value) {
            $friend = $chat->users->first(fn($u) => $u->id !== $user->id);
        }

        return [$chat, $friend, $company];
    }

    public function getChatByCompanySlug(User $user, string $slug): ?array
    {
        $company = Company::where('slug', $slug)
            // !$company abort 404
            ->firstOrFail();

        $chat = $company->chats()
            ->whereHas(
                'users',
                fn($q) => $q->where('user_id', $user->id)
            )->where('type', ChatTypes::CHAT->value)
            // must be only 2 users in chat
            ->withCount('users')
            ->having('users_count', 2)
            ->first();

        return [$chat, $company];
    }

    public function getNotesChat(User $user): ?Chat
    {

        return $user->chats()
            ->where('type', ChatTypes::NOTES->value)->first();
    }

    public function softQuitChat(User $user, Chat $chat): void
    {
        $chat->users()->updateExistingPivot($user->id, ['left_at' => now()]);
    }

    public function forceQuitChat(User $user, Chat $chat): void
    {
        $chat->users()->detach($user->id);
    }

    public function forceQuitAllUsers(Chat $chat): void
    {
        $chat->users()->detach();
    }

    public function joinBackChat(User $user, Chat $chat): void
    {
        $chat->users()->updateExistingPivot($user->id, ['left_at' => null]);
    }

    public function joinChat(User $user, Chat $chat): void
    {
        $chat->users()->attach($user->id);
    }
}
