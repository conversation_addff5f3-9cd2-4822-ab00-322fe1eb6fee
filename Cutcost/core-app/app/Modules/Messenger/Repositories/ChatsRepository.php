<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Repositories;

use App\Modules\Messenger\Models\Chat;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Collection;
use App\Modules\Messenger\Enums\ChatTypes;
use Illuminate\Support\Facades\Log;

/**
 * Handle chat list DB logic
 * Operations with data
 */

final class ChatsRepository
// :TODO implements ChatsContract
{

    # ---------------------- read ----------------------

    public function getNotes(User $user): Builder|Relation
    {
        return $user->chats()->where('type', ChatTypes::NOTES->value);
    }

    public function getChats(User $user, array $filters): Collection
    {
        $mode = data_get($filters, 'mode', 'all_chats');

        // initialization
        $companyId = null;

        if ($mode === 'all_chats') $mode = 'personal';

        if (str_contains($mode, 'company_chats')) {
            $companyId = (int) explode('_', $mode)[2];
            $mode = 'company';
            // Log::info([$companyId]);
        }

        $groupAndNotesQuery = $this->getGroupAndNotesChats(
            $user,
            $mode,
        );

        $chatsQuery = $this->getChatsByMode(
            $user,
            $mode,
            $companyId,
        );

        return $groupAndNotesQuery->concat($chatsQuery);
    }

    private function getGroupAndNotesChats(User $user, string $mode): Collection
    {
        // only in personal chats
        if ($mode === 'company')
            return collect();

        return $user->chats()
            ->whereIn('type', [ChatTypes::NOTES->value, ChatTypes::GROUP->value])
            ->wherePivotNull('left_at')
            ->with('lastMessage.user:id,avatar,nickname,display_name', 'lastMessage.media:id,model_id')
            ->get();
    }

    private function getChatsByMode(User $user, string $mode, ?int $companyId): Collection
    {

        $userCompanyIds = $user->companies->pluck('id')->filter()->all();

        return $user->chats()
            ->when(
                $mode === 'company',
                fn($q) => $q->where('company_id', $companyId)
            )
            ->when(
                $mode === 'personal',
                fn($q) => $q->where(function ($q) use ($userCompanyIds) {
                    if (!empty($userCompanyIds)) {
                        $q->whereNotIn('company_id', $userCompanyIds)
                            ->orWhereNull('company_id');
                    }
                })
            )
            ->where('type', ChatTypes::CHAT->value)
            ->wherePivotNull('left_at')
            ->with([
                'lastMessage.user:id,avatar,nickname,display_name',
                'users:id,avatar,nickname,display_name',
                'company:id,name,avatar,slug',
                'lastMessage.media:id,model_id'
            ])
            ->withCount([
                'messages as new_messages_count' => fn($q) => $q
                    ->where('status', 'unread')
                    ->where('user_id', '!=', $user->id),
            ])
            ->get()
            // after query
            // filter out empty chats
            // but keep chats created by user
            ->filter(function ($chat) use ($user) {
                if ($chat->created_by === $user->id) return true;
                if (!$chat->lastMessage?->content) return false;

                return true;
            });
    }

    # ---------------------- create/update/delete ----------------------

    /**
     * @param User $creator
     * @param Collection $participants - plucked users ids 
     * 
     * @return Chat
     */
    public function createChat(
        User $creator,
        Collection $participants,
        ChatTypes $type,
        ?array $validated = null
    ): Chat {

        // :todo make exception class
        if ($participants->count() === 0) throw new \Exception('No participants');

        $chat = Chat::create(array_merge([
            'created_by' => $creator->id,
            'type' => $type->value,
        ], $validated ?? []));

        $chat->users()->attach($participants);

        return $chat;
    }
}
