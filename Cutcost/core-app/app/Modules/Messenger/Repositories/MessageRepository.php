<?php

namespace App\Modules\Messenger\Repositories;

use App\Modules\Messenger\Models\Chat;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class MessageRepository
{
    public function getMessages(Chat $chat, int $take, int|bool $cursor): Builder|Relation
    {
        return $chat->messages()
            ->with(['user:id,avatar,nickname,display_name', 'media'])
            ->orderByDesc('id')
            ->when(is_int($cursor), fn($q) => $q->where('id', '<', $cursor))
            ->take($take);
    }
}
