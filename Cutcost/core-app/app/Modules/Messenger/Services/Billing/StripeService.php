<?php

namespace App\Modules\Messenger\Services\Billing;

use App\Modules\Partner\Contracts\PaymentContract;
use App\Modules\Partner\Contracts\PaymentResult;
use App\Shared\Models\User;
use App\Modules\Partner\Models\Billing\Partner;
use App\Modules\Partner\Models\Billing\Plan;

class StripeService implements PaymentContract
{
    public function createSubscription(User $user, Plan $plan): PaymentResult
    {
        // Имитация успешной подписки
        // Можно здесь сразу создать партнёра вручную
        if (!Partner::where('user_id', $user->id)->exists()) {
            Partner::create([
                'user_id' => $user->id,
                'full_name' => $user->name ?? 'Без имени',
                'joined_at' => now(),
            ]);
        }

        // Возвращаем успешный результат с фиктивным id подписки
        return new PaymentResult(true, 'stripe-mock', 'sub_mock_' . uniqid());
    }
}
