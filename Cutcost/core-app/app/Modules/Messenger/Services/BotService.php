<?php

namespace App\Modules\Messenger\Services;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Partner\Models\Company;
use Illuminate\Support\Facades\Cache;

final class BotService
{
    public function handleReplyMessage(Chat $chat, Company $company): void
    {
        $company->loadMissing('bot');

        if (!$company->bot) return;


        $messageEvent = data_get($company->bot->builder, 'reply_message');

        $this->handleBotMessage($chat, $company, $messageEvent);
    }

    public function handleFirstMessage(Chat $chat, Company $company): void
    {
        $company->loadMissing('bot');

        if (!$company->bot) return;

        $messageEvent = data_get($company->bot->builder, 'chat_created_message');

        $this->handleBotMessage($chat, $company, $messageEvent);
    }

    private function handleBotMessage(Chat $chat, Company $company, string $message): void
    {
        if (!$message) return;

        if (Cache::has('bot_reply_message_' . $chat->id)) return;

        Cache::forever('bot_reply_message_' . $chat->id, true);

        app(MessageService::class)->send($chat, $company->user, [
            'content' => $message,
            'type' => 'text',
        ], null);
    }
}
