<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Services;

use App\Modules\Messenger\Enums\ChatTypes;
use App\Modules\Messenger\Events\ChatBlocked;
use App\Modules\Messenger\Http\Resources\ChatResource;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatRepository;
use App\Modules\Messenger\Repositories\ChatsRepository;
use App\Modules\Messenger\Repositories\MessageRepository;
use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

final class ChatService
{

    private const MESSAGES_PER_PAGE = 20;

    public function getTransformedChatMessages(
        Chat $chat,
        int|bool $cursor,
        MessageRepository $messageRepository,
    ): Collection {

        $messages = $messageRepository
            ->getMessages($chat, self::MESSAGES_PER_PAGE, $cursor)
            ->get()->sortBy('id')->values();

        return $messages;
    }

    public function getTransformedChatData(
        User $user,
        ChatRepository $chatRepository,
        string $type,
        string $key
    ): ?ChatResource {

        $chat = null;
        $friend = null;
        $company = null;

        $chatType = null;

        // try to find chat by type

        switch ($type) {
            // for chat or notes | message to user
            case 'nickname':
                if ($key === $user->nickname) {

                    $chat = $chatRepository->getNotesChat($user);
                    $friend = $user;
                    $chatType = ChatTypes::NOTES;
                } else {
                    [$chat, $friend] = $chatRepository->getChatByUserNickname($user, $key);
                    $chatType = ChatTypes::CHAT;
                }
                break;
            // for with company chat
            case 'slug':
                $chatType = ChatTypes::CHAT;
                [$chat, $company] = $chatRepository->getChatByCompanySlug($user, $key);

                // if company is mine then notes

                if ($company->user_id === $user->id) {

                    $chat = $chatRepository->getNotesChat($user);
                    $friend = $user;
                    $company = null;
                    $chatType = ChatTypes::NOTES;
                }

                break;
            // for group chat and chat inside messenger navigation
            case 'id':

                // NOT SET chatType, cause it can be any type
                [$chat, $friend, $company] = $chatRepository->getChatById($user, $key);


                if ($chat->type === ChatTypes::NOTES->value) {
                    $chatType = ChatTypes::NOTES;
                    break;
                }

                // if no friend and no company, then group
                if (!$friend && !$company && $chat)
                    $chatType = ChatTypes::GROUP;

                break;
            default:
                abort(404);
        }

        if (!$chat) {
            // it can't be possible to be group or notes chat
            // if it's not found
            $chat = $this->createChat($user, $chat, $friend, $company, $chatType);

            if (!$chat) abort(404);
        }

        // if user quit the chat

        $isUserInChat = $this->isUserInChat($user, $chat);
        $isUserInChatButQuit = $this->isUserInChatButQuit($user, $chat);

        if ($isUserInChatButQuit) {
            // пользователь был, но вышел — возвращаем
            $chatRepository->joinBackChat($user, $chat);

            // for gropup
        } elseif ($chatType === ChatTypes::GROUP && !$isUserInChat) {
            // группа, и пользователь никогда не участвовал
            // Log::info([$chat, $chat->id]);
            if ($chat->is_public) {
                $chatRepository->joinChat($user, $chat);

                // send notification as message
                app(MessageService::class)->send($chat, $user, [
                    'content' => 'joined_the_chat',
                    'type' => 'notification',
                ], null);
            } else {
                abort(403); // приватная группа, нет доступа
            }
        } elseif (!$isUserInChat) {
            // в остальных типах (chat, notes) пользователь не в чате — отказ
            abort(403);
        }

        return new ChatResource(
            $chat,
            $user,
            $friend,
            $company
        );
    }


    private function createChat(
        User $user,
        ?Chat $chat,
        ?User $friend,
        ?Company $company,
        ChatTypes $chatType
    ) {

        $chatsRepository = app(ChatsRepository::class);

        // creation for company chat
        if ($chatType === ChatTypes::CHAT && $company) {

            $chat = $chatsRepository->createChat(
                $user,
                collect([$user->id, $company->user_id]),
                $chatType,
                [
                    'company_id' => $company->id,
                ]
            );

            app(BotService::class)->handleFirstMessage($chat, $company);

            return $chat;
        }

        if ($chatType === ChatTypes::CHAT && $friend) {

            // todo check for friend profile privacy

            return $chatsRepository->createChat(
                $user,
                collect([$user->id, $friend->id]),
                $chatType
            );
        }
    }


    // check if user in the chat
    public function isUserInChat(User $user, Chat $chat): bool
    {
        return $user->chats()
            ->where('chat_id', $chat->id)
            ->wherePivotNull('left_at')
            ->exists();
    }

    public function isUserInChatButQuit(User $user, Chat $chat): bool
    {
        return $user->chats()
            ->where('chat_id', $chat->id)
            ->wherePivotNotNull('left_at')
            ->exists();
    }

    public function quitChat(User $user, Chat $chat, ChatRepository $chatRepository): void
    {
        $chatRepository->softQuitChat($user, $chat);

        // if no one in the chat delete it
        if ($chat->users()->wherePivotNull('left_at')->count() === 0)
            $chat->delete();
    }

    public function blockChat(User $user, Chat $chat): void
    {
        $chat->update([
            'blocked_by' => $user->id,
        ]);

        ChatBlocked::dispatch($chat, $user->id);
    }

    public function unBlockChat(Chat $chat): void
    {
        $chat->update([
            'blocked_by' => null,
        ]);

        ChatBlocked::dispatch($chat, null);
    }
}
