<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Services;

use App\Modules\Messenger\Enums\ChatTypes;
use App\Modules\Messenger\Http\Resources\ChatResource;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatsRepository;
use App\Shared\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class ChatsService
{
    /**
     * check if notes chat exists, else create
     * ensure that notes chat is always exists
     */
    public function checkNotes(User $user, ChatsRepository $chatsRepository): void
    {
        $notes = Cache::rememberForever('notes_' . $user->id, function () use ($user, $chatsRepository) {
            $user->loadMissing('chats');
            return $chatsRepository->getNotes($user)->first();
        });

        if (!$notes) {
            $chatsRepository->createChat($user, collect($user->id), ChatTypes::NOTES);
        }
    }

    public function getTransformedChatsData(User $user, array $filters, ChatsRepository $chatsRepository): Collection
    {
        // 
        // Log::info([$user->companies->pluck('id')]);
        // 

        $chats = $chatsRepository->getChats($user, $filters);

        return $chats->map(function ($chat) use ($user) {
            $company = $chat->relationLoaded('company') ? $chat->company : null;

            return new ChatResource($chat, $user, $this->getFriend($chat, $user), $company);
        });
    }

    private function getFriend(Chat $chat, User $user): ?User
    {
        // except chat with company and groups
        if ($chat->type !== ChatTypes::CHAT->value || $chat->company_id)
            return null;

        return $chat->users->first(fn($u) => $u->id !== $user->id);
    }
}
