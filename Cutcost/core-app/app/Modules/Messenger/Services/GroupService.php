<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Services;

use App\Modules\Messenger\Enums\ChatTypes;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Repositories\ChatsRepository;
use App\Shared\Models\User;

final class GroupService
{
    public function createGroup(
        User $user,
        array $validated,
        ChatsRepository $chatsRepository,
    ): Chat {

        $group = $chatsRepository->createChat(
            $user,
            collect([$user->id]),
            ChatTypes::GROUP,
            $validated
        );

        return $group;
    }
}
