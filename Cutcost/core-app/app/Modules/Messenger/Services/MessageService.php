<?php

namespace App\Modules\Messenger\Services;

use App\Modules\Messenger\Events\MessageDeleted;
use App\Modules\Messenger\Events\MessageSent;
use App\Modules\Messenger\Events\MessagesSeen;
use App\Modules\Messenger\Events\MessageUpdated;
use App\Modules\Messenger\Http\Resources\MessageResource;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Models\Message;
use App\Shared\Models\User;
use App\Shared\Services\MediaService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

final class MessageService
{
    public function send(
        Chat $chat,
        User $user,
        array $validated,
        ?MediaService $mediaService,
    ): void {

        // авторизованный пользователь

        // :todo encryption
        // if (self::isNotes($chat))
        //     $content = SecurityService::encryptMessage((string) $content, $user->id);

        $message = $chat->messages()->create([
            ...$validated,
            'user_id' => $user->id,
        ]);

        if ($mediaService)
            $mediaService->storeRequest(
                $validated['upload'] ?? [],
                Message::class,
                $message->id
            );

        $message->loadMissing('user:id,avatar,nickname,display_name');

        $message->load('media');

        $message = new MessageResource($message)->toArray(request());

        MessageSent::dispatch(
            $chat,
            $message
        );


        if ($chat?->company_id) {
            app(BotService::class)->handleReplyMessage($chat, $chat->company);
        }
    }

    public function update(
        Message $message,
        Chat $chat,
        array $validated,
        MediaService $mediaService,
    ): void {

        $message->update([...$validated, 'edited' => true]);

        $mediaService->updateRequest(
            $validated['upload'],
            Message::class,
            $message->id
        );

        $message->loadMissing('user');
        $message->load('media');

        $message = new MessageResource($message)->toArray(request());

        MessageUpdated::dispatch(
            $chat,
            $message
        );
    }

    public function destroy(
        Message $message,
        Chat $chat,
    ): void {

        $message->delete();
        $priorMessage = $chat->messages()->latest()->first();

        MessageDeleted::dispatch(
            $chat,
            [
                'message_id' => $message->id,
                'prior_message' => isset($priorMessage) ?
                    (new MessageResource($priorMessage)->toArray(request())) : null,
            ]
        );
    }

    public function read(Chat $chat, User $user, Collection $messages): Collection
    {

        // get unread messages ids

        $ids = $messages->filter(function ($m) use ($user) {
            return $m->status === 'unread' && $m->user?->id !== $user->id;
        })->pluck('id')->toArray();

        // :TODO check if messages.id is indexed

        Message::whereIn('id', $ids)
            ->where('status', '!=', 'seen')
            ->update(['status' => 'seen']);


        // if no messages to read, return original
        if (!count($ids)) return $messages;


        MessagesSeen::dispatch($chat, $ids);
        // return transformed and read friend's messages  
        return $messages->map(function ($m) use ($ids) {
            if (in_array($m->id, $ids) && $m->status === 'unread') {
                $m->status = 'seen';
            }
            return $m;
        });
    }
}
