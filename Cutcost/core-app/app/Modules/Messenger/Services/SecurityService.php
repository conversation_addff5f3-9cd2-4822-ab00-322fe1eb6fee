<?php

declare(strict_types=1);

namespace App\Modules\Messenger\Services;

use Illuminate\Support\Facades\Log;

final class SecurityService
{

    //Hash messages
    // todo optimize and implement

    public static function encryptMessage(string $message, int $id): string
    {
        $secretKey = sodium_crypto_generichash((string) $id, "", SODIUM_CRYPTO_SECRETBOX_KEYBYTES);
        $nonce = random_bytes(SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
        $encryptedMessage = sodium_crypto_secretbox($message, $nonce, $secretKey);

        return base64_encode($nonce . $encryptedMessage);
    }

    public static function decryptMessage(string $encryptedMessage, int $id)
    {
        $secretKey = sodium_crypto_generichash((string) $id, "", SODIUM_CRYPTO_SECRETBOX_KEYBYTES);

        $decoded = base64_decode($encryptedMessage);

        $nonce = substr($decoded, 0, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
        $ciphertext = substr($decoded, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);

        $decryptedMessage = sodium_crypto_secretbox_open($ciphertext, $nonce, $secretKey);

        if ($decryptedMessage === false) {
            // Log::critical("Security alert: Decryption failed. User - $id");
            throw new \Exception('Decryption failed');
        }

        return $decryptedMessage;
    }
}
