<?php

namespace App\Modules\Partner\Contracts;

use App\Modules\Partner\Models\Billing\Plan;
use App\Shared\Models\User;

class PaymentResult
{
    public function __construct(
        public bool $success,
        public string $provider,
        public ?string $subscription_id = null,
    ) {}
}

interface PaymentContract
{
    public function createSubscription(User $user, Plan $plan): PaymentResult;
}
