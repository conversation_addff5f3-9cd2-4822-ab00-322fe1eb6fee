<?php

namespace App\Modules\Partner\Exceptions;

use Exception;

class CouponValidationException extends Exception
{
    protected string $customCode;

    public function __construct(string $message = "", string $customCode = 'default', int $code = 0)
    {
        parent::__construct($message, $code);
        $this->customCode = $customCode;
    }

    public function getCustomCode(): string
    {
        return $this->customCode;
    }
}
