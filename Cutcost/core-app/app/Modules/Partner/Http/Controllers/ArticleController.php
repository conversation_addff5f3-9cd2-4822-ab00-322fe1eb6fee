<?php

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Http\Requests\ArticleRequest;
use App\Modules\Partner\Models\Article;
use App\Modules\Partner\Models\Company;
use App\Shared\Services\MediaService;
use App\Shared\Services\Pagination\PaginatorService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ArticleController
{

    public function index(Company $company)
    {
        $articles = PaginatorService::fromQuery($company->articles())
            ->transformWith(fn($article) => [
                'slug' => $article->slug,
                'name' => $article->name,
                'views' => $article->views,
            ])
            ->search(['name', 'content'])
            ->paginate(10);

        return Inertia::render('Articles/ShowPartnerArticles', [
            'articles' => $articles,
            'company' => $company,
        ]);
    }

    public function create(Company $company)
    {

        return Inertia::render('Articles/CreateArticle', [
            'company' => $company,
        ]);
    }

    public function store(
        ArticleRequest $request,
        MediaService $mediaService,
        Company $company
    ): RedirectResponse {
        $validated = $request->safe()->all();

        try {
            $article = $company->articles()->create([
                ...$validated,
                'slug' => Str::uuid()->toString(),
            ]);

            $mediaService->storeRequest(
                $validated['upload'],
                Article::class,
                $article->id
            );

            return to_route('partner.articles.edit', [
                'article' => $article->slug,
                'company' => $company->slug
            ])
                ->with('success', __("app.flash.created", ['subject' => __('app.helpers.article')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.article')]));
        }
    }

    public function edit(Company $company, Article $article)
    {
        Gate::authorize('update', $article);

        return Inertia::render('Articles/EditArticle', [
            'article' => $article,
            'company' => $company,
        ]);
    }

    public function update(
        ArticleRequest $request,
        Article $article,
        MediaService $mediaService
    ): RedirectResponse {
        Gate::authorize('update', $article);

        $validated = $request->safe()->all();

        try {
            $article->update($validated);

            $mediaService->storeRequest(
                $validated['upload'],
                Article::class,
                $article->id
            );

            return back()
                ->with('success', __("app.flash.updated", ['subject' => __('app.helpers.article')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.article')]));
        }
    }

    public function destroy(Article $article): RedirectResponse
    {
        Gate::authorize('update', $article);

        try {
            $article->delete();
            return back()->with('success', __("app.flash.deleted", ['subject' => __('app.helpers.article')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.article')]));
        }
    }
}
