<?php

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Services\RolesRestriction;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BotController
{
    public function index(Request $request, Company $company)
    {
        new RolesRestriction('assistant', $company->user);
        
        return Inertia::render('Bot/ShowBot', [
            'company' => $company,
            'bot' => $this->getBot($company),
        ]);
    }

    public function update(Request $request, Company $company)
    {
        new RolesRestriction('assistant', $company->user);

        $validated = $request->validate([
            'chat_created_message' => 'nullable|string|between:1,512',
            'reply_message' => 'nullable|string|between:1,512',
        ]);

        $bot = $this->getBot($company);
        $bot->builder = $validated;
        $bot->save();

        return back()->with('success', __('app.flash.updated', ['subject' => 'helpers.assistant']));
    }

    private function getBot(Company $company)
    {
        return $company->bot()->firstOrCreate([
            'builder' => [],
        ]);
    }
}
