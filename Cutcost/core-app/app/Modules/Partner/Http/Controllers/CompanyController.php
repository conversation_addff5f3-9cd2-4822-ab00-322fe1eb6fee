<?php

declare(strict_types=1);

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Http\Requests\CompanyRequest;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Repositories\PartnerRepository;
use App\Modules\Partner\Services\CompanyService;
use App\Modules\Partner\Services\RolesRestriction;
use App\Shared\Models\Country;
use App\Shared\Services\TranslateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Illuminate\Support\Str;

// todo make service
class CompanyController
{
    public function create(
        CompanyService $companyService,
        PartnerRepository $partnerRepository,
        Request $request
    ) {
        new RolesRestriction('company', $request->user());

        return Inertia::render(
            'Company/CreateCompany',
            $companyService->create($partnerRepository)
        );
    }

    public function store(CompanyRequest $request, TranslateService $translateService)
    {
        $validated = $request->safe()->all();

        new RolesRestriction('company', $request->user());

        try {

            $user = $request->user();

            isset($validated['country_id'])
                ?: $validated['country_id'] = Country::where('name', 'Worldwide')->first()->id;

            $validated['slug'] = Str::uuid()->toString();
            $validated['user_id'] = $request->user()->id;

            DB::transaction(function () use ($user, $validated, $translateService) {
                $company = $user->companies()->create([...$validated,
                'base_locale' => $translateService->detect($validated['description'] ?? ''),
            ]);
                # @var int $validated['industries'] 
                $company->industries()->sync([$validated['industries']]);
            });

            return to_route('partner.companies.edit', ['company' => $validated['slug']])
                ->with('success', __("app.flash.created", ['subject' => __('app.helpers.company')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.company')]));
        }
    }

    public function edit(Company $company, CompanyService $companyService, PartnerRepository $partnerRepository)
    {
        Gate::authorize('update', $company);

        return Inertia::render(
            'Company/EditCompany',
            $companyService->edit($company, $partnerRepository),
        );
    }

    public function update(CompanyRequest $request, Company $company)
    {
        $validated = $request->safe()->all();
        Gate::authorize('update', $company);

        try {

            isset($validated['country_id'])
                ?: $validated['country_id'] = Country::where('name', 'Worldwide')->first()->id;

            DB::transaction(function () use ($company, $validated) {
                $company->industries()->sync($validated['industries']);
                $company->update($validated);
            });

            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.company')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.company')]));
        }
    }

    public function destroy(Company $company)
    {
        Gate::authorize('update', $company);
        try {
            $company->delete();
            return back()->with('success', __("app.flash.deleted", ['subject' => __('app.helpers.company')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.company')]));
        }
    }
}
