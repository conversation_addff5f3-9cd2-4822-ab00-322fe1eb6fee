<?php

declare(strict_types=1);

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Enums\CouponTypesEnum;
use App\Modules\Partner\Exceptions\CouponValidationException;
use App\Modules\Partner\Http\Requests\CouponRequest;
use App\Modules\Partner\Http\Resources\CompanyResource;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Services\CouponService;
use App\Modules\Partner\Services\RolesRestriction;
use App\Shared\Repositories\CacheRepository;
use App\Shared\Services\MediaService;
use App\Shared\Services\TranslateService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class CouponController
{

    public function create(Request $request, Company $company)
    {
        Gate::authorize('update', $company);
        new RolesRestriction('coupon', $request->user());

        return Inertia::render('Coupon/CreateCoupon', [
            'company' => new CompanyResource($company->loadMissing(['currency'])),
        ]);
    }

    public function edit(Coupon $coupon, Company $company, CouponService $couponService)
    {
        Gate::authorize('update', $company);

        try {
            return Inertia::render('Coupon/EditCoupon', [
                ...$couponService->edit($coupon),
                'company' => new CompanyResource($company->loadMissing(['currency'])),
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return to_route('company.show', ['slug' => $company->slug])
                ->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.coupon')]));
        }
    }

    public function store(
        Company $company,
        CouponRequest $request,
        MediaService $mediaService,
        CacheRepository $cacheRepo,
        TranslateService $translateService
    ): RedirectResponse {
        try {

            new RolesRestriction('coupon', $request->user());

            $validated = $request->safe()->all();

            $validated['slug'] = Str::uuid()->toString();
            $validated['user_id'] = $company->user_id;

            $validated['base_locale'] = $translateService->detect([
                $validated['description'] ?? '',
                $validated['custom_conditions'] ?? '',
                $validated['benefits'] ?? ''
            ]);

            $coupon = $company->coupons()->create($validated);

            $mediaService->storeRequest(
                $validated['upload'],
                Coupon::class,
                $coupon->id
            );

            $cacheRepo->forgetCompanyCouponsCount($company->id);

            return to_route('partner.coupons.edit', [
                'coupon' => $coupon->slug,
                'company' => $company->slug
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.coupon')]));
        }
    }

    public function update(Coupon $coupon, CouponRequest $request, MediaService $mediaService): RedirectResponse
    {
        Gate::authorize('update', $coupon);

        $validated = $request->safe()->all();

        if ($coupon->type->value !== $validated['type'])
            abort(403);

        try {

            $coupon->update($validated);

            $mediaService->updateRequest(
                $validated['upload'],
                Coupon::class,
                $coupon->id
            );

            $coupon->save();

            return back()->with('success', __(
                "app.flash.updated",
                ['subject' => __('app.helpers.coupon')]
            ));
        } catch (CouponValidationException $e) {
            return back()->with('error', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.coupon')]));
        }
    }

    public function destroy(Coupon $coupon, CacheRepository $cacheRepo): void
    {
        Gate::authorize('forceDelete', $coupon);
        try {
            $coupon->delete();
            $cacheRepo->forgetCompanyCouponsCount($coupon->company->id);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }

    public function updateCouponLocations(Coupon $coupon, Request $request)
    {
        Gate::authorize('update', $coupon);


        $validated = $request->validate([
            'locations' => [
                Rule::requiredIf(!$coupon->is_draft
                    && $coupon->type !== CouponTypesEnum::ONLINE),
                "array"
            ],
            'locations.*' => ['exists:locations,id'],
        ]);

        try {
            // @array of location ids
            $coupon->locations()->sync($validated['locations']);

            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.locations')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.locations')]));
        }
    }
}
