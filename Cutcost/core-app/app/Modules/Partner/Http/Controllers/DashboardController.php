<?php

namespace App\Modules\Partner\Http\Controllers;

use App\Shared\Repositories\CacheRepository;
use App\Shared\Services\ReferralService;
use App\Shared\Models\CutsTransaction;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController
{
    public function business(CacheRepository $cacheRepository): Response|RedirectResponse
    {
        $user = Auth::user();

        return Inertia::render('Dashboard/ShowBusiness', [
            'companies_count' => $cacheRepository->companyCount($user),
        ]);
    }

    public function referral(ReferralService $referralService): Response|RedirectResponse
    {
        $user = Auth::user();

        // Ensure user has a referral code
        if (!$user->referral_code) {
            $user->generateReferralCode();
        }

        // Get comprehensive referral statistics
        $stats = $referralService->getReferralStatistics($user);

        // Get recent referrals with user details
        $recentReferrals = $user->referrals()
            ->with(['user:id,name,email,avatar,created_at'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($referral) {
                return [
                    'id' => $referral->id,
                    'user' => [
                        'id' => $referral->user->id,
                        'name' => $referral->user->name,
                        'email' => $referral->user->email,
                        'avatar' => $referral->user->avatar,
                        'joined_at' => $referral->registered_at,
                    ],
                    'status' => [
                        'verified' => $referral->is_verified,
                        'first_coupon_used' => $referral->first_coupon_used,
                        'registration_reward' => $referral->registration_reward_given,
                        'verification_reward' => $referral->verification_reward_given,
                        'first_action_reward' => $referral->first_action_reward_given,
                    ],
                    'registered_at' => $referral->registered_at,
                    'verified_at' => $referral->verified_at,
                    'first_coupon_used_at' => $referral->first_coupon_used_at,
                ];
            });

        // Get recent transactions
        $recentTransactions = $user->cutsTransactions()
            ->with(['sourceUser:id,name,avatar'])
            ->where('status', CutsTransaction::STATUS_COMPLETED)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'type' => $transaction->type,
                    'level' => $transaction->level,
                    'description' => $transaction->getFormattedDescription(),
                    'source_user' => $transaction->sourceUser ? [
                        'id' => $transaction->sourceUser->id,
                        'name' => $transaction->sourceUser->name,
                        'avatar' => $transaction->sourceUser->avatar,
                    ] : null,
                    'created_at' => $transaction->created_at,
                    'metadata' => $transaction->metadata,
                ];
            });

        // Get monthly earnings data for chart
        $monthlyEarnings = $user->cutsTransactions()
            ->where('status', CutsTransaction::STATUS_COMPLETED)
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, SUM(amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('total', 'month')
            ->toArray();

        // Get referral chain details
        $referralChain = $referralService->getReferralChainDetails($user);

        return Inertia::render('Dashboard/ShowReferral', [
            'referral_code' => $user->referral_code,
            'referral_url' => $user->getReferralUrl(),
            'stats' => $stats,
            'recent_referrals' => $recentReferrals,
            'recent_transactions' => $recentTransactions,
            'monthly_earnings' => $monthlyEarnings,
            'referral_chain' => $referralChain,
            'user_cuts_balance' => $user->cuts,
        ]);
    }
}
