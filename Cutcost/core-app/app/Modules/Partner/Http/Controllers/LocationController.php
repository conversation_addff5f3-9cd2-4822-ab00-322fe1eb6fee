<?php

declare(strict_types=1);

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Http\Requests\LocationRequest;
use App\Modules\Partner\Http\Resources\CouponResource;
use App\Modules\Partner\Http\Resources\LocationResource;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Location;
use App\Modules\Partner\Services\LocationService;
use App\Modules\Partner\Services\RolesRestriction;
use App\Modules\Public\Services\InteractionService;
use App\Shared\Services\MediaService;
use App\Shared\Services\TranslateService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Inertia\Response;
use Illuminate\Support\Str;

class LocationController
{
    public function show(string $locationSlug, InteractionService $interactionService, Request $request)
    {
        $location = Location::where('slug', $locationSlug)->withRelations()?->first();

        $status = null;
        $coupons = null;
        $company = null;

        if (!$location) {
            $status = 404;
        } elseif ($location->user->is_banned) {
            $status = 403;
        } else {
            $interactionService->incrementViews($location, $request->ip());
            $coupons = $location->coupons()->withRelations()->get();
            $company = Company::where('id', $location->company_id)
                ->withMiniRelations()->first();
        }

        return Inertia::render('Discount/ShowLocation', [
            'location' => $status ? null : new LocationResource($location),
            'coupons' => $status ? null : CouponResource::collection($coupons),
            'company' => $status ? null : $company,
            'status' => $status,
        ]);
    }

    public function create(
        Request $request,
        Company $company,
        LocationService $locationService
    ): Response {

        new RolesRestriction('location', $request->user());

        return Inertia::render(
            'Location/CreateLocation',
            $locationService->create($company)
        );
    }

    public function updateCoupons(Request $request, Location $location)
    {
        $validated = $request->validate([
            'coupons' => 'nullable|array',
            'coupons.*' => 'exists:coupons,id',
        ]);
        try {
            $location->coupons()->sync($validated['coupons']);

            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.coupons')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.coupons')]));
        }
    }

    public function store(
        Company $company,
        LocationRequest $request,
        MediaService $mediaService,
        TranslateService $translateService
    ): RedirectResponse {

        $validated = $request->safe()->all();

        new RolesRestriction('location', $request->user());

        try {

            $validated['slug'] = Str::uuid()->toString();
            $validated['user_id'] = $company->user_id;
            $validated['base_locale'] = $translateService->detect($validated['description'] ?? '');

            $location = $company->locations()->create($validated);

            $mediaService->storeRequest(
                $validated['upload'],
                Location::class,
                $location->id
            );

            return to_route('partner.locations.edit', [
                'location' => $validated['slug'],
                'company' => $company->slug
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.location')]));
        }
    }

    public function edit(
        Location $location,
        Company $company,
        LocationService $locationService,
    ): Response {

        Gate::authorize('update', $location);

        return Inertia::render(
            'Location/EditLocation',
            $locationService->edit($company, $location),
        );
    }

    /**
     * Update location
     * @param UpdateLocationRequest $request
     * @param string $slug
     * 
     * @return [type]
     */
    public function update(
        LocationRequest $request,
        Location $location,
        LocationService $locationService,
        MediaService $mediaService
    ): RedirectResponse {
        Gate::authorize('update', $location);

        $validated = $request->safe()->all();

        try {

            if ($location->type->value !== $validated['type'])
                abort(403);

            $locationService
                ->update($location, $validated, $mediaService);

            Cache::forget('used_cities');

            return back()
                ->with('success', __("app.flash.updated", ['subject' => __('app.helpers.location')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.location')]));
        }
    }

    public function updateSettings(Request $request, Location $location): RedirectResponse
    {
        Gate::authorize('update', $location);

        $validated = $request->validate([
            'is_open' => 'nullable|boolean',
        ]);
        try {
            if (count($location->media ?? []) === 0)
                return back()->with('info', __("app.dont_forget_images"));

            $location->update($validated);

            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.settings')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.settings')]));
        }
    }

    public function destroy(Location $location)
    {
        Gate::authorize('delete', $location);
        try {
            // detach related coupons 
            $location->coupons()->detach();
            $location->delete();
        } catch (\Exception $e) {
            Log::erorr($e->getMessage());
        }
    }
}
