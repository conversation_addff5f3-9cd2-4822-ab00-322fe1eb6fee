<?php

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Contracts\PaymentContract;
use App\Modules\Partner\Models\Billing\PartnerApplication;
use App\Modules\Partner\Models\Billing\Plan;
use App\Modules\Partner\Models\Billing\Subscription;
use App\Modules\Partner\Services\PartnerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class PartnerController
{
    protected PartnerService $partnerService;
    protected PaymentContract $paymentGateway;

    public function __construct(PartnerService $partnerService, PaymentContract $paymentGateway)
    {
        $this->partnerService = $partnerService;
        $this->paymentGateway = $paymentGateway;
    }

    /**
     * Отображение статуса партнёрства
     */
    public function index()
    {

        $user = Auth::user();

        if ($user->hasRole('partner')) {
            return to_route('seller.companies.index');
        }

        $application = PartnerApplication::where('user_id', $user->id)->latest()->first();
        $subscription = Subscription::where('user_id', $user->id)->latest()->first();
        // $plans = Plan::where('is_active', true)->get(['id', 'name', 'price', 'interval']);

        return Inertia::render('Seller/Partner/BecomePartner', [
            'application_status' => $application?->status,
            'subscription_status' => $subscription?->status,
        ]);
    }

    public function apply(Request $request)
    {
        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'phone' => ['required', 'string', 'max:20', 'regex:/^\+[\d\s\-().]{6,}$/'],
            'email' => 'required|email|max:255',
            'country' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:255',
        ]);

        $user = $request->user();

        $this->partnerService->createPartner($user, $validated);

        PartnerApplication::create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        return back()
            ->with('success', __('app.application_given'));
    }

    public function subscribe(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
            'full_name' => 'required|string|max:255',
        ]);

        $user = Auth::user();
        $plan = Plan::findOrFail($request->plan_id);

        // Создаем подписку через провайдер
        $paymentResult = $this->paymentGateway->createSubscription($user, $plan);

        if (!$paymentResult->success) {
            return response()->json(['error' => 'Не удалось оформить подписку'], 422);
        }

        // Сохраняем подписку
        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'provider' => $paymentResult->provider,
            'subscription_id' => $paymentResult->subscription_id,
            'status' => 'active',
            'starts_at' => now(),
            'renews_at' => now()->addMonth(), // или по plan.interval
        ]);

        // Сразу создаем партнера
        $this->partnerService->createPartner($user, $request->full_name);

        return response()->json(['message' => 'Вы стали партнёром!']);
    }
}
