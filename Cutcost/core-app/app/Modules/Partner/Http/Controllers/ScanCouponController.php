<?php

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Partner\Http\Resources\CompanyResource;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Services\ReferralService;
use App\Modules\Partner\Services\RolesRestriction;
use App\Modules\Partner\Services\ScanService;
use App\Modules\Partner\Traits\ReferralTrait;
use App\Shared\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class ScanCouponController
{
    use ReferralTrait;

    public function index(Company $company): Response
    {
        new RolesRestriction('scan', $company->user_id);
        return Inertia::render('Scan/ScanCoupon', [
            'company' => new CompanyResource($company),
        ]);
    }

    public function check(
        Request $request,
        Company $company,
    ) {
        Gate::authorize('update', $company);

        $validated = $request->validate([
            'qr' => ['required', 'string'],
        ]);

        try {
            $qr = $this->decryptQr($validated['qr']);

            $coupon = Coupon::findOrFail(data_get($qr, 'coupon.coupon_id'));
            Gate::authorize('update', $coupon);

            $user = User::findOrFail(data_get($qr, 'coupon.user_id'));

            // Определяем, есть ли ошибки условий
            $builderErrors = array_filter($data['builder'] ?? []);
            $hasBuilderErrors = !empty($builderErrors);
            $data  = [];

            return response()->json($data);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('app.scan.coupon_not_found')
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => __('app.scan.something_went_wrong')
            ]);
        }
    }

    public function accept(
        Request $request,
        Company $company,
        ScanService $scanService,
        ReferralService $referralService,
    ) {
        Gate::authorize('update', $company);

        $validated = $request->validate([
            'qr' => ['required', 'string'],
        ]);

        try {
            $qr = $this->decryptQr($validated['qr']);

            $coupon = Coupon::findOrFail(data_get($qr, 'coupon.coupon_id'));
            $user = User::findOrFail(data_get($qr, 'coupon.user_id'));

            $data = [];

            // Определяем, есть ли ошибки в условиях
            $builderErrors = array_filter($data['builder'] ?? []);
            $hasErrors = $data['status'] === 'error' || !empty($builderErrors);

            if ($hasErrors) {
                $messages = [$data['message']];
                if (!empty($builderErrors)) {
                    $messages[] = implode(', ', $builderErrors);
                }
                return response()->json([
                    'status' => 'error',
                    'message' => implode(' | ', array_filter($messages))
                ]);
            }

            $earnings = $this->earnings($coupon->price_in_store, $coupon->discount);

            // Запись истории сканирования
            $company->scans()->create([
                'user_id' => $user->id,
                'coupon_id' => $coupon->id,
                'coupon_type' => 'free',
                'earnings' => $earnings,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => __('app.scan.scanned')
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('app.scan.coupon_not_found')
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => __('app.scan.something_went_wrong')
            ]);
        }
    }
}
