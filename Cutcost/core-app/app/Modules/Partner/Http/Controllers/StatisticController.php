<?php

namespace App\Modules\Partner\Http\Controllers;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Models\Message;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Scan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

final class StatisticController
{
    public function index(Request $request): Response
    {
        $user = Auth::user();
        $dateRange = $request->get('range', '30'); // Default to 30 days
        $companyId = $request->get('company_id');

        // Get user's companies
        $companies = $user->companies()->select('id', 'name', 'slug', 'avatar')->get();

        // Build base query for company filtering
        $companyQuery = $companyId ?
            $companies->where('id', $companyId) :
            $companies;

        $companyIds = $companyQuery->pluck('id')->toArray();

        // Calculate date range
        $startDate = $this->getStartDate($dateRange);
        $endDate = now();

        // Get overview statistics
        $overviewStats = $this->getOverviewStatistics($companyIds, $startDate, $endDate);

        // Get chat engagement metrics
        $chatMetrics = $this->getChatEngagementMetrics($companyIds, $startDate, $endDate);

        // Get coupon interaction statistics
        $couponStats = $this->getCouponStatistics($companyIds, $startDate, $endDate);

        // Get time-based analytics
        $timeAnalytics = $this->getTimeBasedAnalytics($companyIds, $startDate, $endDate, $dateRange);

        // Get top performing content
        $topPerformers = $this->getTopPerformers($companyIds, $startDate, $endDate);

        // Get recent activity
        $recentActivity = $this->getRecentActivity($companyIds, 20);

        return Inertia::render('Statistic/ShowStatistic', [
            'companies' => $companies,
            'selected_company_id' => $companyId,
            'date_range' => $dateRange,
            'overview_stats' => $overviewStats,
            'chat_metrics' => $chatMetrics,
            'coupon_stats' => $couponStats,
            'time_analytics' => $timeAnalytics,
            'top_performers' => $topPerformers,
            'recent_activity' => $recentActivity,
        ]);
    }

    private function getStartDate(string $range): \Carbon\Carbon
    {
        return match($range) {
            '7' => now()->subDays(7),
            '30' => now()->subDays(30),
            '90' => now()->subDays(90),
            '365' => now()->subYear(),
            default => now()->subDays(30),
        };
    }

    private function getOverviewStatistics(array $companyIds, $startDate, $endDate): array
    {
        if (empty($companyIds)) {
            return [
                'total_chats' => 0,
                'active_conversations' => 0,
                'total_messages' => 0,
                'coupon_interactions' => 0,
                'total_earnings' => 0,
                'new_customers' => 0,
                'conversion_rate' => 0,
            ];
        }

        // Total chats initiated with companies
        $totalChats = Chat::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Active conversations (chats with messages in the period)
        $activeConversations = Chat::whereIn('company_id', $companyIds)
            ->whereHas('messages', function($q) use ($startDate, $endDate) {
                $q->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->count();

        // Total messages in company chats
        $totalMessages = Message::whereHas('chat', function($q) use ($companyIds) {
                $q->whereIn('company_id', $companyIds);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Coupon interactions (scans/redemptions through chat)
        $couponInteractions = Scan::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Total earnings from scans
        $totalEarnings = Scan::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('earnings');

        // New customers (unique users who started chats)
        $newCustomers = Chat::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('created_by')
            ->count('created_by');

        // Conversion rate (chats that led to coupon interactions)
        $conversationRate = $totalChats > 0 ?
            round(($couponInteractions / $totalChats) * 100, 2) : 0;

        return [
            'total_chats' => $totalChats,
            'active_conversations' => $activeConversations,
            'total_messages' => $totalMessages,
            'coupon_interactions' => $couponInteractions,
            'total_earnings' => round($totalEarnings, 2),
            'new_customers' => $newCustomers,
            'conversion_rate' => $conversationRate,
        ];
    }

    private function getChatEngagementMetrics(array $companyIds, $startDate, $endDate): array
    {
        if (empty($companyIds)) {
            return [
                'avg_response_time' => 0,
                'messages_per_chat' => 0,
                'active_chat_hours' => [],
                'user_engagement_rate' => 0,
            ];
        }

        // Average messages per chat
        $chatsWithMessages = Chat::whereIn('company_id', $companyIds)
            ->withCount(['messages' => function($q) use ($startDate, $endDate) {
                $q->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->having('messages_count', '>', 0)
            ->get();

        $avgMessagesPerChat = $chatsWithMessages->count() > 0 ?
            round($chatsWithMessages->avg('messages_count'), 1) : 0;

        // Peak activity hours (simplified - would need more complex logic for real response time)
        $hourlyActivity = Message::whereHas('chat', function($q) use ($companyIds) {
                $q->whereIn('company_id', $companyIds);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        // Fill missing hours with 0
        $activeChatHours = [];
        for ($i = 0; $i < 24; $i++) {
            $activeChatHours[] = [
                'hour' => $i,
                'count' => $hourlyActivity[$i] ?? 0,
            ];
        }

        // User engagement rate (users who sent multiple messages)
        $totalUsers = Chat::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('created_by')
            ->count('created_by');

        $engagedUsers = Message::whereHas('chat', function($q) use ($companyIds) {
                $q->whereIn('company_id', $companyIds);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('user_id, COUNT(*) as message_count')
            ->groupBy('user_id')
            ->having('message_count', '>', 2)
            ->count();

        $userEngagementRate = $totalUsers > 0 ?
            round(($engagedUsers / $totalUsers) * 100, 1) : 0;

        return [
            'avg_response_time' => '~5 min', // Placeholder - would need more complex calculation
            'messages_per_chat' => $avgMessagesPerChat,
            'active_chat_hours' => $activeChatHours,
            'user_engagement_rate' => $userEngagementRate,
        ];
    }

    private function getCouponStatistics(array $companyIds, $startDate, $endDate): array
    {
        if (empty($companyIds)) {
            return [
                'total_coupons' => 0,
                'active_coupons' => 0,
                'coupon_views' => 0,
                'coupon_interactions' => 0,
                'top_coupons' => [],
                'coupon_conversion_rate' => 0,
            ];
        }

        // Total coupons created by companies
        $totalCoupons = Coupon::whereIn('company_id', $companyIds)->count();

        // Active coupons (published status)
        $activeCoupons = Coupon::whereIn('company_id', $companyIds)
            ->where('status', 'published')
            ->count();

        // Coupon views (sum of views field)
        $couponViews = Coupon::whereIn('company_id', $companyIds)
            ->sum('views');

        // Coupon interactions through chat
        $couponInteractions = Scan::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Top performing coupons
        $topCoupons = Coupon::whereIn('company_id', $companyIds)
            ->withCount(['scanHistory' => function($q) use ($startDate, $endDate) {
                $q->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->orderBy('scan_history_count', 'desc')
            ->limit(5)
            ->get(['id', 'name', 'views', 'discount'])
            ->map(function($coupon) {
                return [
                    'id' => $coupon->id,
                    'name' => is_array($coupon->name) ? ($coupon->name['en'] ?? 'Unnamed') : $coupon->name,
                    'views' => $coupon->views,
                    'interactions' => $coupon->scan_history_count,
                    'discount' => $coupon->discount,
                ];
            });

        // Conversion rate (views to interactions)
        $conversionRate = $couponViews > 0 ?
            round(($couponInteractions / $couponViews) * 100, 2) : 0;

        return [
            'total_coupons' => $totalCoupons,
            'active_coupons' => $activeCoupons,
            'coupon_views' => $couponViews,
            'coupon_interactions' => $couponInteractions,
            'top_coupons' => $topCoupons,
            'coupon_conversion_rate' => $conversionRate,
        ];
    }

    private function getTimeBasedAnalytics(array $companyIds, $startDate, $endDate, string $range): array
    {
        if (empty($companyIds)) {
            return [
                'chats_over_time' => [],
                'messages_over_time' => [],
                'interactions_over_time' => [],
                'earnings_over_time' => [],
            ];
        }

        $dateFormat = match($range) {
            '7', '30' => '%Y-%m-%d',
            '90' => '%Y-%u',
            '365' => '%Y-%m',
            default => '%Y-%m-%d',
        };

        // Chats over time
        $chatsOverTime = Chat::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw("DATE_FORMAT(created_at, '{$dateFormat}') as period, COUNT(*) as count")
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->pluck('count', 'period')
            ->toArray();

        // Messages over time
        $messagesOverTime = Message::whereHas('chat', function($q) use ($companyIds) {
                $q->whereIn('company_id', $companyIds);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw("DATE_FORMAT(created_at, '{$dateFormat}') as period, COUNT(*) as count")
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->pluck('count', 'period')
            ->toArray();

        // Interactions over time
        $interactionsOverTime = Scan::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw("DATE_FORMAT(created_at, '{$dateFormat}') as period, COUNT(*) as count")
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->pluck('count', 'period')
            ->toArray();

        // Earnings over time
        $earningsOverTime = Scan::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw("DATE_FORMAT(created_at, '{$dateFormat}') as period, SUM(earnings) as total")
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->pluck('total', 'period')
            ->toArray();

        return [
            'chats_over_time' => $chatsOverTime,
            'messages_over_time' => $messagesOverTime,
            'interactions_over_time' => $interactionsOverTime,
            'earnings_over_time' => $earningsOverTime,
        ];
    }

    private function getTopPerformers(array $companyIds, $startDate, $endDate): array
    {
        if (empty($companyIds)) {
            return [
                'top_companies' => [],
                'most_active_users' => [],
                'peak_days' => [],
            ];
        }

        // Top performing companies (by interactions)
        $topCompanies = Company::whereIn('id', $companyIds)
            ->withCount(['scans' => function($q) use ($startDate, $endDate) {
                $q->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->orderBy('scans_count', 'desc')
            ->limit(5)
            ->get(['id', 'name', 'avatar'])
            ->map(function($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'avatar' => $company->avatar,
                    'interactions' => $company->scans_count,
                ];
            });

        // Most active users (by message count)
        $mostActiveUsers = Message::whereHas('chat', function($q) use ($companyIds) {
                $q->whereIn('company_id', $companyIds);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with('user:id,display_name,nickname,avatar')
            ->selectRaw('user_id, COUNT(*) as message_count')
            ->groupBy('user_id')
            ->orderBy('message_count', 'desc')
            ->limit(5)
            ->get()
            ->map(function($record) {
                return [
                    'user' => [
                        'id' => $record->user->id,
                        'name' => $record->user->display_name ?? $record->user->nickname,
                        'avatar' => $record->user->avatar,
                    ],
                    'message_count' => $record->message_count,
                ];
            });

        // Peak activity days
        $peakDays = Message::whereHas('chat', function($q) use ($companyIds) {
                $q->whereIn('company_id', $companyIds);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as activity')
            ->groupBy('date')
            ->orderBy('activity', 'desc')
            ->limit(5)
            ->get()
            ->map(function($record) {
                return [
                    'date' => $record->date,
                    'activity' => $record->activity,
                ];
            });

        return [
            'top_companies' => $topCompanies,
            'most_active_users' => $mostActiveUsers,
            'peak_days' => $peakDays,
        ];
    }

    private function getRecentActivity(array $companyIds, int $limit = 20): array
    {
        if (empty($companyIds)) {
            return [];
        }

        // Get recent chats, messages, and scans
        $recentChats = Chat::whereIn('company_id', $companyIds)
            ->with(['company:id,name,avatar', 'users:id,display_name,nickname,avatar'])
            ->latest()
            ->limit($limit / 2)
            ->get()
            ->map(function($chat) {
                $user = $chat->users->first();
                return [
                    'type' => 'chat_started',
                    'description' => 'New chat started',
                    'user' => $user ? [
                        'name' => $user->display_name ?? $user->nickname,
                        'avatar' => $user->avatar,
                    ] : null,
                    'company' => [
                        'name' => $chat->company->name,
                        'avatar' => $chat->company->avatar,
                    ],
                    'created_at' => $chat->created_at,
                ];
            });

        $recentScans = Scan::whereIn('company_id', $companyIds)
            ->with(['user:id,display_name,nickname,avatar', 'company:id,name,avatar', 'coupon:id,name'])
            ->latest()
            ->limit($limit / 2)
            ->get()
            ->map(function($scan) {
                return [
                    'type' => 'coupon_interaction',
                    'description' => 'Coupon redeemed through chat',
                    'user' => [
                        'name' => $scan->user->display_name ?? $scan->user->nickname,
                        'avatar' => $scan->user->avatar,
                    ],
                    'company' => [
                        'name' => $scan->company->name,
                        'avatar' => $scan->company->avatar,
                    ],
                    'coupon_name' => is_array($scan->coupon->name) ?
                        ($scan->coupon->name['en'] ?? 'Unnamed') : $scan->coupon->name,
                    'earnings' => $scan->earnings,
                    'created_at' => $scan->created_at,
                ];
            });

        return $recentChats->concat($recentScans)
            ->sortByDesc('created_at')
            ->take($limit)
            ->values()
            ->toArray();
    }
}
