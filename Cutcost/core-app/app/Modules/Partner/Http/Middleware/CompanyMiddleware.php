<?php

namespace App\Modules\Partner\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * [Ensures that user has created company]
 */
class CompanyMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check() && empty($request->user()->companies()->first()))
            return to_route('partner.companies.create')
                ->with('info', __('app.create_company_first'));

        return $next($request);
    }
}
