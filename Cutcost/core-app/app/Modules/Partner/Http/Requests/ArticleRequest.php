<?php

namespace App\Modules\Partner\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class ArticleRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'between:2,64'],
            'content' => ['required', 'string', 'between:4,6100'],
            'upload.temp_uuid' => 'nullable|string|uuid',
            'upload.deleted_ids' => 'nullable|array',
        ];
    }
}
