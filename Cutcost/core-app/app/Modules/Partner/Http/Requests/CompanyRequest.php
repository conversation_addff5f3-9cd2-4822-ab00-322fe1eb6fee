<?php

namespace App\Modules\Partner\Http\Requests;

use App\Shared\Rules\BusinessHours;
use Illuminate\Foundation\Http\FormRequest;

class CompanyRequest extends FormRequest
{
    public function messages(): array
    {
        return [
            'socials.*' => __('validation.custom.socials.regex'),
        ];
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:64',
            'description' => ['nullable', 'string', 'max:400'],
            'socials' => ['nullable', 'array'],
            'socials.*' => [
                'required',
                'string',
                'regex:/^(?:\+?[1-9]\d{1,14}|(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-]*)?(\?[^\s]*)?)$/i'
            ],
            'industries' => ['nullable', 'numeric'],
            'country_id' => ['nullable', 'numeric'],
            'currency_id' => ['required', 'numeric'],
            'type' => ['required', 'string', 'in:individual,organization'],
            'business_hours' => ['nullable', 'array', new BusinessHours()],
        ];
    }
}
