<?php

namespace App\Modules\Partner\Http\Requests;

use App\Modules\Partner\Enums\CouponTypesEnum;
use App\Shared\Rules\Discount;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CouponRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::enum(CouponTypesEnum::class)],
            'name' => 'required|string|max:64',
            'description' => 'required|string|max:800',
            'price_in_store' => 'required|numeric|between:0.01,10000000',
            'discount' => ['required', 'string', 'regex:/^\d+(\.\d+)?(%|€)?$/', new Discount()],
            'tokens_price' => 'nullable|numeric|between:1,1000000000',
            'tokens_reward' => 'nullable|numeric|between:1,100000000',
            'benefits' => 'nullable|string|max:1200',

            'upload.temp_uuid' => 'nullable|string|uuid',
            'upload.deleted_ids' => 'nullable|array',

            'valid_until' => 'nullable|date|after:today',
            'custom_conditions' => 'nullable|string|max:600',
            'first_time_only' => 'boolean',
            'min_order_amount' => 'nullable|numeric|min:0.01',
            'min_age' => 'nullable|numeric|min:1',
            'max_age' => 'nullable|numeric|min:1',

            'link_to_product' => ['required_without:promocode', 'nullable', 'url', 'starts_with:https://', 'between:10,255'],
            'promocode' => ['required_without:link_to_product', 'nullable', 'string', 'between:2,32'],
            'status' => ['required', Rule::in(['draft', 'published', 'out_of_stock'])],
        ];
    }
}
