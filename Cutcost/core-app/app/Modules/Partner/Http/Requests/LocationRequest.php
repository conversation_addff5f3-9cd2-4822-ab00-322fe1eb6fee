<?php

namespace App\Modules\Partner\Http\Requests;

use App\Modules\Partner\Enums\LocationTypesEnum;
use App\Shared\Rules\BusinessHours;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LocationRequest extends FormRequest
{
    public function rules(): array
    {


        return [
            'type' => ['required', Rule::enum(LocationTypesEnum::class)],
            'is_open' => ['nullable', 'boolean'],
            'name' => ['nullable', 'string', 'max:64'],
            'description' => ['nullable', 'string', 'max:400'],
            'address' => [
                Rule::requiredIf(fn() => $this->input('type') === LocationTypesEnum::OFFLINE->value),
                'string',
                'max:255',
                'nullable',
            ],
            'lnglat' => [
                Rule::requiredIf(fn() => $this->input('type') === LocationTypesEnum::OFFLINE->value),
                'nullable',
                'array',
            ],
            'business_hours' => ['nullable', 'array', new BusinessHours()],
            'city_id' => ['required', 'numeric', 'exists:cities,id'],
            'upload.temp_uuid' => 'nullable|string|uuid',
            'upload.deleted_ids' => 'nullable|array',
        ];
    }
}
