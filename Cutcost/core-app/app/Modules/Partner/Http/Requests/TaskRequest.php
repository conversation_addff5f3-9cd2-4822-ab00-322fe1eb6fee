<?php

namespace App\Modules\Partner\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TaskRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => ['required', 'in:target,general'],
            'reward_coupons' => ['required', 'array',],
            'target_coupons' => ['required_if:type,target', 'numeric',],
            'reward_interval' => ['required', 'integer', 'min:1'],
        ];
    }
}
