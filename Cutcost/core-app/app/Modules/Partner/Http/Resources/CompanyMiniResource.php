<?php

namespace App\Modules\Partner\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class CompanyMiniResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'avatar' => $this->avatar,
            'is_subscribed' => $this->is_subscribed,
            'country' => $this->whenLoaded('country'),
            'user_id' => $this->user_id,
            'currency' => $this->whenLoaded('currency'),
            'business_hours' => $this->business_hours,
        ];
    }
}
