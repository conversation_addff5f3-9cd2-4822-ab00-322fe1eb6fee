<?php

namespace App\Modules\Partner\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class CompanyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'avatar' => $this->avatar,
            'description' => $this->description,
            'cover' => $this->cover,
            'type' => $this->type,
            'user_id' => $this->user_id,
            'business_hours' => $this->business_hours,
            'industries' => $this->whenLoaded('industries'),
            'country' => $this->whenLoaded('country'),
            'currency' => $this->whenLoaded('currency'),
            'user' => $this->whenLoaded('user'),
            'is_subscribed' => $this->is_subscribed,
            'subscribers_count' => $this->subscribers_count,
            'likes_count' => $this->likes_count,
            'is_liked' => $this->is_liked,
            'base_locale' => $this->base_locale,
        ];
    }
}
