<?php

namespace App\Modules\Partner\Http\Resources;

use App\Modules\Partner\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CouponMiniResource extends JsonResource
{
    public function __construct(
        Coupon $resource,
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'name' => $this->name,
            'slug' => $this->slug,
            'price_in_store' => $this->price_in_store,
            'discount' => $this->discount,
            'status' => (bool) $this->status,
            'pivot' => $this->whenLoaded('pivot', fn() => $this->pivot),

        ];
    }
}
