<?php

namespace App\Modules\Partner\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Route;

class CouponResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $cutted = Route::currentRouteName() !== 'coupon.view';

        $desc = strlen($this->description) > 134 && $cutted ? substr($this->description, 0, 134) . '...' : $this->description;

        return [
            'id' => $this->id,
            'type' => $this->type,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $desc,
            'media' => $this->media,
            'price_in_store' => $this->price_in_store,
            'discount' => $this->discount,
            'views' => $this->views,
            'scans' => $this->scans,
            'status' => $this->status,
            'user_id' => $this->user_id,
            'created_at' => $this->created_at?->toDateTimeString(),
            'locations' => $this->whenLoaded('locations'),
            'link_to_product' => $this->link_to_product,
            'currency' => $this->whenLoaded('currency'),
            'promocode' => $this->promocode,
            'company' => $this->whenLoaded('company'),
            'distance' => $this->distance,
            'benefits' => $this->benefits,

            // conditions
            'valid_until' => $this->valid_until,
            'custom_conditions' => $this->custom_conditions,
            'first_time_only' => $this->first_time_only,
            'min_order_amount' => $this->min_order_amount,
            'min_age' => $this->min_age,

            'comments_count' => $this->comments_count,
            'is_liked' => $this->is_liked,
            'likes_count' => $this->likes_count,
            'is_bookmarked' => $this->is_bookmarked,
            'created_at' => $this->created_at->toDateTimeString(),
            'base_locale' => $this->base_locale,
        ];
    }
}
