<?php

namespace App\Modules\Partner\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LocationResource extends JsonResource
{

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'type' => $this->type,
            'is_open' => $this->is_open,
            'address' => $this->address,
            'lnglat' => $this->lnglat,
            'city_id' => $this->city_id,
            'city' => $this->whenLoaded('city', function () {
                return $this->city;
            }),
            'media' => $this->media,
            'business_hours' => $this->business_hours,
            'company' => $this->whenLoaded('company', $this->company),
            'is_bookmarked' => $this->is_bookmarked,
            'views' => $this->views ?? 0,
            'created_at' => $this->created_at->toDateTimeString(),
            'base_locale' => $this->base_locale,
        ];
    }
}
