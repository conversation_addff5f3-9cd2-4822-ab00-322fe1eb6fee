<?php

namespace App\Modules\Partner\Models\Billing;

use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partner extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'full_name',
        'email',
        'phone',
        'address',
        'country',
        'company_name',
        'notes',
        'is_active',
        'joined_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
