<?php

namespace App\Modules\Partner\Models\Billing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'features',
        'price',
        'interval',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'interval' => 'string',
        'is_active' => 'boolean',
        'name' => 'array',
        'description' => 'array',
        'features' => 'array',
    ];
}
