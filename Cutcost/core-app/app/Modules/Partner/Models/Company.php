<?php

namespace App\Modules\Partner\Models;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Partner\Enums\CompanyTypesEnum;
use App\Shared\Models\Country;
use App\Shared\Models\Currency;
use App\Shared\Models\User;
use App\Shared\Traits\Likable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Company extends Model
{
    use HasFactory, Likable;

    protected $fillable = [
        'name',
        'slug',
        'socials',
        'description',
        'avatar',
        'cover',
        'country_id',
        'base_locale',
        'business_hours',
        'currency_id',
        'type',
    ];

    protected $casts = [
        'socials' => 'array',
        'business_hours' => 'array',
        'type' => CompanyTypesEnum::class,
    ];


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bot()
    {
        return $this->hasOne(Bot::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function subscribers()
    {
        return $this->belongsToMany(User::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function articles()
    {
        return $this->hasMany(Article::class);
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    public function locations()
    {
        return $this->hasMany(Location::class);
    }

    public function scans()
    {
        return $this->hasMany(Scan::class);
    }

    public function industries()
    {
        return $this->belongsToMany(Industry::class);
    }

    public function chats()
    {
        return $this->hasMany(Chat::class);
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function scopeWithRelations($query)
    {
        return $query
            ->with([
                'industries',
                'country:id,name,code',
                'currency:currencies.id,code,currencies.name,symbol',
                'user:id,display_name,nickname,avatar,is_banned',
            ])
            ->withCount(['likes', 'subscribers'])
            ->withExists([
                'subscribers as is_subscribed' => fn($q) => $q->where('user_id', Auth::id()),
                'likes as is_liked' => fn($q) => $q->where('user_id', Auth::id()),
            ]);
    }

    public function scopeWithMiniRelations($query)
    {
        return $query->select('id', 'name', 'slug', 'avatar', 'country_id', 'currency_id')
            ->with(['country', 'currency'])
            ->withExists([
                'subscribers as is_subscribed' => fn($q) => $q->where('user_id', Auth::id()),
            ])
        ;
    }
}
