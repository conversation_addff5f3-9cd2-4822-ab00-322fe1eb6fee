<?php

namespace App\Modules\Partner\Models;

use App\Modules\Partner\Enums\CouponTypesEnum;
use App\Shared\Models\Comment;
use App\Shared\Models\Currency;
use App\Shared\Models\Media;
use App\Shared\Models\User;
use App\Shared\Traits\Bookmarkable;
use App\Shared\Traits\Likable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Coupon extends Model
{
    use HasFactory, Likable, Bookmarkable;

    protected $fillable = [
        'name',
        'description',
        'slug',

        'discount',
        'price_in_store', //real price in the store

        'tokens_price', //stars
        'tokens_reward', //online price

        'type',
        'user_id',

        'views',
        'link_to_product',
        'link_clicks',
        'promocode',
        'base_locale',

        'valid_until',
        'custom_conditions',
        'first_time_only',
        'min_order_amount',
        'min_age',
        'max_age',
        'status',
        'benefits',
    ];

    protected $casts = [
        'type' => CouponTypesEnum::class,
        'first_time_only' => 'boolean',
        'valid_until' => 'date:Y-m-d',
    ];

    protected $hidden = ['pivot'];

    public function currency()
    {
        return $this->hasOneThrough(
            Currency::class,
            Company::class,
            'id',           // Foreign key on Company table
            'id',           // Foreign key on Currency table
            'company_id',   // Local key on Coupon table
            'currency_id'   // Local key on Company table
        );
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'model')->orderBy('order_column');
    }

    public function comments()
    {
        return $this->morphMany(Comment::class, 'commentable')->whereNull('parent_id')->latest();
    }

    public function locations()
    {
        return $this->belongsToMany(Location::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function scanHistory()
    {
        return $this->hasMany(Scan::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Who has this coupon in my
    public function owners()
    {
        return $this->belongsToMany(User::class);
    }

    // Scopes 

    public function scopeActive(Builder $query)
    {
        return $query
            ->where('status', 'published');
    }

    public function scopeWithRelations(Builder $query)
    {
        return $query->with([
            'locations:id,address,lnglat',
            'currency:currencies.id,code,currencies.name,symbol',
            'company:id,slug,name',
            'media',
        ])->withCount(['likes', 'comments'])->withExists([
            'bookmarks as is_bookmarked' => fn($q) => $q->where('user_id', Auth::id()),
            'likes as is_liked' => fn($q) => $q->where('user_id', Auth::id()),
        ]);
    }
}
