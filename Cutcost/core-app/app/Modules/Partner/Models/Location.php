<?php

namespace App\Modules\Partner\Models;

use App\Modules\Partner\Enums\LocationTypesEnum;
use App\Modules\Partner\Traits\HasSpatialFields;
use App\Shared\Models\City;
use App\Shared\Models\Media;
use App\Shared\Models\User;
use App\Shared\Traits\Bookmarkable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Location extends Model
{
    use HasFactory, HasSpatialFields, Bookmarkable;

    protected $fillable = [
        'type',
        'name',
        'slug',
        'description',
        'address',
        'lnglat',
        'is_open',
        'city_id',
        'user_id',
        'business_hours',
        'base_locale',
        'views'
    ];

    protected $casts = [
        'type' => LocationTypesEnum::class,
        'is_open' => 'boolean',
        'business_hours' => 'array',
    ];

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'model')->orderBy('order_column');
    }

    public function coupons()
    {
        return $this->belongsToMany(Coupon::class);
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function scopeReal($query)
    {
        return $query->where('type', '!=', 'online');
    }

    public function scopeActive($query)
    {
        return $query->where('is_open', true);
    }

    public function scopeWithRelations($query)
    {
        return $query->with([
            'city:id,name,timezone',
            'media',
            'company:id,slug,name',
            'user:id,is_banned',
        ])->withExists([
            'bookmarks as is_bookmarked' => fn($q) => $q->where('user_id', Auth::id()),
        ]);
    }
}
