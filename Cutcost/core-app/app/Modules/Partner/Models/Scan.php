<?php

namespace App\Modules\Partner\Models;

use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Model;

class Scan extends Model
{

    protected $fillable = [
        'coupon_type',
        'earnings',
        'user_id',
        'coupon_id',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
