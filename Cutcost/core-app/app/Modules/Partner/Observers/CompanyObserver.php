<?php

namespace App\Modules\Partner\Observers;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Partner\Models\Company;
use App\Shared\Repositories\CacheRepository;

class CompanyObserver
{
    public function created(Company $company): void
    {
        $cacheRepository = app(CacheRepository::class);
        $cacheRepository->forgetCompanyCount($company->user_id);
    }

    public function updated(Company $company): void {}

    public function deleted(Company $company): void
    {
        Chat::where('company_id', $company->id)->delete();

        $cacheRepository =  app(CacheRepository::class);
        $cacheRepository->forgetCompanyCount($company->user_id);
    }

    public function restored(Company $company): void {}
}
