<?php

namespace App\Modules\Partner\Observers;

use App\Modules\Partner\Models\Location;
use App\Shared\Repositories\CacheRepository;

class LocationObserver
{
    public function created(Location $location): void
    {
        app(CacheRepository::class)
            ->forgetCompanyLocationsCount($location->company_id);
    }

    public function updated(Location $location): void {}

    public function deleted(Location $location): void
    {
        app(CacheRepository::class)->forgetCompanyLocationsCount($location->company_id);
    }

    public function restored(Location $location): void {}
}
