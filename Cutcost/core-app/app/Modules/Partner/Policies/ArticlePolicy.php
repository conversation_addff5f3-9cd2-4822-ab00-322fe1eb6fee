<?php

namespace App\Modules\Partner\Policies;

use App\Modules\Partner\Models\Article;
use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;

class ArticlePolicy
{

    public function viewAny(Article $article): bool
    {
        return false;
    }


    public function view(Article $article, Company $company): bool
    {
        return false;
    }


    public function create(Article $article): bool
    {
        return false;
    }

    public function update(User $user, Article $article): bool
    {
        return $user->companies()->where('companies.id', $article->company_id)->exists();
    }

    public function delete(Article $article): bool
    {
        return false;
    }

    public function restore(Article $article): bool
    {
        return false;
    }

    public function forceDelete(User $user, Article $article): bool
    {
        return $user->company->id === $article->company_id;
    }
}
