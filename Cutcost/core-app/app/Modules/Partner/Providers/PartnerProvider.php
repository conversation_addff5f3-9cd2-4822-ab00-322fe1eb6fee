<?php

namespace App\Modules\Partner\Providers;

use App\Modules\Messenger\Services\Billing\StripeService;
use App\Modules\Partner\Contracts\CouponContract;
use App\Modules\Partner\Contracts\PaymentContract;
use App\Modules\Partner\Contracts\ReferralRepositoryContract;
use App\Modules\Partner\Models\Article;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Location;
use App\Modules\Partner\Observers\CompanyObserver;
use App\Modules\Partner\Observers\LocationObserver;
use App\Modules\Partner\Policies\ArticlePolicy;
use App\Modules\Partner\Repositories\CouponRepository;
use App\Modules\Partner\Repositories\ReferralRepository;
use App\Shared\Policies\CompanyPolicy;
use App\Shared\Policies\CouponPolicy;
use App\Shared\Policies\LocationPolicy;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class PartnerProvider extends ServiceProvider
{

    public function register(): void
    {
        $this->app->bind(CouponContract::class, CouponRepository::class);
        $this->app->bind(ReferralRepositoryContract::class, ReferralRepository::class);
        $this->app->bind(PaymentContract::class, StripeService::class);
    }

    public function boot(): void
    {
        Company::observe(CompanyObserver::class);
        Location::observe(LocationObserver::class);
        Gate::policy(Coupon::class, CouponPolicy::class);
        Gate::policy(Location::class, LocationPolicy::class);
        Gate::policy(Article::class, ArticlePolicy::class);
        Gate::policy(Company::class, CompanyPolicy::class);
    }
}
