<?php

namespace App\Modules\Partner\Repositories;

use App\Modules\Partner\Contracts\CouponContract;
use App\Modules\Partner\Models\Coupon;
use App\Shared\Models\User;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\DB;

final class CouponRepository implements CouponContract
{
    public function getScannedCouponsByUser(User $user, Coupon $coupon): Builder
    {
        return DB::table('scans')->where('user_id', $user->id)
            ->where('coupon_id', $coupon->id);
    }

    public function getScannedCoupons(Coupon $coupon): Builder
    {
        return  DB::table('scans')
            ->where('coupon_id', $coupon->id);
    }

    public function couponOwnership(Coupon $coupon): Builder
    {
        return auth()->user()->myCoupons()->select('coupons.id')
            ->where('coupons.id', $coupon->id);
    }

    public function allCouponsOwnership(): Relation
    {
        return auth()->user()->myCoupons();
    }
}
