<?php

namespace App\Modules\Partner\Repositories;

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Industry;
use App\Shared\Models\City;
use App\Shared\Models\Country;
use App\Shared\Models\Currency;
use App\Shared\Models\Locale;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

final class PartnerRepository
{

    public function getAllCountries()
    {
        return Cache::rememberForever('countries', function () {
            return Country::select(['id', 'name', 'code'])->get()
                ->sortBy(fn($country) => $country->name === 'Worldwide' ? '' : $country->name)->values();
        });
    }

    public function getAllCurrencies()
    {
        return Cache::rememberForever('currencies', function () {
            return Currency::select(['id', 'name', 'code'])->get();
        });
    }

    public function getAllIndustries()
    {
        return Cache::rememberForever('industries', function () {
            return Industry::select(['id', 'name'])->get();
        });
    }

    public function getCompanyRelations()
    {
        return [
            'countries' => $this->getAllCountries(),
            'industries' => $this->getAllIndustries(),
            'currencies' => $this->getAllCurrencies(),
        ];
    }


    public function getUsedCities()
    {
        return Cache::rememberForever('used_cities', function () {
            $ids = DB::table('locations')
                ->select('city_id')
                ->distinct()
                ->where('is_open', true)
                ->pluck('city_id')
                ->toArray();

            return City::whereIn('id', $ids)
                ->select('name', 'id', 'country_code')
                ->get();
        });
    }

    public function getCompany(string $slug): ?Company
    {
        return Company::where('slug', $slug)->with(['industries', 'country', 'currency'])->firstOrFail();
    }
}
