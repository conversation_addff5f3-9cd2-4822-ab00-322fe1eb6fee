<?php

namespace App\Modules\Partner\Repositories;

use App\Modules\Partner\Contracts\ReferralRepositoryContract;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\DB;

final class ReferralRepository implements ReferralRepositoryContract
{
    public function clearUsedTasks(int $userId, int $taskId, int $quantity): void
    {
        DB::table('user_task')->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->limit($quantity)
            ->update(['deleted_at' => \Carbon\Carbon::now()]);
    }

    public function getUserTasks(): Relation
    {
        return auth()->user()->tasks()
            ->with('coupons:id,slug,name,images')->whereNull('user_task.deleted_at')
            ->orderBy('created_at', 'desc');
    }

    public function getLeaders(): Builder
    {
        return DB::table('user_task')
            ->join('users', 'user_task.user_id', '=', 'users.id')
            ->select('users.id', 'users.display_name', 'users.nickname', DB::raw('COUNT(*) as tasks_count'))
            ->groupBy('user_task.user_id', 'users.id', 'users.nickname', 'users.display_name')
            ->orderByDesc('tasks_count')
            ->limit(10);
    }
}
