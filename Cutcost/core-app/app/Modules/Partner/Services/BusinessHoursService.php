<?php

namespace App\Modules\Partner\Services;

use App\Shared\Models\City;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;

class DaySchedule
{
    public const TYPE_WORK   = 'work';
    public const TYPE_OFF    = 'off';
    public const TYPE_OPEN24 = 'open24';

    public string  $type;
    public ?string $timeStartStr;
    public ?string $timeEndStr;
    /** @var array<int, array{start: string, end: string}> */
    public array   $breaks;

    public function __construct(string $type, ?string $timeStartStr, ?string $timeEndStr, array $breaks = [])
    {
        $this->type         = $type;
        $this->timeStartStr = $timeStartStr;
        $this->timeEndStr   = $timeEndStr;
        $this->breaks       = $breaks;
    }

    public function isOffDay(): bool
    {
        return $this->type === self::TYPE_OFF;
    }

    public function isOpen24(): bool
    {
        return $this->type === self::TYPE_OPEN24;
    }

    public function isCurrentlyOpen(CarbonImmutable $now): bool
    {
        if ($this->isOffDay()) {
            return false;
        }
        if ($this->isOpen24()) {
            return true;
        }

        $startDateTime = CarbonImmutable::createFromFormat('H:i', $this->timeStartStr, $now->getTimezone())
            ->setDate($now->year, $now->month, $now->day);

        $endDateTime = CarbonImmutable::createFromFormat('H:i', $this->timeEndStr, $now->getTimezone())
            ->setDate($now->year, $now->month, $now->day);

        // если время закрытия раньше или равно времени открытия — значит ночь, добавляем день
        if ($endDateTime->lessThanOrEqualTo($startDateTime)) {
            $endDateTime = $endDateTime->addDay();
        }
        if (! $now->between($startDateTime, $endDateTime)) {
            return false;
        }

        // проверяем перерывы
        foreach ($this->breaks as $br) {
            $bStart = $now->setTimeFromTimeString($br['start']);
            $bEnd = $now->setTimeFromTimeString($br['end']);
            if ($bEnd->lessThanOrEqualTo($bStart)) {
                $bEnd = $bEnd->addDay();
            }
            if ($now->between($bStart, $bEnd)) {
                return false;
            }
        }

        return true;
    }

    public function closureReason(CarbonImmutable $now): ?string
    {
        if ($this->isOffDay()) {
            return 'closed_manually';
        }
        if ($this->isOpen24()) {
            return null;
        }

        $startDateTime = $now->setTimeFromTimeString($this->timeStartStr);
        $endDateTime   = $now->setTimeFromTimeString($this->timeEndStr);

        if ($endDateTime->lessThanOrEqualTo($startDateTime)) {
            $endDateTime = $endDateTime->addDay();
            if ($now->lessThan($startDateTime)) {
                $now = $now->subDay();
            }
        }

        if ($now->lessThan($startDateTime) || $now->greaterThanOrEqualTo($endDateTime)) {
            return 'closed_by_worktime';
        }

        foreach ($this->breaks as $br) {
            $bStart = $now->setTimeFromTimeString($br['start']);
            $bEnd   = $now->setTimeFromTimeString($br['end']);

            if ($bEnd->lessThanOrEqualTo($bStart)) {
                $bEnd = $bEnd->addDay();
                if ($now->lessThan($bStart)) {
                    $now = $now->subDay();
                }
            }

            if ($now->greaterThanOrEqualTo($bStart) && $now->lessThan($bEnd)) {
                return 'closed_by_break';
            }
        }

        return null;
    }

    public function nextCloseTimeForOpen24(array $scheduleMap, CarbonImmutable $now): ?CarbonImmutable
    {
        // Перебираем следующие 7 дней начиная со следующего дня
        for ($i = 1; $i <= 7; $i++) {
            $candidateDay = $now->addDays($i);
            $dayName = $candidateDay->format('l');
            $schedule = $scheduleMap[$dayName] ?? null;

            if ($schedule === null) {
                continue;
            }

            // Если день не open24, значит в этот день будет закрытие в заданное время
            if (! $schedule->isOpen24() && ! $schedule->isOffDay()) {
                // Возвращаем время закрытия этого дня
                $closeTime = $candidateDay->setTimeFromTimeString($schedule->timeEndStr);
                // Если время закрытия меньше или равно времени открытия — значит ночь, добавим день
                $startTime = $candidateDay->setTimeFromTimeString($schedule->timeStartStr);
                if ($closeTime->lessThanOrEqualTo($startTime)) {
                    $closeTime = $closeTime->addDay();
                }
                return $closeTime;
            }
            // Если день выходной — можно и вернуть как закрытие (то есть в этот день будет закрыто)
            if ($schedule->isOffDay()) {
                // Например, начало следующего дня — закрытие
                return $candidateDay->startOfDay();
            }
        }

        // Если все 7 дней 24/7, значит закрытия нет
        return null;
    }


    public function nextCloseTime(CarbonImmutable $now): ?CarbonImmutable
    {
        if ($this->isOffDay() || $this->closureReason($now) !== null || $this->isOpen24()) {
            return null;
        }

        $startDateTime = $now->setTimeFromTimeString($this->timeStartStr);
        $endDateTime   = $now->setTimeFromTimeString($this->timeEndStr);

        if ($endDateTime->lessThanOrEqualTo($startDateTime)) {
            $endDateTime = $endDateTime->addDay();
            if ($now->lessThan($startDateTime)) {
                $now = $now->subDay();
            }
        }

        foreach ($this->breaks as $br) {
            $bStart = $now->setTimeFromTimeString($br['start']);
            if ($bStart->lessThanOrEqualTo($startDateTime)) {
                $bStart = $bStart->addDay();
            }
            if ($bStart->greaterThan($now)) {
                return $bStart;
            }
        }

        return $endDateTime;
    }

    public function nextOpenAfterBreak(CarbonImmutable $now): ?CarbonImmutable
    {
        if ($this->closureReason($now) !== 'closed_by_break') {
            return null;
        }

        foreach ($this->breaks as $br) {
            $bStart = $now->setTimeFromTimeString($br['start']);
            $bEnd   = $now->setTimeFromTimeString($br['end']);

            if ($bEnd->lessThanOrEqualTo($bStart)) {
                $bEnd = $bEnd->addDay();
                if ($now->lessThan($bStart)) {
                    $now = $now->subDay();
                }
            }

            if ($now->greaterThanOrEqualTo($bStart) && $now->lessThan($bEnd)) {
                return $bEnd;
            }
        }

        return null;
    }
}

class BusinessHoursService
{
    public const REASON_CLOSED_MANUALLY  = 'closed_manually';
    public const REASON_CLOSED_BY_WORK   = 'closed_by_worktime';
    public const REASON_CLOSED_BY_BREAK  = 'closed_by_break';

    /**
     * Возвращает массив с ключами:
     * 'open' => bool
     * 'reason' => string|null
     * 'opens_in' => string|null (в человекочитаемом виде)
     * 'closes_in' => string|null (в человекочитаемом виде)
     */
    public function isOpenNow(object $location): array
    {
        $locationTz = $location->city->timezone ?? config('app.timezone');
        $now = CarbonImmutable::now($locationTz);
        $cacheKey = 'is_open_now_' . ($location->id ?? '0') . '_' . $now->format('YmdHi');

        // We can't access $cacheKey to delete it later, so we store all keys in another cache key
        $keysArray = Cache::get('is_open_now_keys_' . $location->id, []);
        $keysArray[] = $cacheKey;
        $keysArray = array_unique($keysArray);
        Cache::put('is_open_now_keys_' . $location->id, $keysArray, now()->addHours(1));

        $result = Cache::remember($cacheKey, 120, function () use ($location, $now) {
            if (! $location->is_open) {
                return [
                    'open'      => false,
                    'reason'    => 'app.open_hours.' . self::REASON_CLOSED_MANUALLY,
                    'opens_in'  => null,
                    'closes_in' => null,
                ];
            }

            $todayName   = $now->format('l');
            $rawSchedule = collect($location->working_hours);

            $scheduleMap = $this->buildScheduleMap($rawSchedule);

            $todaySchedule = $scheduleMap[$todayName] ?? null;

            if ($todaySchedule === null || $todaySchedule->isOffDay()) {
                return [
                    'open'      => false,
                    'reason'    => 'app.open_hours.' . self::REASON_CLOSED_BY_WORK,
                    'opens_in'  => $this->findNextOpenHuman($scheduleMap, $now),
                    'closes_in' => null,
                ];
            }

            if ($todaySchedule->isOpen24()) {
                $nextClose = $todaySchedule->nextCloseTimeForOpen24($scheduleMap, $now);
                return [
                    'open'      => true,
                    'reason'    => null,
                    'opens_in'  => null,
                    'closes_in' => $nextClose,
                ];
            }

            if ($todaySchedule->isCurrentlyOpen($now)) {
                $nextClose = $todaySchedule->nextCloseTime($now);
                return [
                    'open'      => true,
                    'reason'    => null,
                    'opens_in'  => null,
                    'closes_in' => $nextClose,
                ];
            }

            $closureReason = $todaySchedule->closureReason($now);

            if ($closureReason === self::REASON_CLOSED_BY_BREAK) {
                $nextOpen = $todaySchedule->nextOpenAfterBreak($now);
                return [
                    'open'      => false,
                    'reason'    => 'app.open_hours.' . self::REASON_CLOSED_BY_BREAK,
                    'opens_in'  => $nextOpen,
                    'closes_in' => null,
                ];
            }

            if ($closureReason === self::REASON_CLOSED_BY_WORK) {
                $startDT = $now->setTimeFromTimeString($todaySchedule->timeStartStr);
                $endDT   = $now->setTimeFromTimeString($todaySchedule->timeEndStr);

                if ($endDT->lessThanOrEqualTo($startDT)) {
                    $endDT = $endDT->addDay();
                }

                if ($now->lessThan($startDT)) {
                    return [
                        'open'      => false,
                        'reason'    => 'app.open_hours.' . self::REASON_CLOSED_BY_WORK,
                        'opens_in'  => $startDT,
                        'closes_in' => null,
                    ];
                }

                if ($now->greaterThanOrEqualTo($endDT)) {
                    return [
                        'open'      => false,
                        'reason'    => 'app.open_hours.' . self::REASON_CLOSED_BY_WORK,
                        'opens_in'  => $this->findNextOpenHuman($scheduleMap, $now),
                        'closes_in' => null,
                    ];
                }
            }

            return [
                'open'      => false,
                'reason'    => 'app.open_hours.' . self::REASON_CLOSED_MANUALLY,
                'opens_in'  => null,
                'closes_in' => null,
            ];
        });

        $result['opens_in'] = $result['opens_in'] ? CarbonImmutable::parse($result['opens_in']) : null;
        $result['closes_in'] = $result['closes_in'] ? CarbonImmutable::parse($result['closes_in']) : null;

        $result = array_map(
            function ($value) {
                if (is_null($value)) {
                    return null;
                } elseif (is_bool($value)) {
                    return $value;
                } elseif ($value instanceof CarbonImmutable) {
                    return $value->setTimezone($this->resolveUserTimezone())->diffForHumans();
                } elseif (is_string($value)) {
                    return __($value);
                }

                return $value;
            },
            $result
        );


        return $result;
    }

    private function resolveUserTimezone(): string
    {
        $cityId = Cookie::get('city') ?? auth()?->user()?->city_id;

        return Cache::rememberForever("tz_city_$cityId", function () use ($cityId) {
            return City::find($cityId)?->timezone ?? config('app.timezone');
        });
    }

    /**
     * Строит карту расписания с ключами — названиями дней недели
     * @param Collection $rawSchedule
     * @return array<string, DaySchedule>
     */
    private function buildScheduleMap(Collection $rawSchedule): array
    {
        $weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        $map = [];

        foreach ($weekdays as $wd) {
            $map[$wd] = new DaySchedule(DaySchedule::TYPE_OFF, null, null);
        }

        // Обработка выходных
        $rawSchedule->filter(fn($entry) => $entry['type'] === 'off_day')
            ->pluck('name')
            ->each(fn(string $day) => $map[$day] = new DaySchedule(DaySchedule::TYPE_OFF, null, null));

        // Обработка рабочих дней
        $rawSchedule->filter(fn($entry) => $entry['type'] === 'work_day')
            ->each(function ($entry) use (&$map) {
                $open24 = $entry['open24'] ?? false;
                $breaks = $entry['breaks'] ?? [];
                $day = $entry['name'];
                if ($open24) {
                    $map[$day] = new DaySchedule(DaySchedule::TYPE_OPEN24, null, null);
                } else {
                    $start = $entry['timeStart'] ?? '00:00';
                    $end   = $entry['timeEnd']   ?? '00:00';
                    $map[$day] = new DaySchedule(DaySchedule::TYPE_WORK, $start, $end, $breaks);
                }
            });

        return $map;
    }

    private function findNextOpenHuman(array $map, CarbonImmutable $now): ?string
    {
        $todayName = $now->format('l');
        $next = $this->findNextOpenTimestamp($map, $todayName, $now);
        return $next;
    }

    private function findNextOpenTimestamp(array $map, string $todayName, CarbonImmutable $now): ?CarbonImmutable
    {
        // Проверяем сегодня
        $todaySch = $map[$todayName] ?? null;
        if ($todaySch !== null && ! $todaySch->isOffDay() && ! $todaySch->isOpen24()) {
            $startDT = $now->setTimeFromTimeString($todaySch->timeStartStr);
            if ($now->lessThan($startDT)) {
                return $startDT;
            }
        }

        // Ищем в следующие 7 дней
        for ($i = 1; $i <= 7; $i++) {
            $candidateDay = $now->addDays($i);
            $dayName = $candidateDay->format('l');
            $schedule = $map[$dayName] ?? null;

            if ($schedule === null || $schedule->isOffDay()) {
                continue;
            }

            if ($schedule->isOpen24()) {
                return $candidateDay->startOfDay();
            }
            return $candidateDay->setTimeFromTimeString($schedule->timeStartStr);
        }

        return null;
    }
}
