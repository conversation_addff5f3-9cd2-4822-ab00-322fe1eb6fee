<?php

namespace App\Modules\Partner\Services;

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Repositories\PartnerRepository;
use App\Shared\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

final class CompanyService
{


    public function create(PartnerRepository $partnerRepository)
    {
        return [
            ...$partnerRepository->getCompanyRelations(),
        ];
    }

    public function edit(Company $company, PartnerRepository $partnerRepository)
    {
        return [
            'company' => $company->loadMissing('industries'),
            ...$partnerRepository->getCompanyRelations(),
        ];
    }
}
