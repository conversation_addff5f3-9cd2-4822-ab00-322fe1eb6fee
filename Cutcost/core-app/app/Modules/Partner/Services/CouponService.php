<?php

namespace App\Modules\Partner\Services;

use App\Modules\Partner\Enums\CouponTypesEnum;
use App\Modules\Partner\Models\Coupon;
use Illuminate\Support\Collection;

final class CouponService
{
    public function edit(Coupon $coupon): array
    {
        $coupon['earnings'] = $coupon->scanHistory->sum('earnings');

        $coupon->load('media');

        if ($coupon->type === CouponTypesEnum::LOCATION) {
            $coupon->load(['locations:id']);
            $locations = $coupon->company->locations()
                ->where('type', '!=', 'online')->get();
        } else {
            $locations = $coupon->company->locations()
                ->where('type', 'online')->get();
        }

        $couponLocations = $coupon->locations;

        return [
            'coupon' => $coupon,
            'locations' => $locations,
            'couponLocations' => $couponLocations?->pluck('id'),
        ];
    }

    public static function isCouponLocationsOpen(Collection $locations): array
    {
        $reason = [];
        $locationService = app(BusinessHoursService::class);
        //if some location is open, then return data, else return reason
        foreach ($locations as $location) {
            $isOpenNow = $locationService->isOpenNow($location);
            if ($isOpenNow['open'] === true) {
                return $isOpenNow;
            } else {
                $reason = $isOpenNow;
            }
        }

        return $reason;
    }
}
