<?php

namespace App\Modules\Partner\Services;

use App\Modules\Partner\Enums\LocationTypesEnum;
use App\Modules\Partner\Http\Resources\CompanyResource;
use App\Modules\Partner\Http\Resources\CouponMiniResource;
use App\Modules\Partner\Http\Resources\LocationResource;
use App\Modules\Partner\Models\Location;
use App\Modules\Partner\Models\Company;
use App\Shared\Repositories\CacheRepository;
use App\Shared\Services\MediaService;
use App\Shared\Services\Pagination\PaginatorService;
use Illuminate\Support\Str;

final class LocationService
{

    public function index(Company $company, BusinessHoursService $businessHoursService)
    {
        $locations = PaginatorService::fromQuery($company->locations())
            ->transformWith(fn($lc) => [
                'name' => $lc->name,
                'type' => $lc->type,
                ...$lc->type === LocationTypesEnum::ONLINE ? [] : ['address' => $lc->address],
                'is_open' => $businessHoursService->isOpenNow($lc)['open'],
                'slug' => $lc->slug,
            ])
            ->search(['name', 'address'])
            ->paginate(10);

        return [
            'locations' => $locations,
            'company' => new CompanyResource($company),
        ];
    }

    public function create(Company $company)
    {
        return [
            'company' => new CompanyResource($company),
        ];
    }

    public function edit(
        Company $company,
        Location $location,
    ) {

        $location->loadMissing(['coupons:id', 'city']);

        return [
            'location' => new LocationResource($location),
            'locationCoupons' => $location->coupons->pluck('id'),
            'company' => new CompanyResource($company),
            'coupons' => CouponMiniResource::collection($company->coupons()
                ->get()),
        ];
    }

    public function update(
        Location $location,
        array &$validated,
        MediaService $mediaService
    ) {

        $mediaService->updateRequest(
            $validated['upload'],
            Location::class,
            $location->id
        );

        $location->update($validated);
    }
}
