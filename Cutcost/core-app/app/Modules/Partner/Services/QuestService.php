<?php

namespace App\Modules\Partner\Services;

use Illuminate\Support\Collection;

final class QuestService
{
    //mapers

    public function transfromToCompletedTasksWithCount(Collection $rewards)
    {
        // leave only unique tasks row couse user may have multiple
        // Count it, how muck tasks completed?

        return $rewards->groupBy('id')->map(function ($items) {
            return array_merge(
                $items->first()->toArray(),
                ['count' => $items->count()]
            );
        })->values();
    }

    public function rewardsAvailable()
    {
        // :TODO show available rewards in all quests ui interface
    }
}
