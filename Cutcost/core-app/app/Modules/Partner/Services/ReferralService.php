<?php

namespace App\Modules\Partner\Services;

use App\Modules\Partner\Contracts\CouponContract;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Traits\ReferralTrait;
use App\Shared\Models\Task;
use App\Shared\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Service responsible for managing referral-related logic.
 */
final class ReferralService
{
    use ReferralTrait;

    /**
     * Completes a task for a referred user, ensuring it's a valid referral and hasn't been processed before.
     *
     * @param array|null $qr QR payload that contains referral data.
     * @return void
     */
    public function completeTask(?array $referral, array $coupon): void {}

    /**
     * Checks if a referral coupon is valid by counting how many users own it.
     *
     * @param Coupon $coupon The coupon to validate.
     * @return int Count of coupon ownership, or -1 if not a referral coupon.
     */
    public function validReferralCoupon(Coupon $coupon): int
    {
        $couponRepository = app(CouponContract::class);
        $couponsCountInOwnership = -1; // Default for non-referral

        if ($coupon->is_referral) {
            $couponsCountInOwnership = $couponRepository->couponOwnership($coupon)->count();
        }

        return $couponsCountInOwnership;
    }

    /**
     * Removes reward for refferral coupon from a user.
     *
     * @param Coupon $coupon The coupon to remove.
     * @param User $user The ID of the user.
     * @return void
     */
    public function removeRewardFromUser(Coupon $coupon, User $user): void
    {
        DB::table('coupon_user')
            ->where('coupon_id', $coupon->id)
            ->where('user_id', $user->id)
            ->limit(1)
            ->delete();
    }
}
