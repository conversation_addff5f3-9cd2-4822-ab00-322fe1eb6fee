<?php

namespace App\Modules\Partner\Services;

use App\Modules\Partner\Http\Resources\CouponResource;
use App\Modules\Partner\Models\Coupon;
use App\Shared\Http\Resources\UserMiniResource;
use App\Shared\Models\User;

final class ScanService
{

    /**
     * 
     * Qr is scanned coupon and user data and referral
     * 
     * @param array $qr
     * @param Coupon $coupon
     * @param User $user
     * 
     * @return array
     */
    public function validateScan(
        Coupon $coupon,
        User $user,
        ReferralService $referralService,
    ): array {

        $message = __('app.scan.success');;
        $status = 'success';

        if (auth()->id() === $user->id && app()->isProduction()) {
            $message = __('app.scan.cant_scan_yourself');
            $status = 'error';
        }

        $referralCoupponsInOwnership = $referralService->validReferralCoupon($coupon);

        if ($referralCoupponsInOwnership === 0) {
            $message = __('app.scan.not_owner');
            $status = 'error';
        }


        return [
            'couponsInOwnership' => $referralCoupponsInOwnership,
            'status' => $status,
            'coupon' => new CouponResource($coupon),
            'user' => new UserMiniResource($user),
            'message' => $message,
        ];
    }
}
