<?php

namespace App\Modules\Partner\Traits;

use Illuminate\Database\Query\Expression;
use Illuminate\Support\Facades\DB;

trait HasSpatialFields
{
    /**
     * Create a POINT with SRID 4326.
     *
     * @param float $lng
     * @param float $lat
     * @return Expression
     */
    public function setPoint(float $lng, float $lat): Expression
    {
        return DB::raw("ST_GeomFromText('POINT($lng $lat)', 4326)");
    }

    /**
     * Create a POINT with SRID 4326 from an array ['lat' => ..., 'lng' => ...].
     *
     * @param array $coords
     * @return Expression
     */
    public function setPointFromArray(array $coords): Expression
    {
        return $this->setPoint($coords['lng'], $coords['lat']);
    }

    /**
     * Intercept attribute assignment to auto-convert 'lnglat' to spatial POINT type.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setAttribute($key, $value)
    {
        if ($key === 'lnglat') {
            if (is_array($value) && isset($value['lat'], $value['lng'])) {
                $value = $this->setPointFromArray($value);
            } elseif (is_object($value) && isset($value->lat, $value->lng)) {
                $value = $this->setPointFromArray((array) $value);
            }
        }

        parent::setAttribute($key, $value);
    }

    /**
     * Распаковываем бинарное поле POINT из MySQL в массив с ключами lng и lat.
     *
     * @param string|null $value
     * @return array|null
     */
    public function getLnglatAttribute($value)
    {
        if (!$value) return null;
        $result = DB::selectOne("SELECT ST_X(?) AS lng, ST_Y(?) AS lat", [$value, $value]);

        return ['lng' => $result->lng, 'lat' => $result->lat];
    }
}
