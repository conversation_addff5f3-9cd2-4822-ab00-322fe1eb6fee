<?php

namespace App\Modules\Partner\Traits;

use Illuminate\Support\Collection;

trait ReferralTrait
{
    /**
     * Generates a unique cache key for a referral task.
     *
     * @param int $refId Referral user ID.
     * @param int $couponId Coupon ID.
     * @param int $taskId Task ID.
     * @param int $id Referral ID.
     * @return string
     */
    protected function couponTemplate(int $refId, int $couponId, int $taskId, int $id): string
    {
        return "task_{$taskId}_ref_{$refId}_coupon_{$couponId}_{$id}";
    }

    /**
     * Decrypts and decodes a QR payload from base64 JSON format.
     *
     * @param string $cryptedQr Encrypted QR data.
     * @return array
     */
    protected function decryptQr(string $cryptedQr): array
    {
        return (array) json_decode(base64_decode($cryptedQr), true);
    }

    /**
     * Syncs reward and target coupons with their types for task association.
     *
     * @param array|int $rewards Reward coupon IDs.
     * @param array|int $targets Target coupon IDs.
     * @return Collection
     */
    protected function syncCouponTask(array|int|string $rewards, array|int|string $targets): Collection
    {
        return collect($rewards)->map(function ($id) {
            return [$id => ['type' => 'reward']];
        })
            ->merge(
                collect($targets)->map(function ($id) {
                    return [$id => ['type' => 'target']];
                })
            )->collapseWithKeys();
    }
}
