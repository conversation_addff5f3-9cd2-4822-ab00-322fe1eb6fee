<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Partner\Repositories\PartnerRepository;
use App\Shared\Repositories\CacheRepository;
use App\Shared\Services\SEO\FeedSEO;
use DateTimeZone;
use Inertia\Inertia;
use Inertia\Response;

class FeedController
{
    public function coupons(
        FeedSEO $seo,
        CacheRepository $cacheRepository
    ): Response {

        $seo->make();

        return Inertia::render('Discount/Feed', [
            'categories' => $cacheRepository->getAllCategories(),
            'countries' => $cacheRepository->getUsedCountries(),
        ]);
    }
}
