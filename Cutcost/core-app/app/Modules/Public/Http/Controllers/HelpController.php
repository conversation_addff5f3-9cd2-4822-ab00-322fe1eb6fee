<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Public\Models\Article;
use App\Shared\Services\MetaService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class HelpController
{
    public function index()
    {
        MetaService::setBasicMeta(
            'CutCost — Help and Support Center',
            'Find answers to frequently asked questions, user guides, and get support for using the CutCost platform.',
            null,
            'CutCost, help, support, FAQ, frequently asked questions, user guide, technical support, how to use, instructions, troubleshooting'
        );

        $aboutContent = $this->getAboutContent();
        $helpArticles = $this->getHelpArticles();
        $faqs = $this->getFaqs();
        $helpCategories = $this->getHelpCategories();

        return Inertia::render('Help/Index', [
            'aboutContent' => $aboutContent,
            'helpArticles' => $helpArticles,
            'faqs' => $faqs,
            'categories' => $helpCategories,
        ]);
    }

    /**
     * Display FAQ page
     */
    public function faq()
    {
        MetaService::setBasicMeta(
            'CutCost — Frequently Asked Questions | FAQ',
            'Find answers to the most common questions about using CutCost platform, deals, coupons, and business features.',
            null,
            'CutCost, FAQ, frequently asked questions, help, support, answers'
        );

        $faqs = $this->getFaqs();
        $faqCategories = $this->getFaqCategories();

        return Inertia::render('Help/FAQ', [
            'faqs' => $faqs,
            'categories' => $faqCategories,
        ]);
    }

    /**
     * Display a specific help article
     */
    public function article(string $slug)
    {
        $article = Article::where('slug', $slug)
            ->where(function ($query) {
                $query->where('type', Article::TYPE_HELP)
                      ->orWhere('type', Article::TYPE_FAQ);
            })
            ->published()
            ->first();

        if (!$article) {
            abort(404, 'Help article not found');
        }

        // Increment views
        $article->incrementViews();

        MetaService::setBasicMeta(
            "CutCost Help — {$article->name}",
            $article->excerpt ?? "Learn about {$article->name} on CutCost platform.",
            null,
            "CutCost, help, guide, {$article->name}"
        );

        return Inertia::render('Help/Article', [
            'article' => [
                'id' => $article->id,
                'title' => $article->name,
                'content' => $article->content,
                'category' => $article->category,
                'excerpt' => $article->excerpt,
                'created_at' => $article->formatted_created_at,
                'reading_time' => $article->reading_time,
                'views' => $article->views,
                'slug' => $article->slug,
            ],
        ]);
    }

    /**
     * Search help articles
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (empty($query)) {
            return redirect()->route('help.index');
        }

        $results = Article::where(function ($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('content', 'LIKE', "%{$query}%")
                  ->orWhere('excerpt', 'LIKE', "%{$query}%");
            })
            ->where(function ($q) {
                $q->where('type', Article::TYPE_HELP)
                  ->orWhere('type', Article::TYPE_FAQ);
            })
            ->published()
            ->ordered()
            ->get()
            ->map(function ($article) {
                return [
                    'title' => $article->name,
                    'slug' => $article->slug,
                    'category' => $article->category,
                    'excerpt' => $article->excerpt,
                    'type' => $article->type,
                ];
            });

        MetaService::setBasicMeta(
            "CutCost Help — Search Results for '{$query}'",
            "Search results for help articles related to '{$query}' on CutCost platform.",
            null,
            "CutCost, help, search, {$query}"
        );

        return Inertia::render('Help/Search', [
            'query' => $query,
            'results' => $results,
            'popularSearches' => $this->getPopularSearches(),
        ]);
    }

    /**
     * Contact support form
     */
    public function contact()
    {
        MetaService::setBasicMeta(
            'CutCost — Contact Support',
            'Get in touch with our support team for help with your CutCost account, technical issues, or general questions.',
            null,
            'CutCost, contact, support, help, customer service'
        );

        // Fetch support channels from database/service
        $supportChannels = $this->getSupportChannels();

        return Inertia::render('Help/Contact', [
            'supportChannels' => $supportChannels
        ]);
    }

    /**
     * Get help articles from database
     */
    private function getHelpArticles(): array
    {
        return Article::helpArticles()
            ->take(6)
            ->get()
            ->map(function ($article) {
                return [
                    'title' => $article->name,
                    'slug' => $article->slug,
                    'category' => $article->category,
                    'excerpt' => $article->excerpt,
                    'reading_time' => $article->reading_time,
                    'views' => $article->views,
                ];
            })
            ->toArray();
    }

    /**
     * Get FAQs from database
     */
    private function getFaqs(): array
    {
        return Article::faqs()
            ->take(6)
            ->get()
            ->map(function ($faq) {
                return [
                    'question' => $faq->name,
                    'answer' => $faq->content,
                    'slug' => $faq->slug,
                    'category' => $faq->category,
                ];
            })
            ->toArray();
    }

    /**
     * Get FAQ categories from database
     */
    private function getFaqCategories(): array
    {
        return Article::faqs()
            ->select('category')
            ->distinct()
            ->pluck('category')
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * Get about content from database/service
     */
    private function getAboutContent(): array
    {
        // This would typically fetch from a database or CMS
        // For now, return structured content
        return [
            'title' => 'About CutCost',
            'description' => 'Your local deals and savings platform',
            'content' => 'CutCost connects customers with local businesses through exclusive deals, coupons, and promotions.',
            'mission' => 'To help people save money while supporting local businesses in their community.',
            'features' => [
                'Exclusive local deals and coupons',
                'Easy-to-use mobile and web platform',
                'Support for local businesses',
                'Real-time deal notifications'
            ]
        ];
    }

    /**
     * Get help categories from database
     */
    private function getHelpCategories(): array
    {
        $categories = Article::helpArticles()
            ->select('category')
            ->selectRaw('COUNT(*) as articles_count')
            ->groupBy('category')
            ->get()
            ->map(function ($category) {
                return [
                    'id' => Str::slug($category->category),
                    'title' => $category->category,
                    'description' => $this->getCategoryDescription($category->category),
                    'icon' => $this->getCategoryIcon($category->category),
                    'articles_count' => $category->articles_count,
                ];
            })
            ->toArray();

        return $categories;
    }

    /**
     * Get category description
     */
    private function getCategoryDescription(string $category): string
    {
        $descriptions = [
            'Getting Started' => 'Learn the basics of using CutCost',
            'Using Coupons' => 'Master the art of finding and using deals',
            'Wallet & Payments' => 'Manage your cuts and payment methods',
            'Account Management' => 'Control your account settings and preferences',
            'For Businesses' => 'Information for business partners',
        ];

        return $descriptions[$category] ?? 'Helpful guides and information';
    }

    /**
     * Get category icon
     */
    private function getCategoryIcon(string $category): string
    {
        $icons = [
            'Getting Started' => 'rocket',
            'Using Coupons' => 'ticket',
            'Wallet & Payments' => 'wallet',
            'Account Management' => 'user',
            'For Businesses' => 'building',
        ];

        return $icons[$category] ?? 'question';
    }

    /**
     * Get support channels from database/service
     */
    private function getSupportChannels(): array
    {
        // This would typically fetch from a database or configuration
        return [
            [
                'type' => 'chat',
                'title' => 'Live Chat',
                'description' => 'Get instant help from our support team',
                'available' => true,
                'hours' => 'Mon-Fri 9AM-6PM'
            ],
            [
                'type' => 'email',
                'title' => 'Email Support',
                'description' => 'Send us a detailed message',
                'available' => true,
                'response_time' => 'Within 24 hours'
            ]
        ];
    }

    /**
     * Get popular search terms from database/service
     */
    private function getPopularSearches(): array
    {
        // This would typically fetch from analytics/database
        return [
            'how to redeem coupon',
            'create account',
            'reset password',
            'business registration',
            'payment issues',
            'delete account'
        ];
    }

}
