<?php

namespace App\Modules\Public\Http\Controllers;

use App\Shared\Services\MetaService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HelpController
{
    public function index()
    {
        MetaService::setBasicMeta(
            'CutCost — Help and Support Center',
            'Find answers to frequently asked questions, user guides, and get support for using the CutCost platform.',
            null,
            'CutCost, help, support, FAQ, frequently asked questions, user guide, technical support, how to use, instructions, troubleshooting'
        );

        $aboutContent = $this->getAboutContent();
        $helpCategories = $this->getHelpCategories();
        $popularTopics = $this->getPopularTopics();

        return Inertia::render('Help/Index', [
            'aboutContent' => $aboutContent,
            'categories' => $helpCategories,
            'popularTopics' => $popularTopics,
        ]);
    }

    /**
     * Display FAQ page
     */
    public function faq()
    {
        MetaService::setBasicMeta(
            'CutCost — Frequently Asked Questions | FAQ',
            'Find answers to the most common questions about using CutCost platform, deals, coupons, and business features.',
            null,
            'CutCost, FAQ, frequently asked questions, help, support, answers'
        );

        // Fetch FAQ content from database/service
        $faqs = $this->getFaqContent();

        return Inertia::render('Help/FAQ', [
            'faqs' => $faqs,
        ]);
    }

    /**
     * Display a specific help article
     */
    public function article(string $slug)
    {
        // Fetch article from database/service
        $article = $this->getHelpArticle($slug);

        if (!$article) {
            abort(404, 'Help article not found');
        }

        MetaService::setBasicMeta(
            "CutCost Help — {$article['title']}",
            $article['description'] ?? "Learn about {$article['title']} on CutCost platform.",
            null,
            "CutCost, help, guide, {$article['title']}"
        );

        return Inertia::render('Help/Article', [
            'article' => $article,
        ]);
    }

    /**
     * Search help articles
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (empty($query)) {
            return redirect()->route('help.index');
        }

        // Search articles in database/service
        $results = $this->searchHelpArticles($query);

        MetaService::setBasicMeta(
            "CutCost Help — Search Results for '{$query}'",
            "Search results for help articles related to '{$query}' on CutCost platform.",
            null,
            "CutCost, help, search, {$query}"
        );

        return Inertia::render('Help/Search', [
            'query' => $query,
            'results' => $results,
            'popularSearches' => $this->getPopularSearches(),
        ]);
    }

    /**
     * Contact support form
     */
    public function contact()
    {
        MetaService::setBasicMeta(
            'CutCost — Contact Support',
            'Get in touch with our support team for help with your CutCost account, technical issues, or general questions.',
            null,
            'CutCost, contact, support, help, customer service'
        );

        // Fetch support channels from database/service
        $supportChannels = $this->getSupportChannels();

        return Inertia::render('Help/Contact', [
            'supportChannels' => $supportChannels
        ]);
    }

    /**
     * Get about content from database/service
     */
    private function getAboutContent(): array
    {
        // This would typically fetch from a database or CMS
        // For now, return structured content
        return [
            'title' => 'About CutCost',
            'description' => 'Your local deals and savings platform',
            'content' => 'CutCost connects customers with local businesses through exclusive deals, coupons, and promotions.',
            'mission' => 'To help people save money while supporting local businesses in their community.',
            'features' => [
                'Exclusive local deals and coupons',
                'Easy-to-use mobile and web platform',
                'Support for local businesses',
                'Real-time deal notifications'
            ]
        ];
    }

    /**
     * Get help categories from database/service
     */
    private function getHelpCategories(): array
    {
        // This would typically fetch from a database
        return [
            [
                'id' => 'getting-started',
                'title' => 'Getting Started',
                'description' => 'Learn the basics of using CutCost',
                'icon' => 'rocket'
            ],
            [
                'id' => 'for-users',
                'title' => 'For Users',
                'description' => 'How to make the most of CutCost as a customer',
                'icon' => 'user'
            ],
            [
                'id' => 'for-businesses',
                'title' => 'For Businesses',
                'description' => 'Guide for business owners and partners',
                'icon' => 'building'
            ]
        ];
    }

    /**
     * Get popular topics from database/service
     */
    private function getPopularTopics(): array
    {
        // This would typically fetch from analytics/database
        return [
            ['title' => 'How to redeem coupons', 'slug' => 'redeeming-coupons'],
            ['title' => 'Creating an account', 'slug' => 'creating-account'],
            ['title' => 'Finding deals near me', 'slug' => 'finding-deals'],
            ['title' => 'Business registration', 'slug' => 'business-account']
        ];
    }

    /**
     * Get FAQ content from database/service
     */
    private function getFaqContent(): array
    {
        // This would typically fetch from a database
        return [
            [
                'category' => 'General',
                'questions' => [
                    [
                        'question' => 'What is CutCost?',
                        'answer' => 'CutCost is a platform that connects customers with local businesses through exclusive deals, coupons, and promotions.'
                    ],
                    [
                        'question' => 'Is CutCost free to use?',
                        'answer' => 'Yes, CutCost is completely free for customers. You can browse deals, redeem coupons, and use all customer features at no cost.'
                    ]
                ]
            ]
        ];
    }

    /**
     * Get support channels from database/service
     */
    private function getSupportChannels(): array
    {
        // This would typically fetch from a database or configuration
        return [
            [
                'type' => 'chat',
                'title' => 'Live Chat',
                'description' => 'Get instant help from our support team',
                'available' => true,
                'hours' => 'Mon-Fri 9AM-6PM'
            ],
            [
                'type' => 'email',
                'title' => 'Email Support',
                'description' => 'Send us a detailed message',
                'available' => true,
                'response_time' => 'Within 24 hours'
            ]
        ];
    }

    /**
     * Get help article by slug from database/service
     */
    private function getHelpArticle(string $slug): ?array
    {
        // This would typically fetch from a database
        $articles = [
            'creating-account' => [
                'title' => 'Creating an Account',
                'description' => 'Learn how to create your CutCost account',
                'content' => '<p>Step-by-step guide to creating your account...</p>',
                'category' => 'Getting Started',
                'updated_at' => '2024-01-15'
            ],
            'redeeming-coupons' => [
                'title' => 'Redeeming Coupons',
                'description' => 'How to use your coupons at participating businesses',
                'content' => '<p>Guide to redeeming your coupons...</p>',
                'category' => 'For Users',
                'updated_at' => '2024-01-10'
            ]
        ];

        return $articles[$slug] ?? null;
    }

    /**
     * Get popular search terms from database/service
     */
    private function getPopularSearches(): array
    {
        // This would typically fetch from analytics/database
        return [
            'how to redeem coupon',
            'create account',
            'reset password',
            'business registration',
            'payment issues',
            'delete account'
        ];
    }

    /**
     * Search help articles in database/service
     */
    private function searchHelpArticles(string $query): array
    {
        // This would typically perform a database search
        // For demonstration, return filtered results based on query
        $allArticles = [
            [
                'title' => 'Creating an Account',
                'description' => 'Learn how to create your CutCost account',
                'slug' => 'creating-account',
                'category' => 'Getting Started'
            ],
            [
                'title' => 'Redeeming Coupons',
                'description' => 'How to use your coupons at participating businesses',
                'slug' => 'redeeming-coupons',
                'category' => 'For Users'
            ]
        ];

        // Simple search simulation
        return array_filter($allArticles, function($article) use ($query) {
            return stripos($article['title'], $query) !== false ||
                   stripos($article['description'], $query) !== false;
        });
    }
}
