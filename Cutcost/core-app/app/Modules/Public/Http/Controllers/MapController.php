<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Partner\Models\Location;
use App\Shared\Services\SEO\MapSEO;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MapController
{

    public function index(MapSEO $seo)
    {
        try {

            $locations = Location::select('id', 'name', 'lnglat', 'slug')
                ->with('company', 'user:id,is_banned')->active()->real()
                ->whereHas('user', function ($q) {
                    $q->where('is_banned', false);
                })
                ->get();

            $seo->make();

            return Inertia::render('Discount/ShowLocations', [
                'locations' => $locations,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return to_route('feed')->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.locations')]));
        }
    }
}
