<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Partner\Models\Article;
use App\Modules\Public\Http\Resources\ArticleResource;
use App\Shared\Services\MetaService;
use App\Shared\Services\Pagination\PaginatorService;
use Inertia\Inertia;

/**
 * :todo
 * Make excerpt field
 * make images inside article
 */
class NewsController
{

    public function index()
    {
        $paginatedArticles = PaginatorService::fromQuery(Article::query()->with('media'))
            ->transformWith(fn($article) => new ArticleResource($article))
            ->paginate(10);

        // dd($paginatedArticles);

        // Заголовки на разных языках
        $titleRu = 'CutCost — последние новости и статьи';
        $titleLt = 'CutCost — naujausios naujienos ir straipsniai';
        $titleEn = 'CutCost - Latest news and articles';

        // Описания на разных языках
        $descriptionRu = 'CutCost — платформа с актуальными новостями, статьями и полезной информацией о скидках и локальном бизнесе.';
        $descriptionLt = 'CutCost — platforma su naujausiomis naujienomis, straipsniais ir naudinga informacija apie nuolaidas ir vietinį verslą.';
        $descriptionEn = 'CutCost is a platform with the latest news, articles, and useful information about deals and local businesses.';

        // Ключевые слова расширенные
        $keywordsRu = [
            'CutCost',
            'новости',
            'статьи',
            'актуальная информация',
            'скидки',
            'местные бизнесы',
            'партнеры',
            'маркетинговая стратегия',
            'информация',
            'полезные советы',
            'обзор акций',
            'обновления',
            'локальный бизнес',
            'экономия',
            'покупки'
        ];

        $keywordsLt = [
            'CutCost',
            'naujienos',
            'straipsniai',
            'aktuali informacija',
            'nuolaidos',
            'vietiniai verslai',
            'partneriai',
            'rinkodaros strategija',
            'informacija',
            'naudingi patarimai',
            'akcijų apžvalga',
            'atnaujinimai',
            'lokalus verslas',
            'taupymas',
            'pirkimai'
        ];

        $keywordsEn = [
            'CutCost',
            'news',
            'articles',
            'up-to-date information',
            'deals',
            'local businesses',
            'partners',
            'marketing strategy',
            'information',
            'useful tips',
            'deal reviews',
            'updates',
            'local business',
            'savings',
            'shopping'
        ];

        MetaService::setBasicMeta(
            "$titleRu | $titleLt | $titleEn",
            "$descriptionRu $descriptionLt $descriptionEn",
            null,
            implode(', ', array_merge($keywordsRu, $keywordsLt, $keywordsEn))
        );

        return Inertia::render('Information/News', [
            'articles' => $paginatedArticles,
        ]);
    }


    public function show(Article $article)
    {
        $this->incrementViews($article);

        $article->load('company:id,slug,name', 'media');
        MetaService::setMetaForModel($article);

        return Inertia::render('Information/ShowArticle', [
            'article' => new ArticleResource($article),
        ]);
    }
}
