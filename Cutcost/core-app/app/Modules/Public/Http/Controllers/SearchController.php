<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Public\Services\Pagination\PaginateCompanyService;
use App\Modules\Public\Services\Pagination\PaginateCouponService;
use App\Modules\Public\Services\Pagination\PaginateLocationService;
use App\Modules\Public\Services\Pagination\PaginatePostService;
use App\Modules\Public\Services\Pagination\PaginateUserService;
use App\Modules\Public\Services\SearchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SearchController
{
    public function index(Request $request)
    {
        $validated = $request->validate([
            'search' => 'nullable|string|between:1,128',
        ]);

        $coupons = [];
        $users = [];
        $locations = [];
        $companies = [];

        if (isset($validated['search']) && $validated['search']) {
            $companyService = app(PaginateCompanyService::class);
            $userService = app(PaginateUserService::class);
            $locationService = app(PaginateLocationService::class);
            $couponService = app(PaginateCouponService::class); 

            $companies = $companyService->paginateCompanies(null, ['search_query' => $validated['search']]);
            $users = $userService->paginateUsers(null, ['search_query' => $validated['search']]);
            $locations = $locationService->paginateLocationFeed(null, ['search_query' => $validated['search']]);
            $coupons = $couponService->paginateFeed(null, ['search_query' => $validated['search']]);
        }

        return Inertia::render('Search/AdvancedSearch', [
            'coupons' => $coupons,
            'users' => $users,
            'locations' => $locations,
            'companies' => $companies,
        ]);
    }

    public function searchUsers(Request $request, SearchService $searchService): JsonResponse
    {
        $validated = $request->validate([
            'search' => 'required|string|between:1,128',
        ]);

        $users = $searchService
            ->searchUsers($validated['search'])->limit(5)->get();

        return response()->json([
            'users' => $users ?? [],
        ]);
    }

    public function searchCoupons(Request $request, SearchService $searchService): JsonResponse
    {
        $validated = $request->validate([
            'search' => ['required', 'string', 'between:1,68'],
        ]);

        $coupons = $searchService
            ->searchCoupons($validated['search'])->limit(5)
            ->with('currency', 'media')->get();

        return response()->json([
            'coupons' => $coupons ?? [],
        ]);
    }
}
