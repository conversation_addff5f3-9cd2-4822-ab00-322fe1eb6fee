<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Partner\Http\Resources\CompanyResource;
use App\Modules\Partner\Models\Company;
use App\Shared\Services\MetaService;
use Inertia\Inertia;

class ShowCompanyController
{
    public function show(
        string $companySlug
    ) {

        $company = Company::where('slug', $companySlug)
            ->withRelations()->first();

        $status = null;

        if (! $company) {
            $status = 404;
        } elseif ($company->user->is_banned) {
            $status = 403;
        } else {
            MetaService::setMetaForModel($company);
        }

        return Inertia::render('Discount/ShowCompany', [
            'company' => $status ? null : new CompanyResource($company),
            'status' => $status,
        ]);
    }
}
