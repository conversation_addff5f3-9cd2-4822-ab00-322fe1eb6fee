<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Partner\Http\Resources\CompanyMiniResource;
use App\Modules\Partner\Http\Resources\CouponResource;
use App\Modules\Partner\Http\Resources\LocationResource;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Location;
use App\Modules\Public\Services\InteractionService;
use App\Shared\Services\MetaService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class ShowCouponController
{

    public function index(
        Request $request,
        string $couponSlug,
        InteractionService $interactionService
    ): Response|RedirectResponse {
        try {
            $status = null;

            $coupon = Coupon::where('slug', $couponSlug)
                ->withRelations()->first();

            if (!$coupon) {
                $status = 404;
            } elseif ($coupon->status !== 'published' || $coupon->user->is_banned) {
                $status = 403;
            } else {
                $interactionService->incrementViews($coupon, $request->ip());
                MetaService::setMetaForModel($coupon);
            }

            return Inertia::render('Discount/ShowCoupon', [
                'coupon' => $status ? null : new CouponResource($coupon),
                'company' => $status ? null : new CompanyMiniResource(
                    Company::where('id', $coupon->company_id)
                        ->withMiniRelations()
                        ->first()
                ),
                'status' => $status,
                'locations' => $status ? null : LocationResource::collection(Location::whereIn('id', $coupon->locations->pluck('id'))->withExists([
                    'bookmarks as is_bookmarked' => fn($q) => $q->where('user_id', $request->user()?->id),
                ])->get()),
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return to_route('feed')->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.coupon')]));
        }
    }
}
