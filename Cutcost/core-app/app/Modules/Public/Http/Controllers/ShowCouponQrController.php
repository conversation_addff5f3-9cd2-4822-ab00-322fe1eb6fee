<?php

namespace App\Modules\Public\Http\Controllers;

use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Traits\ReferralTrait;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class ShowCouponQrController
{
    use ReferralTrait;

    public function show(
        Coupon $coupon,
    ): Response|RedirectResponse {


        if ($coupon->status !== 'published')
            return to_route('coupon.view', ['coupon' => $coupon->slug]);

        return Inertia::render(
            'Discount/ShowQrCode',
            [
                'coupon' => $coupon->loadMissing('currency', 'media'),
            ]
        );
    }
}
