<?php

namespace App\Modules\Public\Http\Controllers;

use App\Shared\Services\MetaService;
use Inertia\Inertia;

class SupportController
{
    /**
     * Display the support ticket category selection page
     */
    public function index()
    {
        MetaService::setBasicMeta(
            'CutCost — Support Center',
            'Get help with your CutCost account. Select a category to describe your issue and get the right support.',
            null,
            'CutCost, support, help, ticket, customer service, technical support, billing, account help'
        );

        $ticketCategories = $this->getTicketCategories();

        return Inertia::render('Support/Index', [
            'categories' => $ticketCategories,
        ]);
    }

    /**
     * Get hardcoded ticket categories
     */
    private function getTicketCategories(): array
    {
        return [
            [
                'id' => 'technical-issue',
                'title' => 'Technical Issue',
                'description' => 'Problems with the app, website, or features not working properly',
                'icon' => 'bug',
                'examples' => [
                    'App crashes or freezes',
                    'Cannot log in to account',
                    'Features not working',
                    'Website loading issues'
                ]
            ],
            [
                'id' => 'billing-question',
                'title' => 'Billing Question',
                'description' => 'Questions about payments, charges, refunds, or subscription issues',
                'icon' => 'credit-card',
                'examples' => [
                    'Unexpected charges',
                    'Refund requests',
                    'Payment method issues',
                    'Subscription problems'
                ]
            ],
            [
                'id' => 'account-help',
                'title' => 'Account Help',
                'description' => 'Issues with your account, profile, verification, or settings',
                'icon' => 'user',
                'examples' => [
                    'Account verification',
                    'Profile settings',
                    'Password reset',
                    'Account deletion'
                ]
            ],
            [
                'id' => 'feature-request',
                'title' => 'Feature Request',
                'description' => 'Suggestions for new features or improvements to existing ones',
                'icon' => 'lightbulb',
                'examples' => [
                    'New feature ideas',
                    'Improvement suggestions',
                    'User experience feedback',
                    'Integration requests'
                ]
            ],
            [
                'id' => 'other',
                'title' => 'Other',
                'description' => 'General questions or issues that don\'t fit other categories',
                'icon' => 'question',
                'examples' => [
                    'General inquiries',
                    'Partnership questions',
                    'Media requests',
                    'Other concerns'
                ]
            ]
        ];
    }
}
