<?php

namespace App\Modules\Public\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'slug' => $this->slug,
            'media' => $this->media,
            'content' => $this->content,
            'company_id' => $this->company_id,
            'created_at' => $this->created_at->toDateTimeString(),
            'views' => $this->views,
            'company' => $this->whenLoaded('company'),
        ];
    }
}
