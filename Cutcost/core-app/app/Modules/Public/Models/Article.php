<?php

namespace App\Modules\Public\Models;

use App\Shared\Models\Media;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'content', 'slug'];
    protected $with = ['media'];

    public function media()
    {
        return $this->morphMany(Media::class, 'model')
            ->orderBy('order_column');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
