<?php

namespace App\Modules\Public\Models;

use App\Shared\Models\Media;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'content',
        'slug',
        'type',
        'category',
        'excerpt',
        'is_published',
        'order',
        'meta'
    ];

    protected $with = ['media'];

    protected $casts = [
        'is_published' => 'boolean',
        'meta' => 'array',
        'views' => 'integer',
        'order' => 'integer',
    ];

    // Article types
    const TYPE_ARTICLE = 'article';
    const TYPE_HELP = 'help';
    const TYPE_FAQ = 'faq';

    public function media()
    {
        return $this->morphMany(Media::class, 'model')
            ->orderBy('order_column');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // Scopes
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    public function scopeInCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('order')->orderBy('created_at', 'desc');
    }

    public function scopeHelpArticles(Builder $query): Builder
    {
        return $query->ofType(self::TYPE_HELP)->published()->ordered();
    }

    public function scopeFaqs(Builder $query): Builder
    {
        return $query->ofType(self::TYPE_FAQ)->published()->ordered();
    }

    // Helper methods
    public function isHelpArticle(): bool
    {
        return $this->type === self::TYPE_HELP;
    }

    public function isFaq(): bool
    {
        return $this->type === self::TYPE_FAQ;
    }

    public function incrementViews(): void
    {
        $this->increment('views');
    }

    // Accessors
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('M j, Y');
    }

    public function getReadingTimeAttribute(): int
    {
        $wordCount = str_word_count(strip_tags($this->content));
        return max(1, ceil($wordCount / 200)); // Assuming 200 words per minute
    }
}
