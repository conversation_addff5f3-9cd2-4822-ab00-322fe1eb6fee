<?php

namespace App\Modules\Public\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Cache;

class UserNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private array $data,
        private string $type,
    ) {
    }

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        return $this->data;
    }

    public function databaseType(object $notifiable): string
    {
        return $this->type;
    }
}
