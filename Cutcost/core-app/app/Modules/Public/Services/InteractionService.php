<?php

namespace App\Modules\Public\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Shared\Repositories\CacheRepository;
final class InteractionService
{
    public static function toggleLike(Model $model, int $userId): void
    {
        DB::transaction(function () use ($model, $userId) {
            $like = $model->likes()->where('user_id', $userId)->first();

            if ($like) {
                $like->delete();
            } else {
                $model->likes()->create([
                    'user_id' => $userId,
                    'value' => 1,
                ]);
            }
        });
    }

    public static function toggleBookmark(Model $model, int $userId): void
    {
        DB::transaction(function () use ($model, $userId) {
            $bookmark = $model->bookmarks()->where('user_id', $userId)->first();

            if ($bookmark) {
                $bookmark->delete();
            } else {
                $model->bookmarks()->create([
                    'user_id' => $userId,
                ]);
            }
        });
    }

    public function incrementViews(Model $model, $ip): void
    {
        $cache = app(CacheRepository::class);

        if ($cache->isViewed($model, $ip)) return;

        $model->increment('views');
    }

    public static function getCurrentRating(Model $model, ?int $userId): int
    {
        if (empty($userId)) {
            return 0;
        }

       return $model->likes()->where('user_id', $userId)->exists() ? 1 : 0;

    }

}
