<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Public\Contracts\AppPaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CouponPaginator implements AppPaginator
{
    protected int $perPage = 12;
    protected array $withFilters;

    public function __construct(array $filters = [])
    {
        $this->perPage = config('cutcost.pagination.per_page');

        $this->withFilters = $filters;
    }

    public function scrollPaginate(Builder|Relation $query, ?int $cursor = null): Collection
    {
        $userId = Auth::id();

        // todo move to model
        $query = $query->withRelations()
            ->when($userId, function ($q) use ($userId) {
                // Authenticated user:
                // - Show all coupons with status != 'draft'
                // - Also include coupons with status = 'draft' if they belong to this user
                $q->where(function ($q) use ($userId) {
                    $q->where('status', '!=', 'draft')
                        ->orWhere(function ($q2) use ($userId) {
                            $q2->where('status', 'draft')
                                ->where('coupons.user_id', $userId);
                        });
                });
            }, function ($q) {
                $q->where('status', '!=', 'draft');
            });


        if ($this->checkFilters())
            $query = $this->filters($query);

        if ($cursor)
            $query->where('coupons.id', '<', $cursor);

        $query->withCount(['likes', 'comments']);

        $query->whereHas('user', function ($q) {
            $q->where('is_banned', false);
        });

        $query->take($this->perPage);

        return $query->get();
    }

    public function filters(Builder|Relation $query): Builder|Relation
    {
        if (! $this->checkFilters()) {
            return $query;
        }

        $cityId     = data_get($this->withFilters, 'city');
        // coordinates
        $near     = data_get($this->withFilters, 'near');
        $category = data_get($this->withFilters, 'category');

        $lng = data_get($near, 0);
        $lat = data_get($near, 1);

        $search = data_get($this->withFilters, 'search_query');

        if ($lng && $lat) {
            $pointExpr = "ST_GeomFromText(CONCAT('POINT(', ?, ' ', ?, ')'), 4326)";

            return $query->selectRaw(
                "coupons.*,
             ST_AsText(locations.lnglat) as location_point_text,
             MIN(ST_Distance_Sphere(locations.lnglat, {$pointExpr})) as distance",
                [$lng, $lat]
            )
                ->join('coupon_location', 'coupon_location.coupon_id', '=', 'coupons.id')
                ->join('locations', 'locations.id', '=', 'coupon_location.location_id')
                ->groupBy('coupons.id', 'locations.lnglat')
                ->having('distance', '<', 100000)
                ->orderBy('distance', 'asc');
        }

        if ($cityId) {
            $query->whereHas('locations', fn($q) => $q->where('city_id', $cityId));
        }
        if ($category) {
            $query->whereHas('company', fn($q) => $q->whereHas('industries', fn($q2) => $q2->where('industries.id', $category)));
        }
        
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('coupons.name', 'like', "%{$search}%")
                    ->orWhere('coupons.benefits', 'like', "%{$search}%");
            });
        }
        return $query;
    }

    public function checkFilters(): bool
    {
        return ! empty($this->withFilters);
    }
}
