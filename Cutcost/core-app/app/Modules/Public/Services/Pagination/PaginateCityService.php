<?php

namespace App\Modules\Public\Services\Pagination;

use App\Shared\Models\City;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

final class PaginateCityService
{
    private array $filters = [];

    public function paginateCities(?int $cursor, array $filters)
    {
        $this->filters = $filters;

        return $this->prepare(City::query(), $cursor)
            ->get();
    }

    private function applyFilters(Builder $query): Builder
    {
        
        if (data_get($this->filters, 'ids')) {
            return $query->whereIn('id', $this->filters['ids']);
        }

        if (data_get($this->filters, 'country_code') === 'WW') {
            $this->filters['country_code'] = null;
        }

        $search = data_get($this->filters, 'search_query');
        $countryCode = data_get($this->filters, 'country_code');

        // фильтр по поисковому запросу
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "{$search}%")
                    ->orWhere('alternate_names', 'like', "%{$search}%");
            });
        }

        if ($countryCode) {
            $query->where('country_code', $countryCode);
        }

        return $query;
    }

    private function prepare(Builder $query, ?int $cursor = null): Builder
    {
        $query = $this->applyFilters($query);

        return $query->when($cursor, fn($q) => $q->where('id', '<', $cursor))
            ->orderBy('id', 'desc')
            ->limit(config('cutcost.pagination.per_page'));
    }
}
