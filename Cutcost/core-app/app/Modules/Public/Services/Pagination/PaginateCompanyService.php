<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Partner\Models\Company;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class PaginateCompanyService extends Paginator
{
    public string $orderColumn = 'companies.id';
    public string $orderDirection = 'desc';
    public string $searchColumn = 'companies.name';

    public function paginateUserCompanies(int $userId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Company::query()
            ->where('user_id', $userId)
            ->with(['subscribers:id', 'country', 'currency']);

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateUserFollowingCompanies(int $userId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Company::query()
            ->whereHas('subscribers', fn($q) => $q->where('user_id', $userId))
            ->with(['subscribers:id', 'country', 'currency']);

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateCompanies(?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Company::query()
            ->with(['subscribers:id', 'country', 'currency']);

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }
}
