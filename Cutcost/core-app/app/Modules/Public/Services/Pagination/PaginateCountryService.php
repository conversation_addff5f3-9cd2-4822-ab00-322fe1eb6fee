<?php

namespace App\Modules\Public\Services\Pagination;

use App\Shared\Models\Country;
use Illuminate\Support\Facades\Log;

final class PaginateCountryService extends Paginator
{
    public function paginateCountries(?int $cursor, array $filters)
    {
        $query = Country::query();
        $this->filters = $filters;

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)
            ->get();
    }
}
