<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Location;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

final class PaginateCouponService extends CouponPaginator
{
    public function paginateFeed(?int $cursor = null, array $filters): Collection
    {
        $this->withFilters = $filters;

        $query = Coupon::active()
            ->latest();

        return $this->scrollPaginate($query, $cursor);
    }

    public function paginateBookmars(?int $cursor = null): Collection
    {
        $query = Coupon::whereHas('bookmarks', function ($q) {
            $q->where('user_id', Auth::id())
                ->where('bookmarkable_type', Coupon::class);
        });

        return $this->scrollPaginate($query, $cursor);
    }

    public function paginateCompanyCoupons(Company $company, ?int $cursor = null): Collection
    {
        $query = $company->coupons()
            ->latest();

        return $this->scrollPaginate($query, $cursor);
    }

    public function paginateLocationCoupons(Location $location, ?int $cursor = null)
    {
        $query = $location->coupons()->active()->latest();

        return $this->scrollPaginate($query, $cursor);
    }

    public function paginateSearchedCoupons($query): Collection
    {
        return $this->scrollPaginate($query);
    }
}
