<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Location;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class PaginateLocationService extends Paginator
{
    public string $orderColumn = 'locations.id';
    public string $orderDirection = 'desc';
    public string $searchColumn = 'locations.name'; 

    public function paginateCompanyLocations(Company $company, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = $company->locations()
            ->whereHas('user', fn($q) => $q->where('is_banned', false))
            ->withRelations();

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateLocationFeed(?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Location::active()
            ->whereHas('user', fn($q) => $q->where('is_banned', false))
            ->withRelations();

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateBookmarks(?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Location::query()
            ->whereHas('bookmarks', function ($q) {
                $q->where('user_id', Auth::id())
                    ->where('bookmarkable_type', Location::class);
            })
            ->whereHas('user', fn($q) => $q->where('is_banned', false))
            ->withRelations();

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }
}
