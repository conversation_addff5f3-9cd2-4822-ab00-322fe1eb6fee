<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Seller\Models\Post;
use App\Modules\Seller\Services\PreferencesService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class PaginatePostService extends Paginator
{
    public string $orderColumn = 'posts.id';
    public string $orderDirection = 'desc';
    public string $searchColumn = 'posts.content';

    public function paginateFeedPosts(?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $posts = $this->buildFeedQuery($cursor)->get();

        return $posts
            ->filter(fn($post) => app(PreferencesService::class)->canView($post->user))
            ->shuffle()
            ->values();
    }

    public function paginateUserPosts(int $userId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Post::withRelations()
            ->where('user_id', $userId);

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateBookmarks(?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = Post::withRelations()
            ->whereHas('bookmarks', function ($q) {
                $q->where('user_id', Auth::id())
                    ->where('bookmarkable_type', Post::class);
            });

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    private function buildFeedQuery(?int $cursor = null)
    {
        $query = Post::with([
            'user' => fn($q) => $q->withFriendStatus(Auth::user()),
        ])->withRelations();

        $query->whereHas('user', function ($q) {
            $q->where('is_banned', false);
        });

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor);
    }
}
