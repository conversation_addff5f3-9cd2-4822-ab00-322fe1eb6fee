<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Seller\Models\Friend;
use App\Shared\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class PaginateUserService extends Paginator
{
    public string $orderColumn = 'users.id';
    public string $orderDirection = 'desc';
    public string $searchColumn = 'users.display_name';

    public function paginateUserFriends(int $userId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = User::withFriendStatus(Auth::user())
            ->where(function ($q) use ($userId) {
                $q->whereHas('myFriends', function ($r) use ($userId) {
                    $r->where('friends.user_id', $userId);
                })->orWhereHas('myFriends', function ($r) use ($userId) {
                    $r->where('friends.friend_id', $userId);
                });
            })
            ->where('users.id', '<>', $userId);

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateCompanySubscribers(int $companyId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = User::withFriendStatus(Auth::user())
            ->whereHas('companiesSubscriptions', function ($q) use ($companyId) {
                $q->where('company_user.company_id', $companyId);
            });

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateUserSubscribers(int $userId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = User::withFriendStatus(Auth::user())
            ->whereHas('outgoingRequests', function ($q) use ($userId) {
                $q->where('friends.friend_id', $userId)
                    ->where('friends.status', Friend::STATUS_PENDING);
            });

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateUserFollowing(int $userId, ?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = User::withFriendStatus(Auth::user())
            ->whereHas('incomingRequests', function ($q) use ($userId) {
                $q->where('friends.user_id', $userId)
                    ->where('friends.status', Friend::STATUS_PENDING);
            });

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }

    public function paginateUsers(?int $cursor = null, array $filters = []): Collection
    {
        $this->filters = $filters;

        $query = User::query()->select([
            'users.id',
            'users.nickname',
            'users.display_name',
            'users.avatar',
        ]);

        $query = $this->baseFilters($query);

        return $this->paginate($query, $cursor)->get();
    }
}
