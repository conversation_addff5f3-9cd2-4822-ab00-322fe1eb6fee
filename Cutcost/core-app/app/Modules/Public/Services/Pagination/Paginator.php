<?php

namespace App\Modules\Public\Services\Pagination;

class Paginator
{
    public string $searchColumn = 'name';
    public ?array $filters = null;
    public string $orderColumn = 'id';
    public string $orderDirection = 'asc';

    public function baseFilters($query)
    {
        
        if (data_get($this->filters, 'ids')) {
            return $query->whereIn('id', $this->filters['ids']);
        }

        if (data_get($this->filters, 'search_query')) {
            return $query->where(
                $this->searchColumn,
                'like',
                "%{$this->filters['search_query']}%"
            );
        }

        return $query;
    }

    public function paginate($query, ?int $cursor)
    {
        return $query->when($cursor, fn($q) => $q->where(
            $this->orderColumn,
            $this->orderDirection === 'asc' ? '>' : '<',
            $cursor
        ))
            ->orderBy($this->orderColumn, $this->orderDirection)
            ->limit(config('cutcost.pagination.per_page'));
    }
}
