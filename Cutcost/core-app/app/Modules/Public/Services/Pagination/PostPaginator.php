<?php

namespace App\Modules\Public\Services\Pagination;

use App\Modules\Public\Contracts\AppPaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class PostPaginator implements AppPaginator
{

    protected int $perPage = 9;

    public function filters(Builder|Relation $query)
    {
        return $query;
    }

    public function checkFilters()
    {
        return false;
    }
}
