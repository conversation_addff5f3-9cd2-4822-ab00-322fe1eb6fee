<?php

namespace App\Modules\Public\Services;

use App\Modules\Partner\Models\Coupon;
use App\Shared\Models\User;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Collection;

final class SearchService
{
    public function findCoupons(int $id, string $type, string $query): Builder
    {
        // 6 - 1 for adding coupon as additional data
        $isSearchCoupon = $type === 'coupon';
        $quantity = $isSearchCoupon ? 6 : 2;

        return $this->searchCoupons($query)->limit($quantity);
    }

    public function findUsers(int $id, string $type, string $query): Builder
    {
        $isSearchUser = $type === 'user';
        $quantity = $isSearchUser ? 6 : 2;

        return $this->searchUsers($query)->limit($quantity);
    }

    public function searchCoupons(string $searchQuery): Builder
    {
        return Coupon::where(function ($query) use ($searchQuery) {
            $query->where('name', 'LIKE', "%{$searchQuery}%")
                ->orWhere('slug', 'LIKE', "%{$searchQuery}%");
        })->active();
    }

    public function searchUsers(string $searchQuery): Builder
    {
        return User::where(function ($query) use ($searchQuery) {
            $query->where('nickname', 'like', "%{$searchQuery}%")
                ->orWhere('email', 'like', "%{$searchQuery}%");
        })
            ->where('id', '!=', auth()?->id())
            ->select('id', 'nickname', 'avatar', 'display_name');
    }

    public function sortById(Collection $collection, int $id)
    {
        return $collection->sortBy(function ($item) use ($id) {
            return $item->id === $id ? 0 : 1;
        });
    }
}
