<?php

namespace App\Modules\Seller\Http\Controllers;

use App\Modules\Partner\Models\Company;
use Illuminate\Http\Request;

class CompanySubscriptionController
{
    public function subscribe(Request $request, Company $company)
    {
        $request->user()->companiesSubscriptions()->attach($company->id);
    }

    public function unsubscribe(Request $request, Company $company)
    {
        $request->user()->companiesSubscriptions()->detach($company->id);
    }
}
