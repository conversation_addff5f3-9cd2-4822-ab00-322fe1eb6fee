<?php

namespace App\Modules\Seller\Http\Controllers;

use Illuminate\Http\JsonResponse;
use App\Shared\Models\User;
use Illuminate\Support\Facades\Log;
use App\Modules\Seller\Services\FriendService;
use App\Shared\Repositories\CacheRepository;
use App\Shared\Services\NotificationService;
use Illuminate\Support\Facades\Auth;

class FriendController
{

    public function __construct(private CacheRepository $cacheRepository) {}

    public function cancelRequest(User $user, FriendService $friendService)
    {
        try {
            $result = $friendService->cancelRequest($user);
            $this->cacheRepository->forgetFollowingCount(Auth::id());
            $this->cacheRepository->forgetSubscriberCount($user->id);
            return response()->json($result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(__("app.flash.wrong", ['subject' => __('app.helpers.profile')]), 500);
        }
    }

    public function acceptFriendRequest(User $user, FriendService $friendService): JsonResponse
    {
        try {
            $result = $friendService->acceptFriendRequest($user);

            $this->cacheRepository->forgetSubscriberCount(Auth::id());
            $this->cacheRepository->forgetFriendCount(Auth::id());
            
            $this->cacheRepository->forgetFollowingCount($user->id);
            $this->cacheRepository->forgetFriendCount($user->id);
            return response()->json($result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(__("app.flash.wrong", ['subject' => __('app.helpers.profile')]), 500);
        }
    }

    public function deleteFriend(User $user, FriendService $friendService): JsonResponse
    {
        try {
            $result = $friendService->deleteFriend($user);

            $this->cacheRepository->forgetFriendCount(Auth::id());
            $this->cacheRepository->forgetSubscriberCount(Auth::id());

            $this->cacheRepository->forgetFriendCount($user->id);
            $this->cacheRepository->forgetFollowingCount($user->id);
            return response()->json($result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(__("app.flash.wrong", ['subject' => __('app.helpers.profile')]), 500);
        }
    }

    public function sendFriendRequest(User $user, FriendService $friendService): JsonResponse
    {
        try {
            $result = $friendService->sendFriendRequest($user);

            $this->cacheRepository->forgetFollowingCount(Auth::id());
            $this->cacheRepository->forgetSubscriberCount($user->id);

            // send notification

            app(NotificationService::class)->friendRequestNotification($user);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(__("app.flash.wrong", ['subject' => __('app.helpers.profile')]), 500);
        }
    }
}
