<?php

declare(strict_types=1);

namespace App\Modules\Seller\Http\Controllers;

use App\Modules\Partner\Http\Resources\CouponResource;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MyCouponsController 
{
    public function index()
    {
        try {

            return to_route('feed');
            $quantity = auth()->user()->myCoupons()
                ->distinct('coupon_user.coupon_id')->count();

            $coupons = $couponsService->paginateMyCoupons();

            return Inertia::render('Seller/MyCoupons', [
                'coupons' => CouponResource::collection($coupons),
                'quantity' => $quantity,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return to_route('feed')->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.coupons')]));
        }
    }
}
