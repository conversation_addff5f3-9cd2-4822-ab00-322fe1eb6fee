<?php

namespace App\Modules\Seller\Http\Controllers;

use App\Shared\Models\User;
use App\Shared\Repositories\CacheRepository;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NotificationsController
{
    public function index(Request $request)
    {
        $notifications = $request->user()->notifications()
            ->latest()->limit(15)->get();

        if ($notifications->count() && $notifications->first()?->read_at === null) {
            $request->user()->notifications()
                ->whereIn('id', $notifications->pluck('id')
                    ->toArray())->update(['read_at' => now()]);
        }

        $userIds = $notifications->map(function ($n) {
            return isset($n->data['constructor']['user_id'])
                ? $n->data['constructor']['user_id']
                : null;
        });

        $users = User::whereIn('id', $userIds->unique()->filter()->values()->toArray())
            ->withFriendStatus($request->user())->get();

        return Inertia::render('Seller/Notifications', [
            'notifications' => $notifications,
            'users' => $users,
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $request->user()->notifications()->where('id', $id)->delete();

        return response()->json(['status' => 'success']);
    }
}
