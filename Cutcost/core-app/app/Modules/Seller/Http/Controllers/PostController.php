<?php

declare(strict_types=1);

namespace App\Modules\Seller\Http\Controllers;

use App\Modules\Public\Services\InteractionService;
use App\Modules\Seller\Http\Requests\PostRequest;
use App\Modules\Seller\Http\Resources\PostResource;
use App\Modules\Seller\Models\Post;
use App\Modules\Seller\Services\PreferencesService;
use App\Shared\Repositories\CacheRepository;
use App\Shared\Services\MediaService;
use App\Shared\Services\NotificationService;
use App\Shared\Services\SEO\PostSEO;
use App\Shared\Services\TranslateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PostController
{
    // string $post - postSlug
    public function index(
        Request $request,
        string $post,
        PostSEO $seo,
        InteractionService $interactionService,
        PreferencesService $preferencesService
    ) {
        $post = Post::where('slug', $post)->withRelations()->first();
        $status = null;

        if (!$post) {
            $status = 404;
        }
        elseif ($post->user->is_banned || !$preferencesService->canView($post->user)) {
            $status = 403;
        } else {
            $seo->make($post);
            $interactionService->incrementViews($post, $request->ip());

        }

        return Inertia::render('Seller/Profile/ShowPost', [
            'post' => $status ? null : new PostResource($post),
            'status' => $status,
        ]);
    }

    public function store(
        PostRequest $request,
        MediaService $mediaService,
        CacheRepository $cacheRepository,
        TranslateService $translateService
    ) {
        $validated = $request->safe()->all();

        try {
            $mentions = $this->findMentions($validated['content']);

            $post = $request->user()->posts()->create([
                ...$validated,
                'base_locale' => $translateService->detect($validated['content']),
                'slug' => Str::uuid()->toString(),
            ]);

            if ($mentions)
                app(NotificationService::class)->mentionsNotification($mentions, $post, 'post');

            $mediaService->storeRequest(
                $validated['upload'],
                Post::class,
                $post->id
            );

            $cacheRepository->forgetPostCount($request->user()->id);

            return to_route('show.post', ['post' => $post->slug])
                ->with('success', __("app.flash.created", ['subject' => __('app.helpers.post')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.post')]));
        }
    }

    public function update(PostRequest $request, Post $post, MediaService $mediaService)
    {
        Gate::authorize('update', $post);
        $validated = $request->safe()->all();

        try {
            $post->update($validated);

            $mediaService->updateRequest(
                $validated['upload'],
                Post::class,
                $post->id
            );

            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.post')]));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.post')]));
        }
    }

    public function destroy(Post $post, CacheRepository $cacheRepository)
    {
        Gate::authorize('forceDelete', $post);
        try {
            $post->delete();

            $cacheRepository->forgetPostCount($post->user_id);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __("app.flash.wrong", ['subject' => __('app.helpers.post')]));
        }
    }

    public function create()
    {
        return Inertia::render('Seller/Post/CreatePost');
    }

    public function edit(Post $post)
    {
        Gate::authorize('update', $post);

        return Inertia::render('Seller/Post/EditPost', [
            'post' => new PostResource($post),
        ]);
    }

    public function getPost(string $postSlug)
    {
        return new PostResource(Post::where('slug', $postSlug)
            ->WithRelations()->firstOrFail());
    }

    private function findMentions(string $content): ?array
    {
        preg_match_all('/@[\w]+/', $content, $matches);

        if (!empty($matches[0])) {
            // Remove the @ from each mention
            $mentions = array_map(fn($mention) => ltrim($mention, '@'), $matches[0]);
            return $mentions;
        }

        return null;
    }
}
