<?php

declare(strict_types=1);

namespace App\Modules\Seller\Http\Controllers;

use App\Modules\Seller\Services\PreferencesService;
use App\Shared\Http\Resources\UserMiniResource;
use App\Shared\Http\Resources\UserResource;
use App\Shared\Models\User;
use App\Shared\Repositories\CacheRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ProfileController
{

    public function __construct(private PreferencesService $preferencesService) {}

    public function profile(Request $request)
    {
        try {
            $request->validate([
                'user' => 'sometimes|string|max:128',
            ]);

            $profileData = [];
            $asGuest = true;
            $user = null;
            $reachable = true;
            $status = null;

            if ($request->user && $request->user !== 'guest') {
                $user = User::withFriendStatus(Auth::user())
                    ->where('nickname', $request->user)
                    ->first();
            } else {
                $user = Auth::user();
                $asGuest = false;
            }
            
            if ($user && !$user->is_banned) {
                $reachable = $this->preferencesService->canView($user);
                $cacheRepository = app(CacheRepository::class);
                $asGuest = Auth::user()?->id !== $user->id;

                if (!$reachable) {
                    $user = $user->only(['id', 'nickname', 'display_name', 'avatar', 'preferences']);
                } else {
                    $profileData = [
                        'friends_count' => $cacheRepository->friendCount($user),
                        'subscribers_count' => $cacheRepository->subscriberCount($user),
                        'following_count' => $cacheRepository->followingCount($user),
                        'businesses_count' => $cacheRepository->companyCount($user),
                    ];
                }
            } elseif ($user && $user->is_banned) {
                $user = null;
                $status = 403;
            } else {
                $status = 404;
            }

            return Inertia::render('Seller/Profile', [
                'data' => $profileData,
                'as_guest' => $asGuest,
                'friend' => $asGuest && $user ? new UserResource($user) : null,
                'reachable' => $reachable,
                'status' => $status,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('warning', __("app.flash.wrong", ['subject' => __('app.helpers.profile')]));
        }
    }

    public function connections(User $user, string $type, Request $request)
    {
        try {

            if (!$this->preferencesService->canView($user))
                return to_route('profile', ['user' => $user->nickname]);

            if (!in_array($type, ['friends', 'subscribers', 'following']))
                abort(404);

            return Inertia::render('Seller/Profile/ShowConnections', [
                'type' => $request->type,
                'user' => new UserMiniResource($user),
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return to_route('feed')->with('warning', __("app.flash.wrong", ['subject' => __('app.helpers.profile')]));
        }
    }

    public function businesses(User $user)
    {
        try {

            if (!$this->preferencesService->canView($user))
                return to_route('profile', ['user' => $user->nickname]);

            return Inertia::render('Seller/Profile/ShowBusinesses', [
                'user' => new UserMiniResource($user),
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return to_route('feed')->with('warning', __("app.flash.wrong", ['subject' => __('app.helpers.profile')]));
        }
    }
}
