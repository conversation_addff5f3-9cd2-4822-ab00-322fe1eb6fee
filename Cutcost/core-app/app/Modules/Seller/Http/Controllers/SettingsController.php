<?php

namespace App\Modules\Seller\Http\Controllers;

use App\Modules\Seller\Http\Requests\UpdateProfileDetailsRequest;
use App\Shared\Models\City;
use App\Shared\Repositories\CacheRepository;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class SettingsController
{
    public function index(Request $request, CacheRepository $cacheRepository)
    {
        $pref = $cacheRepository->preferences(Auth::user());
        $city = null;

        if (isset($pref['city_id'])) {
            $city = City::where('id', $pref['city_id'])->first();
        }

        $sessions = DB::table('sessions')
            ->where('user_id', $request->user()->id)
            ->orderBy('last_activity', 'desc')
            ->get()
            ->map(function ($session) use ($request) {
                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $session->user_agent,
                    'last_active' => Carbon::createFromTimestamp($session->last_activity)->diffForHumans(),
                    'is_current_device' => $session->id === $request->session()->getId(),
                ];
            });

        return Inertia::render('Seller/Settings', [
            'locales' => $cacheRepository->getAllLocales(),
            'city' => $city,
            'sessions' => $sessions,
        ]);
    }

    public function updateProfileDetails(UpdateProfileDetailsRequest $request): RedirectResponse
    {
        try {

            $forbiddenWords = ['guest', 'admin', 'root', 'cutcost', 'manager', 'support'];

            if (array_filter($forbiddenWords, fn($word) => str_contains($request->nickname, $word))) {
                return back()->with('warning', 'Nickname contains forbidden word');
            }
            
            $request->user()->update(
                $request->safe()->all()
            );
            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.profile')]));
        } catch (\Throwable $e) {
            Log::error('Caught exception: ' . $e->getMessage());
            return back()->with('warning', __("app.flash.wrong", ['subject' => __('app.helpers.profile')]));
        }
    }

    public function updateProfilePrivacyPreferences(Request $request, CacheRepository $cacheRepository): RedirectResponse
    {
        try {
            $validated = $request->validate([
                'visibility' => 'required|in:everyone,friends,nobody',
                'messenger_visibility' => 'required|in:everyone,friends,nobody',
                'allow_comments' => 'boolean',
                'allow_personalized_ads' => 'boolean',
                'allow_email_notifications' => 'boolean',
                'store_prefrences_on_device' => 'boolean',
                'disable_unnecessary_logs' => 'boolean',
            ]);

            $request->user()->preferences = array_merge($request->user()->preferences ?? [], $validated);
            $request->user()->save();
            $cacheRepository->forgetPreferences($request->user()->id);
            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.profile')]));
        } catch (\Throwable $e) {
            Log::error('Caught exception: ' . $e->getMessage());
            return back()->with('warning', __("app.flash.wrong", ['subject' => __('app.helpers.profile')]));
        }
    }

    public function updateProfileLangAndRegion(Request $request, CacheRepository $cacheRepository): RedirectResponse
    {
        try {

            $validated = $request->validate([
                'translate_to' => 'nullable|string',
                'dont_translate' => 'nullable|array',
                'dont_translate.*' => 'required|string',
                'timezone' => 'required',
                'city_id' => 'nullable|numeric',
            ]);

            $request->user()->preferences = array_merge($request->user()->preferences ?? [], $validated);
            $request->user()->save();
            $cacheRepository->forgetPreferences($request->user()->id);
            return back()->with('success', __("app.flash.updated", ['subject' => __('app.helpers.settings')]));
        } catch (\Throwable $e) {
            Log::error('Caught exception: ' . $e->getMessage());
            return back()->with('warning', __("app.flash.wrong", ['subject' => __('app.helpers.settings')]));
        }
    }
}
