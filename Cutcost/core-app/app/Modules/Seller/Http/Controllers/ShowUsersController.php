<?php

namespace App\Modules\Seller\Http\Controllers;

use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;
use Inertia\Inertia;

class ShowUsersController
{

    //available types: user_friends, user_subscribers, user_companySubscribers
    public function index(string $type, string $model, int $id)
    {

        $relationName = null;
        $title = null;

        if ($model === 'company') {
            $relationName = Company::where('id', $id)->pluck('name')->first();
        }

        if ($model === 'user') {
            $relationName = User::where('id', $id)->pluck('display_name')->first();
        }

        $title = match ($type) {
            'user_friends' =>  'friends',
            'user_subscribers' => 'subscribers',
            'user_companySubscribers' =>  'subscribers',
            default => abort(404),
        };
        
        return Inertia::render('Seller/Profile/ShowUsersList', [
            'type' => $type,
            'data' => [$model => $id],
            'name' => $relationName,
            'title' => $title,
        ]);
    }
}
