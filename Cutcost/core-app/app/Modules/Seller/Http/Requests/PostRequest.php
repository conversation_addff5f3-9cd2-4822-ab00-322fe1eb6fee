<?php

namespace App\Modules\Seller\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PostRequest extends FormRequest
{

    public function rules(): array
    {

        
        return [
            'content' => 'required|string|between:5,1023',
            'upload.temp_uuid' => 'nullable|string|uuid',
            'upload.deleted_ids' => 'nullable|array',
            'repost_id' => 'nullable|exists:posts,id',
            'video' => [
                'nullable',
                'string',
                'regex:/^(https:\/\/www\.youtube\.com\/(shorts|watch\?v=|embed)\/?[a-zA-Z0-9_-]{11})/'
            ],
        ];
    }
}
