<?php

namespace App\Modules\Seller\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileDetailsRequest extends FormRequest
{

    public function rules(): array
    {

        $userId = $this->user()->id;

        return [
            'description' => 'nullable|min:3|max:128|string',
            'display_name' => 'required|between:2,16|string',
            // 'locales' => 'nullable|array',
            // 'locales.*' => 'required',
            // 'city_id' => 'required',
            'nickname' => [
                'required',
                'min:3',
                'max:16',
                'string',
                Rule::unique('users', 'nickname')->ignore($userId),
                'regex:/^[\p{L}][\p{L}\p{N}_ ]*$/u',
            ],
        ];
    }
}
