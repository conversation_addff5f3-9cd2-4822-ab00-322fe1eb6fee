<?php

namespace App\Modules\Seller\Http\Resources;

use App\Modules\Public\Services\InteractionService;
use App\Shared\Http\Resources\UserMiniResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class PostResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'media' => $this->media,
            'slug' => $this->slug,
            'created_at' => $this->created_at->toDateTimeString(),
            'content' => $this->content,
            'creator' => new UserMiniResource($this->user),
            'comments_count' => $this->comments_count,
            'likes_count' => $this->likes_count,
            'is_liked' => $this->is_liked,
            'is_bookmarked' => $this->is_bookmarked,
            'base_locale' => $this->base_locale,
            'views' => $this->views,
            'repost' => $this->whenLoaded('repost', function () {
                return new PostResource($this->repost);
            }),
            'repost_id' => $this->repost_id,
        ];
    }
}
