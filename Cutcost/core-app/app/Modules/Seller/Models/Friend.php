<?php

namespace App\Modules\Seller\Models;

use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Friend extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'friend_id',
        'status',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_ACCEPTED = 'accepted';

    // Владелец записи
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Друг
    public function friend()
    {
        return $this->belongsTo(User::class, 'friend_id');
    }
}
