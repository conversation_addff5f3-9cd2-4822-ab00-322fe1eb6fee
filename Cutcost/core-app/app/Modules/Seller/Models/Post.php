<?php

namespace App\Modules\Seller\Models;

use App\Shared\Models\Comment;
use App\Shared\Models\Locale;
use App\Shared\Models\Media;
use App\Shared\Models\User;
use App\Shared\Traits\Bookmarkable;
use App\Shared\Traits\Likable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Auth;

class Post extends Model
{
    use HasFactory, Likable, Bookmarkable;

    protected $fillable = [
        'content',
        'slug',
        'repost_id',
        'video',
        'base_locale',
        'views'
    ];

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable')
            ->whereNull('parent_id')
            ->latest();
    }

    public function media(): MorphMany
    {
        return $this->morphMany(Media::class, 'model')->orderBy('order_column');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function repost()
    {
        return $this->belongsTo(Post::class, 'repost_id');
    }

    public function reposts()
    {
        return $this->hasMany(Post::class, 'repost_id');
    }

    public function scopeWithRelations($query)
    {
        return $query->with([
            'media',
            'user:id,display_name,nickname,avatar,preferences,is_banned',
            'repost' => function ($q) {
                $q->with([
                    'media',
                    'user:id,display_name,nickname,avatar'
                ])
                    ->withCount(['likes', 'comments'])
                    ->withExists([
                        'bookmarks as is_bookmarked' => fn($q) => $q->where('user_id', Auth::id()),
                        'likes as is_liked' => fn($q) => $q->where('user_id', Auth::id()),
                    ]);
            }
        ])
            ->withCount(['likes', 'comments'])
            ->withExists([
                'bookmarks as is_bookmarked' => fn($q) => $q->where('user_id', Auth::id()),
                'likes as is_liked' => fn($q) => $q->where('user_id', Auth::id()),
            ]);
    }
}
