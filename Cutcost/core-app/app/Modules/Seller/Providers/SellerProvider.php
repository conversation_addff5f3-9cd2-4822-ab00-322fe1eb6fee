<?php

namespace App\Modules\Seller\Providers;

use App\Modules\Seller\Models\Post;
use App\Modules\Seller\Policies\PostPolicy;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class SellerProvider extends ServiceProvider
{
    public function register(): void {}

    public function boot(): void
    {
        Gate::policy(Post::class, PostPolicy::class);
    }
}
