<?php

namespace App\Modules\Seller\Services;

use App\Modules\Seller\Models\Friend;
use App\Shared\Contracts\Repositories\UserRepositoryContract;
use App\Shared\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

final class FriendService
{
    public function __construct(private UserRepositoryContract $userRepository) {}

    public function sendFriendRequest(User $friend): bool
    {
        $user = Auth::user();

        if ($friend->id === $user->id) return false;

        if ($user->outgoingRequests()->where('friend_id', $friend->id)->exists()) return false;

        $areFriends = Friend::where(function ($q) use ($user, $friend) {
            $q->where('user_id', $user->id)->where('friend_id', $friend->id);
        })->orWhere(function ($q) use ($user, $friend) {
            $q->where('user_id', $friend->id)->where('friend_id', $user->id);
        })->where('status', Friend::STATUS_ACCEPTED)->exists();

        if ($areFriends) return false;

        $now = now();

        $inserted = DB::table('friends')->insertOrIgnore([
            'user_id'    => $user->id,
            'friend_id'  => $friend->id,
            'status'     => Friend::STATUS_PENDING,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        if ($inserted === 0) {
            return false;
        }

        return true;
    }


    public function acceptFriendRequest(User $requester): bool
    {
        $user = Auth::user();

        $pending = Friend::where('user_id', $requester->id)
            ->where('friend_id', $user->id)
            ->where('status', Friend::STATUS_PENDING)
            ->first();

        if (!$pending) return false;

        DB::transaction(function () use ($pending, $user, $requester) {
            $pending->update(['status' => Friend::STATUS_ACCEPTED]);

            Friend::create([
                'user_id' => $user->id,
                'friend_id' => $requester->id,
                'status' => Friend::STATUS_ACCEPTED,
            ]);
        });

        return true;
    }

    public function deleteFriend(User $friend): bool
    {
        $user = Auth::user();

        $existing = Friend::where(function ($q) use ($user, $friend) {
            $q->where('user_id', $user->id)->where('friend_id', $friend->id);
        })->orWhere(function ($q) use ($user, $friend) {
            $q->where('user_id', $friend->id)->where('friend_id', $user->id);
        })->where('status', Friend::STATUS_ACCEPTED)->exists();

        if (!$existing) return false;

        DB::transaction(function () use ($user, $friend) {
            Friend::where('user_id', $user->id)
                ->where('friend_id', $friend->id)
                ->where('status', Friend::STATUS_ACCEPTED)
                ->delete();

            Friend::where('user_id', $friend->id)
                ->where('friend_id', $user->id)
                ->where('status', Friend::STATUS_ACCEPTED)
                ->update(['status' => Friend::STATUS_PENDING]);
        });

        return true;
    }

    public function cancelRequest(User $friend): bool
    {
        $user = Auth::user();

        $exists = $user->outgoingRequests()
            ->wherePivot('status', Friend::STATUS_PENDING)
            ->where('friend_id', $friend->id)
            ->exists();

        if (!$exists) return false;

        $user->outgoingRequests()->detach($friend->id);

        return true;
    }
}
