<?php

namespace App\Modules\Seller\Services;

use App\Shared\Models\User;
use Illuminate\Support\Facades\Auth;

final class PreferencesService
{
    public function canView(?User $user): bool
    {

        if (!$user) return false;

        if ($user?->id === Auth::id()) return true;

        $visibility = $user->preferences['visibility'] ?? 'everyone';

        if ($visibility === 'everyone') return true;

        if ($visibility === 'nobody') return false;

        if ($visibility === 'friends') {

            if ($user->isFriend(Auth::user()))
                return true;


            return false;
        }

        return false;
    }
}
