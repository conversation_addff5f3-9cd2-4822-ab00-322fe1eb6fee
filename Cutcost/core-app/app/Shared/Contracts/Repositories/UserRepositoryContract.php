<?php

namespace App\Shared\Contracts\Repositories;

use App\Shared\Models\User;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Collection;

interface UserRepositoryContract
{
    // all users
    public function getAll(): Collection;

    public function getUserWithCompanies(User $user): User;

    public function getUserPosts(User $user): Collection;

    public function getUserFriends(User $user): Builder;

    public function getUserRequests(User $user): Builder;

    public function getUserCompanies(User $user): Builder;

    public function getUserSubscribers(User $user): Builder;
}
