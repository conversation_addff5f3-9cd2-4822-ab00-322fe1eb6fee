<?php

namespace App\Shared\Enums;

enum CacheKeys: string
{
    case USER_POST_COUNT = 'user_posts_count_';
    case USER_COMPANY_COUNT = 'user_companies_count_';
    
    // all user locations
    case USER_COUPON_COUNT = 'user_coupons_count_';
    case USER_LOCATION_COUNT = 'user_locations_count_';
    case USER_SUBSCRIBER_COUNT = 'user_subscribers_count_';
    case USER_FRIEND_COUNT = 'user_friends_count_';
    case USER_FOLLOWING_COUNT = 'user_following_count_';

    case USER_PREFERENCES = 'user_preferences_';

    case COMPANY_SUBSCRIBER_COUNT = 'company_subscribers_count_';
    case COMPANY_COUPON_COUNT = 'company_coupons_count_';
    case COMPANY_LOCATION_COUNT = 'company_locations_count_';

    // static 
    case LOCALES = 'locales';
    case USED_COUNTRIES = 'used_countries';
}
