<?php

namespace App\Shared\Events;

use App\Shared\Models\User;
use App\Shared\Models\CutsTransaction;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReferralRewardEarned
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public CutsTransaction $transaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, CutsTransaction $transaction)
    {
        $this->user = $user;
        $this->transaction = $transaction;
    }
}
