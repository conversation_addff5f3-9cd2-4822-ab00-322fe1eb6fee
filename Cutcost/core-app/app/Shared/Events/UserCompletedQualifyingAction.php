<?php

namespace App\Shared\Events;

use App\Shared\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserCompletedQualifyingAction
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public string $actionType;
    public array $metadata;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, string $actionType, array $metadata = [])
    {
        $this->user = $user;
        $this->actionType = $actionType;
        $this->metadata = $metadata;
    }
}
