<?php

namespace App\Shared\Events;

use App\Shared\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRegisteredViaReferral
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public User $referrer;
    public string $referralCode;
    public ?string $ipAddress;
    public ?string $userAgent;

    public function __construct(
        User $user,
        User $referrer,
        string $referralCode,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ) {
        $this->user = $user;
        $this->referrer = $referrer;
        $this->referralCode = $referralCode;
        $this->ipAddress = $ipAddress;
        $this->userAgent = $userAgent;
    }
}
