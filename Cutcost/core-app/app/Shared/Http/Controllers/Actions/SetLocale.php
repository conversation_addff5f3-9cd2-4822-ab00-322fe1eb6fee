<?php

namespace App\Shared\Http\Controllers\Actions;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cookie;

class SetLocale 
{
    public function __invoke(string $locale)
    {
        if (! in_array($locale, ['en', 'lt', 'ru', 'pl', 'de', 'es'])) {
            abort(400);
        }

        //Inertia middleware
        App::setLocale($locale);
        Cookie::queue('locale', $locale, 60 * 24 * 365);
        return back();
    }
}
