<?php

declare(strict_types=1);

namespace App\Shared\Http\Controllers\Auth;

use App\Shared\Http\Requests\Auth\LoginUserRequest;
use App\Shared\Http\Requests\Auth\RegisterUserRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Shared\Services\AuthService;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

final class AuthController 
{
    public function __construct(private AuthService $authService) {}

    public function auth()
    {
        return Inertia::render('Auth/AuthProviders');
    }

    public function emailAuth()
    {
        // if (app()->isProduction()) abort(403);

        return Inertia::render('Auth/Auth');
    }

    public function register(RegisterUserRequest $request)
    {
        if (app()->isProduction()) abort(403);

        $validated = $request->validated();
        $validated['name'] = '';

        $this->authService->authUser($validated);
        Inertia::clearHistory();
        return redirect()->route('feed');
    }

    public function login(LoginUserRequest $request)
    {
        // if (app()->isProduction()) abort(403);

        $validated = $request->validated();

        if (Auth::attempt($validated, $request->remember)) {
            $request->session()->regenerate();
            Inertia::clearHistory();

            return redirect()->intended('/');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        Inertia::clearHistory();
        return to_route('feed');
    }

    public function destroySession(Request $request, $sessionId)
    {
        DB::table('sessions')
            ->where('user_id', $request->user()->id)
            ->where('id', $sessionId)
            ->delete();

        return back()->with('success', 'Session terminated successfully.');
    }

    public function destroyUser(Request $request)
    {
        $request->user()->delete();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        Inertia::clearHistory();

        return to_route('feed');
    }
}
