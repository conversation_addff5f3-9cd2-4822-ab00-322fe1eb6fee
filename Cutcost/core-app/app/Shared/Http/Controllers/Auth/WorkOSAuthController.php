<?php

namespace App\Shared\Http\Controllers\Auth;

use Illuminate\Http\Request;
use App\Shared\Services\AuthService;
use Illuminate\Support\Facades\Log;
use WorkOS\SSO; 
use Illuminate\Support\Str;
use Inertia\Inertia;

final class WorkOSAuthController 
{
    public const providers = [
        'GoogleOAuth',
        'MicrosoftOAuth',
        'GitHubOAuth',
        'AppleOAuth',
    ];

    public function __construct(
        private SSO $sso,
        private AuthService $authService
    ) {}


    public function redirectToProvider(int $providerId)
    {

        $provider = self::providers[$providerId - 1];

        $redirectUri = route('provider.callback');

        $authorizationUrl = $this->sso->getAuthorizationUrl(
            domain: null,
            provider: $provider,
            state: Str::random(10),
            redirectUri: $redirectUri,
        );

        return redirect($authorizationUrl);
    }

    public function callback(Request $request)
    {
        try {
            $code = $request->query('code');
            $profileAndToken = $this->sso->getProfileAndToken($code);
            $profile = $profileAndToken->profile;

            $this->authService->authUser([
                'email' => $profile->email,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            if (auth()->check()) {
                Inertia::clearHistory();
                return to_route('feed');
            } else {
                return to_route('feed');
            }
        }

        Inertia::clearHistory();
            
        return redirect()->intended('/feed');
    }
}
