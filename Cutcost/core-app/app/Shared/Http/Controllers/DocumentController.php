<?php

namespace App\Shared\Http\Controllers;

use App\Shared\Models\Document;
use App\Shared\Models\Locale;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class DocumentController 
{
    public function terms()
    {
        return Inertia::render('Footer/Document', [
            'document' => $this->getDocument('terms')
        ]);
    }

    public function privacy()
    {
        return Inertia::render('Footer/Document', [
            'document' => $this->getDocument('privacy')
        ]);
    }

    private function getDocument(string $type)
    {
        $localeCode = Cookie::get('locale', 'en');

        $localeId = Locale::where('code', $localeCode)
            ->pluck('id')->toArray();

        return Document::where('type', $type)
            ->where('locale_id', $localeId[0])->firstOrFail();
    }
}
