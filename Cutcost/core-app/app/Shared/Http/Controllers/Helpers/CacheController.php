<?php

namespace App\Shared\Http\Controllers\Helpers;

use App\Shared\Enums\CacheKeys;
use Illuminate\Http\Request;
use App\Shared\Services\CacheService as Cache;

final class CacheController
{

    public function requestKeys(Request $request)
    {
        $this->authorize($request->ip());

        return CacheKeys::cases();
    }

    public function clear(Request $request)
    {
        $this->authorize($request->ip());

        Cache::destroy($request->key);
    }

    public function set(Request $request)
    {
        $this->authorize($request->ip());

        Cache::use($request->key, function () use ($request) {
            return $request->value;
        });
    }

    private function authorize(?string $requestIp)
    {
        $serverIp = request()->server('SERVER_ADDR') ?? gethostbyname(gethostname());

        $allowedIps = [
            $serverIp,
            '127.0.0.1',
            '::1',
        ];

        if (!in_array($requestIp, $allowedIps, true)) {
            abort(403, 'Unauthorized');
        }
    }
}
