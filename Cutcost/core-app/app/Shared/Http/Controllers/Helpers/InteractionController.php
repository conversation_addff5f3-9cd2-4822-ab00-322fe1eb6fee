<?php

namespace App\Shared\Http\Controllers\Helpers;

use App\Modules\Public\Services\InteractionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class InteractionController
{
    public function like(Request $request, string $model, int $id)
    {
        $instance = resolve($this->getModelClass($model))->find($id);

        if (! $instance) {
            return response()->json('Record not found.', 404);
        }

        InteractionService::toggleLike($instance, $request->user()->id);
    }

    public function bookmark(Request $request, string $model, int $id)
    {
        $instance = resolve($this->getModelClass($model))->find($id);

        if (! $instance) {
            return response()->json('Record not found.', 404);
        }

        InteractionService::toggleBookmark($instance, $request->user()->id);
    }

    private function getModelClass(string $model): string
    {
        if (!isset(config('cutcost.model_paths')[$model])) {
            abort(400, 'Invalid model type');
        }

        return config('cutcost.model_paths')[$model];
    }   
}
