<?php

namespace App\Shared\Http\Controllers\Helpers;

use App\Shared\Models\Media;
use App\Shared\Services\MediaService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;

class MediaController 
{
    private MediaService $mediaService;

    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    /**
     * Загрузка фото:
     * - Если переданы model_type + model_id — сразу сохраняет к модели
     * - Иначе — сохраняет во временные с temp_uuid
     *
     * Ожидает:
     * - 'images'       => массив файлов
     * - 'model_type'   => string|null
     * - 'model_id'     => int|null
     * - 'temp_uuid'    => uuid|null
     */
    public function upload(Request $request): JsonResponse
    {
        $request->validate([
            'images'   => 'required|array',
            'images.*' => 'file|image|max:8192',
            'temp_uuid' => 'nullable|uuid',
            'model_type' => 'nullable|string|required_with:model_id',
            'model_id'   => 'nullable|integer|required_with:model_type',
        ]);

        try {


            $images    = $request->file('images');
            $modelType = $request->input('model_type');
            $modelId   = $request->input('model_id');
            $tempUuid  = $request->input('temp_uuid');

            $medias = $this->mediaService->store(
                $images,
                $modelType,
                $modelId,
                $tempUuid
            );

            // Если это временная загрузка — вернём temp_uuid
            $response = [
                'success' => true,
                'media'   => collect($medias)->map(fn($m) => [
                    'id'       => $m->id,
                    'path'     => $m->path,
                ]),
            ];

            if (empty($modelType) && empty($modelId)) {
                $response['temp_uuid'] = $medias[0]->temp_uuid;
            }

            return response()->json($response);
        } catch (\Throwable $e) {
            Log::error('Media upload error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Media upload failed',
            ], 500);
        }
    }

    public function sort(Request $request)
    {
        $request->validate([
            'temp_uuid'     => 'nullable|uuid',
            'ordered_ids'   => 'required|array',
            'ordered_ids.*' => 'integer|exists:media,id',
        ]);

        $orderedIds = $request->input('ordered_ids');
        $tempUuid = $request->input('temp_uuid');

        // Получаем все записи, чтобы взять path для upsert
        $mediaItems = Media::whereIn('id', $orderedIds)
            ->when($tempUuid, fn($q) => $q->where('temp_uuid', $tempUuid))
            ->get();

        // Формируем данные для upsert — обязательно с path
        $upsertData = $mediaItems->map(function ($media) use ($orderedIds) {
            return [
                'id' => $media->id,
                'order_column' => array_search($media->id, $orderedIds),
                'path' => $media->path,
            ];
        })->all();

        // Массово сохраняем
        Media::upsert($upsertData, ['id'], ['order_column']);

        return response()->json(['success' => true]);
    }


    /**
     * Удаление фото по id (одного или массива)
     */
    public function delete(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer',
        ]);

        try {
            $this->mediaService->deleteById($request->input('ids'));
            return response()->json(['success' => true]);
        } catch (\Throwable $e) {
            Log::error('Media delete error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Media delete failed',
            ], 500);
        }
    }
}
