<?php

namespace App\Shared\Http\Controllers\Helpers;

use App\Shared\Services\MediaService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

final class ModelMediaController
{
    private array $allowedColumns = ['avatar', 'cover'];
    private array $allowedTypes = ['user', 'company', 'group'];

    public function __construct(private MediaService $mediaService) {}

    public function change(Request $request)
    {
        $validated = $request->validate([
            'avatar' => 'nullable|max:9000|image',
            'action' => 'required|string|in:change,delete',
            'column' => 'required|string',
            'model_type' => 'required|string',
            'model_id' => 'required|integer',
        ]);

        $column = $validated['column'];
        $modelType = $validated['model_type'];
        $modelId = $validated['model_id'];

        // Проверка на допустимые значения
        if (!in_array($column, $this->allowedColumns)) {
            abort(400, 'Invalid column');
        }

        if (!in_array($modelType, $this->allowedTypes)) {
            abort(400, 'Invalid model type');
        }

        $model = $this->getModel($request, $modelType, $modelId);
        if (!$model) {
            abort(404, 'Model not found');
        }

        if ($validated['action'] === 'delete') {
            $this->deleteWithColumn($model, $column);
        } else {
            $this->updateWithColumn($model, $column, $request->file('avatar'));
        }
    }

    private function getModel(Request $request, string $type, int $id): ?Model
    {
        return match ($type) {
            'user' => $request->user(),
            'company' => $request->user()->companies()->findOrFail($id),
            'group' => $request->user()->groups()->findOrFail($id),
            default => null,
        };
    }

    private function updateWithColumn(Model $model, string $column, $image)
    {
        if (!$image) {
            abort(400, 'No image uploaded');
        }

        // Удаляем старый файл, если есть
        if ($model->$column) {
            $this->mediaService->deleteIfExists($model->$column);
        }

        $model->$column = $this->mediaService->storeIn($image, $column . 's');
        $model->save();
    }

    private function deleteWithColumn(Model $model, string $column): void
{
        if (!$model->$column) {
            abort(404, 'File not found');
        }

        $this->mediaService->deleteIfExists($model->$column);
        $model->$column = null;
        $model->save();
    }
}
