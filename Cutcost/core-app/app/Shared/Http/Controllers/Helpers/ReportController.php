<?php

namespace App\Shared\Http\Controllers\Helpers;

use App\Modules\Messenger\Repositories\ChatRepository;
use App\Modules\Messenger\Services\ChatService;
use App\Modules\Messenger\Services\MessageService;
use App\Shared\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class ReportController
{
    public function send(
        Request $request,
    ) {
        $validated =  $request->validate([
            'reason' => ['required', 'string'],
            'other' => ['nullable', 'string', 'between:3,256'],
            'model_type' => ['required', 'string'],
            'model_id' => ['required', 'numeric'],
        ]);

        try {
            Report::create([
                ...$validated,
                'reporter_id' => $request->user()->id,
            ]);

            return back()->with('success', 'Thank you for your report!');
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', __('messages.errors.wrong'));
        }
    }
}
