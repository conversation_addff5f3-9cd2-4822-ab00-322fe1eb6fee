<?php

namespace App\Shared\Http\Controllers\Helpers;

use App\Shared\Services\TranslateService;   
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class TranslationController
{
    
    public function translate(Request $request, TranslateService $translateService): JsonResponse
    {
        try {
            $validated = $request->validate([
                'text' => 'required|string',
                'source' => 'required|string',
                'target' => 'required|string',
            ]);
    
            $result = $translateService->translate($validated['text'], $validated['source'], $validated['target']);
    
            return response()->json($result);
            
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return response()->json($validated['text']);
        }
    }
    
}
