<?php

namespace App\Shared\Http\Controllers;

use App\Shared\Models\User;
use App\Shared\Models\CutsTransaction;
use App\Shared\Services\ReferralService;
use App\Shared\Services\RewardDistributionService;
use App\Shared\Events\UserCompletedQualifyingAction;
use App\Shared\Policies\ReferralPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class ReferralController 
{
    protected ReferralService $referralService;
    protected RewardDistributionService $rewardService;

    public function __construct(
        ReferralService $referralService,
        RewardDistributionService $rewardService
    ) {
        $this->referralService = $referralService;
        $this->rewardService = $rewardService;
    }

    /**
     * Get user's referral statistics
     */
    public function getStats(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        Gate::authorize('viewStats', [ReferralPolicy::class]);

        $stats = $this->referralService->getReferralStatistics($user);

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get referral leaderboard
     */
    public function getLeaderboard(Request $request): JsonResponse
    {
        Gate::authorize('viewLeaderboard', [ReferralPolicy::class]);

        $limit = $request->get('limit', 10);
        $leaderboard = $this->referralService->getReferralLeaderboard($limit);

        return response()->json([
            'success' => true,
            'data' => $leaderboard
        ]);
    }

    /**
     * Get user's cuts transaction history
     */
    public function getTransactions(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        Gate::authorize('viewTransactions', [ReferralPolicy::class]);

        $transactions = $user->cutsTransactions()
            ->with(['sourceUser:id,name,avatar'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions
        ]);
    }

    /**
     * Generate new referral code
     */
    public function generateCode(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        Gate::authorize('generateReferralCode', [ReferralPolicy::class]);

        $code = $user->generateReferralCode();

        return response()->json([
            'success' => true,
            'data' => [
                'referral_code' => $code,
                'referral_url' => $user->getReferralUrl()
            ]
        ]);
    }

    /**
     * Get referral chain details
     */
    public function getReferralChain(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        Gate::authorize('viewStats', [ReferralPolicy::class]);

        $chain = $this->referralService->getReferralChainDetails($user);

        return response()->json([
            'success' => true,
            'data' => $chain
        ]);
    }

    /**
     * Validate referral code (for registration)
     */
    public function validateCode(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|max:20'
        ]);

        $referrer = $this->referralService->validateReferralCode($request->code);

        if (!$referrer) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid referral code'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'valid' => true,
                'referrer' => [
                    'id' => $referrer->id,
                    'name' => $referrer->name,
                    'avatar' => $referrer->avatar
                ]
            ]
        ]);
    }

    /**
     * Trigger qualifying action (for testing/manual triggers)
     */
    public function triggerAction(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $request->validate([
            'action_type' => 'required|in:verification,first_coupon,coupon_scan,purchase',
            'metadata' => 'array'
        ]);

        // Only allow if user has permission or is admin
        if (!$user->hasRole('admin') && $user->id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        event(new UserCompletedQualifyingAction(
            $user,
            $request->action_type,
            $request->get('metadata', [])
        ));

        return response()->json([
            'success' => true,
            'message' => 'Action triggered successfully'
        ]);
    }

    /**
     * Get referral analytics (admin only)
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        Gate::authorize('viewAnalytics', [ReferralPolicy::class]);

        $analytics = [
            'total_referrals' => \App\Shared\Models\Referral::count(),
            'verified_referrals' => \App\Shared\Models\Referral::verified()->count(),
            'active_referrals' => \App\Shared\Models\Referral::withFirstCouponUsed()->count(),
            'total_cuts_distributed' => CutsTransaction::completed()->sum('amount'),
            'cuts_by_level' => CutsTransaction::completed()
                ->selectRaw('level, SUM(amount) as total')
                ->groupBy('level')
                ->pluck('total', 'level'),
            'cuts_by_type' => CutsTransaction::completed()
                ->selectRaw('type, SUM(amount) as total')
                ->groupBy('type')
                ->pluck('total', 'type'),
            'recent_activity' => CutsTransaction::with(['user:id,name', 'sourceUser:id,name'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }

    /**
     * Reverse a transaction (admin only)
     */
    public function reverseTransaction(Request $request, CutsTransaction $transaction): JsonResponse
    {
        Gate::authorize('reverseTransaction', [ReferralPolicy::class, $transaction]);

        try {
            $this->rewardService->reverseRewards($transaction);

            return response()->json([
                'success' => true,
                'message' => 'Transaction reversed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reverse transaction: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's referral profile
     */
    public function getProfile(Request $request, User $user): JsonResponse
    {
        Gate::authorize('viewReferralProfile', [ReferralPolicy::class, $user]);

        $profile = [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->avatar,
                'created_at' => $user->created_at
            ],
            'stats' => $this->referralService->getReferralStatistics($user),
            'recent_referrals' => $user->referrals()
                ->with('user:id,name,avatar,created_at')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $profile
        ]);
    }
}
