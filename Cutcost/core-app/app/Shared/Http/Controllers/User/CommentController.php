<?php

namespace App\Shared\Http\Controllers\User;

use App\Modules\Partner\Models\Coupon;
use App\Modules\Seller\Models\Post;
use App\Shared\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Shared\Traits\Helpers;

// Move logic to the service 
final class CommentController
{

    use Helpers;

    public function index(Request $request, ?string $class, $id)
    {
        $modelClass = $this->classFromType($class);
        $cursor = $request->input('cursor') ?? null;

        if (!$this->ensureModelAllowsComments($modelClass, $id))
            return response()->json(['message' => 'restricted']);

        $comments = Comment::where('commentable_type', $modelClass)
            ->where('commentable_id', $id)
            ->whereNull('parent_id')
            ->when($cursor, fn($q) => $q->where('id', '<', $cursor))
            ->with(['user:id,display_name,nickname,avatar', 'replies.user:id,display_name,nickname,avatar'])
            ->orderByDesc('id')
            ->limit(config('cutcost.pagination.per_page'))
            ->get();

        return response()->json([
            'comments' => $comments,
            'next_cursor' => $comments->last()?->id,
        ]);
    }

    public function store(Request $request, $class, $id)
    {
        $modelClass = $this->classFromType($class);

        $request->validate([
            'body' => 'required|string',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $comment = Comment::create([
            'body' => $request->body,
            'parent_id' => $request->parent_id,
            'user_id' => Auth::id(),
            'commentable_type' => $modelClass,
            'commentable_id' => $id,
        ]);

        $comment->load('user:id,display_name,nickname,avatar');

        return response()->json($comment, 201);
    }

    public function update(Request $request, Comment $comment)
    {

        if ($comment->user_id !== $request->user()->id) {
            abort(403);
        }

        $request->validate([
            'body' => 'required|string',
        ]);

        $comment->update([
            'body' => $request->body,
        ]);

        $comment->load('user:id,display_name,nickname,avatar');

        return response()->json($comment);
    }

    public function destroy(Request $request, Comment $comment)
    {
        $modelClass = $comment->commentable_type;

        $user = $request->user();
        
        $isCommentOwner = $comment->user_id === $user->id;
        $isAdmin = $user->hasRole('admin');
        $isModelOwner = $this->ownerOfModel($modelClass, $comment->commentable_id);

        if (!$isCommentOwner && !$isAdmin && !$isModelOwner) {
            abort(403);
        }

        $comment->delete();

        return response()->json(['message' => 'Comment deleted']);
    }

    private function ensureModelAllowsComments(string $modelClass, int|string $id): bool
    {
        $model = $modelClass::findOrFail($id);

        $owner = $model->user;

        if (!$owner) {
            return false;
        }

        $preferences = $owner->preferences;

        if (is_null($preferences) || !array_key_exists('allow_comments', $preferences)) {
            return true;
        }

        if ($preferences['allow_comments'] === false) {
            return false;
        }

        return true;
    }

    private function ownerOfModel($modelClass, $id): bool
    {
        $model = $modelClass::findOrFail($id);

        return $model->user_id === Auth::id();
    }

 }
