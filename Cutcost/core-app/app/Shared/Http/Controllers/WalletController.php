<?php

namespace App\Shared\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Shared\Models\CutsPlan;
use App\Shared\Models\PaymentMethod;
use App\Shared\Services\StripeService;
use App\Shared\Services\WalletService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class WalletController 
{
    protected WalletService $walletService;
    protected StripeService $stripeService;

    public function __construct(WalletService $walletService, StripeService $stripeService)
    {
        $this->walletService = $walletService;
        $this->stripeService = $stripeService;
    }

    /**
     * Display wallet dashboard.
     */
    public function index(): Response
    {
        $user = Auth::user();
        
        return Inertia::render('Wallet/Dashboard', [
            'wallet_overview' => $this->walletService->getWalletOverview($user),
            'transaction_history' => $this->walletService->getTransactionHistory($user, ['limit' => 20]),
            'wallet_statistics' => $this->walletService->getWalletStatistics($user),
            'payment_methods' => $user->paymentMethods()->active()->get(),
            'available_plans' => $this->walletService->getAvailablePlans(),
        ]);
    }

    /**
     * Display cuts plans page.
     */
    public function plans(): Response
    {
        $user = Auth::user();
        
        return Inertia::render('Wallet/Plans', [
            'plans' => $this->walletService->getAvailablePlans(),
            'user_subscription' => $user->getActiveSubscription(),
            'payment_methods' => $user->paymentMethods()->active()->get(),
            'wallet_balance' => $user->getWalletBalance(),
            'cuts_balance' => $user->getCutsBalance(),
        ]);
    }

    /**
     * Display payment verification page.
     */
    public function verification()
    {
        $user = Auth::user();
        
        if ($user->isPaymentVerified()) {
            return redirect()->route('wallet.index')
                ->with('success', 'Your account is already verified.');
        }
        
        return Inertia::render('Wallet/Verification', [
            'verification_plan' => $this->walletService->getVerificationPlan(),
            'payment_methods' => $user->paymentMethods()->active()->get(),
            'pending_verification' => $user->paymentVerifications()->pending()->latest()->first(),
        ]);
    }

    /**
     * Display transaction history page.
     */
    public function transactions(Request $request): Response
    {
        $user = Auth::user();
        
        $filters = $request->only(['type', 'status', 'date_from', 'date_to']);
        $filters['limit'] = 100;
        
        return Inertia::render('Wallet/Transactions', [
            'transactions' => $this->walletService->getTransactionHistory($user, $filters),
            'filters' => $filters,
            'transaction_types' => [
                'deposit' => 'Deposit',
                'withdrawal' => 'Withdrawal',
                'purchase' => 'Purchase',
                'refund' => 'Refund',
                'bonus' => 'Bonus',
                'referral' => 'Referral',
            ],
        ]);
    }

    /**
     * Create setup intent for adding payment method.
     */
    public function createSetupIntent(): \Illuminate\Http\JsonResponse
    {
        try {
            $user = Auth::user();
            $setupIntent = $this->stripeService->createSetupIntent($user);
            
            return response()->json([
                'success' => true,
                'client_secret' => $setupIntent->client_secret,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create setup intent.',
            ], 500);
        }
    }

    /**
     * Add payment method.
     */
    public function addPaymentMethod(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'payment_method_id' => 'required|string',
        ]);

        $user = Auth::user();
        $result = $this->walletService->addPaymentMethod($user, $request->payment_method_id);

        return response()->json($result);
    }

    /**
     * Remove payment method.
     */
    public function removePaymentMethod(PaymentMethod $paymentMethod): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $result = $this->walletService->removePaymentMethod($user, $paymentMethod);

        return response()->json($result);
    }

    /**
     * Set default payment method.
     */
    public function setDefaultPaymentMethod(PaymentMethod $paymentMethod): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $result = $this->walletService->setDefaultPaymentMethod($user, $paymentMethod);

        return response()->json($result);
    }

    /**
     * Process payment verification.
     */
    public function processVerification(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'payment_method_id' => 'required|string',
        ]);

        $user = Auth::user();
        $result = $this->walletService->processPaymentVerification($user, $request->payment_method_id);

        return response()->json($result);
    }

    /**
     * Purchase cuts plan.
     */
    public function purchasePlan(Request $request, CutsPlan $plan): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'payment_method_id' => 'required|string',
        ]);

        $user = Auth::user();
        $result = $this->walletService->purchaseCutsPlan($user, $plan, $request->payment_method_id);

        return response()->json($result);
    }

    /**
     * Cancel subscription.
     */
    public function cancelSubscription(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $result = $this->walletService->cancelSubscription($user);

        return response()->json($result);
    }

    /**
     * Handle Stripe webhook.
     */
    public function webhook(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');
            
            // Verify webhook signature
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                config('services.stripe.webhook_secret')
            );

            // Handle the event
            $handled = $this->stripeService->handleWebhook($event);

            return response()->json(['success' => $handled]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Return page after payment verification.
     */
    public function verificationReturn(Request $request): Response
    {
        $user = Auth::user();
        
        if ($user->isPaymentVerified()) {
            return Inertia::render('Wallet/VerificationSuccess', [
                'message' => 'Your account has been successfully verified!',
            ]);
        }
        
        return Inertia::render('Wallet/VerificationPending', [
            'message' => 'Your verification payment is being processed.',
        ]);
    }

    /**
     * Return page after cuts purchase.
     */
    public function purchaseReturn(Request $request): Response
    {
        return Inertia::render('Wallet/PurchaseSuccess', [
            'message' => 'Your cuts purchase was successful!',
        ]);
    }

    /**
     * Spend cuts (for internal use by other services).
     */
    public function spendCuts(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'amount' => 'required|integer|min:1',
            'description' => 'required|string|max:255',
            'metadata' => 'sometimes|array',
        ]);

        $user = Auth::user();
        $result = $this->walletService->spendCuts(
            $user,
            $request->amount,
            $request->description,
            $request->metadata ?? []
        );

        return response()->json($result);
    }

    /**
     * Get wallet balance (API endpoint).
     */
    public function balance(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        
        return response()->json([
            'wallet_balance' => $user->getWalletBalance(),
            'cuts_balance' => $user->getCutsBalance(),
            'formatted_wallet_balance' => $user->formatted_wallet_balance,
            'formatted_cuts_balance' => $user->formatted_cuts_balance,
        ]);
    }
}
