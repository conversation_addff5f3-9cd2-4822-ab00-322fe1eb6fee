<?php

namespace App\Shared\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;

class AuthMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            $previousUrl = url()->previous(route('feed'));

            $route = collect(Route::getRoutes())->first(function ($route) use ($previousUrl) {
                return $route->matches(request()->create($previousUrl, 'GET'));
            });

            if (array_any(
                Route::gatherRouteMiddleware($route),
                fn($middleware) => $middleware === AuthMiddleware::class
            )) {
                return to_route('feed', ['login' => 1]);
            }

            if (!str_contains($previousUrl, 'login=1')) {
                $parsedUrl = parse_url($previousUrl);

                $query = [];
                if (isset($parsedUrl['query'])) {
                    parse_str($parsedUrl['query'], $query);
                }

                $query['login'] = 1;

                $newUrl = ($parsedUrl['scheme'] ?? 'http') . '://' . ($parsedUrl['host'] ?? $request->getHost());

                if (isset($parsedUrl['port'])) {
                    $newUrl .= ':' . $parsedUrl['port'];
                }

                if (isset($parsedUrl['path'])) {
                    $newUrl .= $parsedUrl['path'];
                }

                $newUrl .= '?' . http_build_query($query);

                return redirect($newUrl);
            }

            return redirect($previousUrl);
        }


        return $next($request);
    }
}
