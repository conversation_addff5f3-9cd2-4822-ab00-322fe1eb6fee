<?php

namespace App\Shared\Http\Middleware;

use App\Shared\Http\Controllers\Actions\SetLocale;
use App\Shared\Http\Resources\UserResource;
use App\Shared\Repositories\CacheRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    protected $rootView = 'app';

    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    public function share(Request $request): array
    {

        App::setLocale(Cookie::get('locale', explode('_', $request->getPreferredLanguage())[0]));

        $user = $request->user();

        if ($request->header('X-User-Timezone'))
            Cookie::queue('timezone', $request->header('X-User-Timezone'), 60 * 24 * 365);

        // change languge with query
        if ($request->query('lang')) (new SetLocale())($request->query('lang'));

        if ($user && $user->is_banned)
            abort(423, 'User is banned');

        return array_merge(parent::share($request), [
            'notifications_count' => fn() => $user?->notifications()->where('read_at', null)->count(),
            'auth.user' => fn() => $user ? new UserResource($user) : null,
            'auth.roles' => fn() => $user
                ? Cache::rememberForever('auth_roles_' . $user->id, function () use ($user) {
                    return $user->roles->pluck('name');
                })
                : null,
            'flash' => [
                'info' => fn() => $request->session()->get('info'),
                'error' => fn() => $request->session()->get('error'),
                'success' => fn() => $request->session()->get('success'),
                'warning' => fn() => $request->session()->get('warning'),
            ],
        ]);
    }
}
