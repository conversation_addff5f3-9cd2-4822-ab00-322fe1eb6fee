<?php

declare(strict_types=1);

namespace App\Shared\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class LoginUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check() === false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $emailRules = ['required', 'email'];

        if (app()->isProduction()) {
            $emailRules[] = Rule::in([
                '<EMAIL>',
                '<EMAIL>',
            ]);
        }

        return [
            'email' => $emailRules,
            'password' => ['required'],
        ];
    }
}
