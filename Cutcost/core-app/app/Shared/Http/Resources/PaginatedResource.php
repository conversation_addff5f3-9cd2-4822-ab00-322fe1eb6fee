<?php

namespace App\Shared\Http\Resources;

class PaginatedResource
{
    public static function make($resourceClass, $paginator)
    {
        return [
            'data' => $resourceClass::collection($paginator->items()),
            'pagination' => [
                'total' => $paginator->total(),
                'per_page' => $paginator->perPage(),
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'next_page_url' => $paginator->nextPageUrl(),
                'prev_page_url' => $paginator->previousPageUrl(),
                'links' => $paginator->linkCollection()->toArray(),
            ],
        ];
    }
}
