<?php

namespace App\Shared\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserMiniResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nickname' => $this->is_banned ? 'guest' : $this->nickname,
            'display_name' => $this->is_banned ? 'banned' : $this->display_name,
            'avatar' => $this->is_banned ? null : $this->avatar,
            'friend_status' => $this->friend_status,
            'is_banned' => $this->is_banned,
        ];
    }
}
