<?php

namespace App\Shared\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nickname' => $this->nickname,
            'display_name' => $this->display_name,
            'avatar' => $this->avatar,
            'email' => $this->email,
            'description' => $this->description,
            'cover' => $this->cover,
            'cutstars' => $this->cutstars,
            'created_at' => $this->created_at,
            'preferences' => $this->preferences,
            'friend_status' => $this->friend_status,
        ];
    }
}
