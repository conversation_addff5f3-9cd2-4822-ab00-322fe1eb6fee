<?php

namespace App\Shared\Listeners;

use App\Shared\Events\UserRegisteredViaReferral;
use App\Shared\Models\Referral;
use App\Shared\Services\RewardDistributionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateReferralRecord implements ShouldQueue
{
    use InteractsWithQueue;

    protected RewardDistributionService $rewardService;

    /**
     * Create the event listener.
     */
    public function __construct(RewardDistributionService $rewardService)
    {
        $this->rewardService = $rewardService;
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisteredViaReferral $event): void
    {
        try {
            // Create referral record
            $referral = Referral::create([
                'user_id' => $event->user->id,
                'referrer_id' => $event->referrer->id,
                'referral_code' => $event->referralCode,
                'registered_at' => now(),
                'ip_address' => $event->ipAddress,
                'user_agent' => $event->userAgent,
            ]);

            // Distribute registration rewards
            $this->rewardService->distributeRegistrationRewards($referral);

            Log::info('Referral record created and registration rewards distributed', [
                'user_id' => $event->user->id,
                'referrer_id' => $event->referrer->id,
                'referral_code' => $event->referralCode,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create referral record', [
                'user_id' => $event->user->id,
                'referrer_id' => $event->referrer->id,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }
}
