<?php

namespace App\Shared\Listeners;

use App\Shared\Events\UserCompletedQualifyingAction;
use App\Shared\Models\CutsTransaction;
use App\Shared\Services\RewardDistributionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class DistributeActionRewards implements ShouldQueue
{
    use InteractsWithQueue;

    protected RewardDistributionService $rewardService;

    /**
     * Create the event listener.
     */
    public function __construct(RewardDistributionService $rewardService)
    {
        $this->rewardService = $rewardService;
    }

    /**
     * Handle the event.
     */
    public function handle(UserCompletedQualifyingAction $event): void
    {
        try {
            // Only process if user was referred
            if (!$event->user->wasReferred()) {
                return;
            }

            $referral = $event->user->referralRecord;
            if (!$referral) {
                Log::warning('User was referred but no referral record found', [
                    'user_id' => $event->user->id,
                ]);
                return;
            }

            // Handle different action types
            switch ($event->actionType) {
                case CutsTransaction::TYPE_VERIFICATION:
                    if (!$referral->hasVerificationReward()) {
                        $referral->markAsVerified();
                        $this->rewardService->distributeVerificationRewards($referral);
                    }
                    break;

                case CutsTransaction::TYPE_FIRST_COUPON:
                    if (!$referral->hasFirstActionReward()) {
                        $referral->markFirstCouponUsed();
                        $this->rewardService->distributeFirstActionRewards($referral);
                    }
                    break;

                case CutsTransaction::TYPE_COUPON_SCAN:
                    $this->rewardService->distributeCouponScanRewards($referral, $event->metadata);
                    break;

                case CutsTransaction::TYPE_PURCHASE:
                    $this->rewardService->distributePurchaseRewards($referral, $event->metadata);
                    break;
            }

            Log::info('Action rewards distributed', [
                'user_id' => $event->user->id,
                'action_type' => $event->actionType,
                'referral_id' => $referral->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to distribute action rewards', [
                'user_id' => $event->user->id,
                'action_type' => $event->actionType,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }
}
