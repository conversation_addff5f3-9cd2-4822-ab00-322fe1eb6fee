<?php

namespace App\Shared\Listeners;

use App\Shared\Events\ReferralRewardEarned;
use App\Shared\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotifyUserOfReward implements ShouldQueue
{
    use InteractsWithQueue;

    protected NotificationService $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle(ReferralRewardEarned $event): void
    {
        try {
            $transaction = $event->transaction;
            $user = $event->user;

            // Create notification message
            $message = $this->createNotificationMessage($transaction);

            // Send notification to user
            $this->notificationService->sendNotification(
                $user,
                'referral_reward',
                $message,
                [
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'type' => $transaction->type,
                    'level' => $transaction->level,
                ]
            );

            Log::info('Referral reward notification sent', [
                'user_id' => $user->id,
                'transaction_id' => $transaction->id,
                'amount' => $transaction->amount,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send referral reward notification', [
                'user_id' => $event->user->id,
                'transaction_id' => $event->transaction->id,
                'error' => $e->getMessage(),
            ]);

            // Don't re-throw as notification failure shouldn't break the reward process
        }
    }

    /**
     * Create notification message based on transaction
     */
    private function createNotificationMessage($transaction): string
    {
        $sourceUser = $transaction->sourceUser;
        $amount = $transaction->amount;
        $level = $transaction->level;

        $messages = [
            'registration' => "You earned {$amount} cuts! {$sourceUser->name} joined using your referral link (Level {$level})",
            'verification' => "You earned {$amount} cuts! {$sourceUser->name} verified their account (Level {$level})",
            'first_coupon' => "You earned {$amount} cuts! {$sourceUser->name} used their first coupon (Level {$level})",
            'coupon_scan' => "You earned {$amount} cuts! {$sourceUser->name} scanned a coupon (Level {$level})",
            'purchase' => "You earned {$amount} cuts! {$sourceUser->name} made a purchase (Level {$level})",
        ];

        return $messages[$transaction->type] ?? "You earned {$amount} cuts from referral activity!";
    }
}
