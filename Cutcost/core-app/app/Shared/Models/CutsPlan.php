<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CutsPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'features',
        'price',
        'cuts_amount',
        'bonus_cuts',
        'interval',
        'is_verification_plan',
        'verification_price',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
        'features' => 'array',
        'price' => 'decimal:2',
        'cuts_amount' => 'integer',
        'bonus_cuts' => 'integer',
        'verification_price' => 'decimal:2',
        'is_verification_plan' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Intervals
    const INTERVAL_ONE_TIME = 'one_time';
    const INTERVAL_MONTHLY = 'monthly';
    const INTERVAL_YEARLY = 'yearly';

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(CutsSubscription::class, 'plan_id');
    }

    /**
     * Scope for active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured plans.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for verification plans.
     */
    public function scopeVerification($query)
    {
        return $query->where('is_verification_plan', true);
    }

    /**
     * Scope for regular plans (not verification).
     */
    public function scopeRegular($query)
    {
        return $query->where('is_verification_plan', false);
    }

    /**
     * Scope for specific interval.
     */
    public function scopeInterval($query, string $interval)
    {
        return $query->where('interval', $interval);
    }

    /**
     * Get localized name.
     */
    public function getLocalizedName(string $locale = 'en'): string
    {
        return $this->name[$locale] ?? $this->name['en'] ?? $this->code;
    }

    /**
     * Get localized description.
     */
    public function getLocalizedDescription(string $locale = 'en'): string
    {
        return $this->description[$locale] ?? $this->description['en'] ?? '';
    }

    /**
     * Get total cuts (including bonus).
     */
    public function getTotalCutsAttribute(): int
    {
        return $this->cuts_amount + $this->bonus_cuts;
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return '€' . number_format($this->price, 2);
    }

    /**
     * Get price per cut.
     */
    public function getPricePerCutAttribute(): float
    {
        return $this->total_cuts > 0 ? $this->price / $this->total_cuts : 0;
    }

    /**
     * Get formatted price per cut.
     */
    public function getFormattedPricePerCutAttribute(): string
    {
        return '€' . number_format($this->price_per_cut, 4);
    }

    /**
     * Check if plan is recurring.
     */
    public function isRecurring(): bool
    {
        return in_array($this->interval, [self::INTERVAL_MONTHLY, self::INTERVAL_YEARLY]);
    }

    /**
     * Check if plan is one-time.
     */
    public function isOneTime(): bool
    {
        return $this->interval === self::INTERVAL_ONE_TIME;
    }

    /**
     * Get interval display name.
     */
    public function getIntervalDisplayAttribute(): string
    {
        return match($this->interval) {
            self::INTERVAL_ONE_TIME => 'One-time',
            self::INTERVAL_MONTHLY => 'Monthly',
            self::INTERVAL_YEARLY => 'Yearly',
            default => ucfirst($this->interval),
        };
    }

    /**
     * Get savings percentage compared to monthly (for yearly plans).
     */
    public function getSavingsPercentage(): ?float
    {
        if ($this->interval !== self::INTERVAL_YEARLY) {
            return null;
        }

        $monthlyPlan = self::active()
            ->where('code', str_replace('_yearly', '_monthly', $this->code))
            ->first();

        if (!$monthlyPlan) {
            return null;
        }

        $yearlyTotal = $monthlyPlan->price * 12;
        $savings = ($yearlyTotal - $this->price) / $yearlyTotal * 100;

        return round($savings, 1);
    }

    /**
     * Create verification plan.
     */
    public static function createVerificationPlan(): self
    {
        return self::create([
            'code' => 'verification',
            'name' => [
                'en' => 'Account Verification',
                'de' => 'Kontoverifizierung',
                'fr' => 'Vérification du compte',
            ],
            'description' => [
                'en' => 'Verify your account with a small payment to unlock all features',
                'de' => 'Verifizieren Sie Ihr Konto mit einer kleinen Zahlung, um alle Funktionen freizuschalten',
                'fr' => 'Vérifiez votre compte avec un petit paiement pour débloquer toutes les fonctionnalités',
            ],
            'features' => [
                'Account verification',
                'Full platform access',
                'Referral system access',
                'Premium support',
            ],
            'price' => 2.00,
            'cuts_amount' => 0,
            'bonus_cuts' => 0,
            'interval' => self::INTERVAL_ONE_TIME,
            'is_verification_plan' => true,
            'verification_price' => 2.00,
            'is_active' => true,
            'is_featured' => false,
            'sort_order' => 0,
        ]);
    }

    /**
     * Get default plans for seeding.
     */
    public static function getDefaultPlans(): array
    {
        return [
            [
                'code' => 'starter',
                'name' => [
                    'en' => 'Starter Pack',
                    'de' => 'Starter-Paket',
                    'fr' => 'Pack de démarrage',
                ],
                'description' => [
                    'en' => 'Perfect for getting started with cuts',
                    'de' => 'Perfekt für den Einstieg mit Cuts',
                    'fr' => 'Parfait pour commencer avec les cuts',
                ],
                'features' => [
                    '100 cuts included',
                    '10 bonus cuts',
                    'Basic support',
                    'Mobile app access',
                ],
                'price' => 9.99,
                'cuts_amount' => 100,
                'bonus_cuts' => 10,
                'interval' => self::INTERVAL_ONE_TIME,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 1,
            ],
            [
                'code' => 'premium',
                'name' => [
                    'en' => 'Premium Pack',
                    'de' => 'Premium-Paket',
                    'fr' => 'Pack Premium',
                ],
                'description' => [
                    'en' => 'Best value for regular users',
                    'de' => 'Bestes Preis-Leistungs-Verhältnis für regelmäßige Nutzer',
                    'fr' => 'Meilleur rapport qualité-prix pour les utilisateurs réguliers',
                ],
                'features' => [
                    '500 cuts included',
                    '75 bonus cuts',
                    'Priority support',
                    'Mobile app access',
                    'Exclusive offers',
                ],
                'price' => 39.99,
                'cuts_amount' => 500,
                'bonus_cuts' => 75,
                'interval' => self::INTERVAL_ONE_TIME,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'enterprise',
                'name' => [
                    'en' => 'Enterprise Pack',
                    'de' => 'Enterprise-Paket',
                    'fr' => 'Pack Entreprise',
                ],
                'description' => [
                    'en' => 'Maximum value for power users',
                    'de' => 'Maximaler Wert für Power-User',
                    'fr' => 'Valeur maximale pour les utilisateurs avancés',
                ],
                'features' => [
                    '1500 cuts included',
                    '300 bonus cuts',
                    'Premium support',
                    'Mobile app access',
                    'Exclusive offers',
                    'Early access to features',
                ],
                'price' => 99.99,
                'cuts_amount' => 1500,
                'bonus_cuts' => 300,
                'interval' => self::INTERVAL_ONE_TIME,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 3,
            ],
        ];
    }
}
