<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CutsSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_price_id',
        'status',
        'amount',
        'cuts_received',
        'starts_at',
        'ends_at',
        'renews_at',
        'cancelled_at',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'cuts_received' => 'integer',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'renews_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'metadata' => 'array',
    ];

    // Subscription statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_PAST_DUE = 'past_due';
    const STATUS_UNPAID = 'unpaid';
    const STATUS_INCOMPLETE = 'incomplete';

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan for this subscription.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(CutsPlan::class, 'plan_id');
    }

    /**
     * Scope for active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for cancelled subscriptions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    /**
     * Scope for recurring subscriptions.
     */
    public function scopeRecurring($query)
    {
        return $query->whereHas('plan', function($q) {
            $q->whereIn('interval', [CutsPlan::INTERVAL_MONTHLY, CutsPlan::INTERVAL_YEARLY]);
        });
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Check if subscription is past due.
     */
    public function isPastDue(): bool
    {
        return $this->status === self::STATUS_PAST_DUE;
    }

    /**
     * Check if subscription is recurring.
     */
    public function isRecurring(): bool
    {
        return $this->plan && $this->plan->isRecurring();
    }

    /**
     * Check if subscription has ended.
     */
    public function hasEnded(): bool
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    /**
     * Get days until renewal.
     */
    public function getDaysUntilRenewal(): ?int
    {
        if (!$this->renews_at || !$this->isActive()) {
            return null;
        }

        return now()->diffInDays($this->renews_at, false);
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '€' . number_format($this->amount, 2);
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_PAST_DUE => 'Past Due',
            self::STATUS_UNPAID => 'Unpaid',
            self::STATUS_INCOMPLETE => 'Incomplete',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color class.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'text-green-600',
            self::STATUS_CANCELLED => 'text-gray-600',
            self::STATUS_PAST_DUE => 'text-yellow-600',
            self::STATUS_UNPAID => 'text-red-600',
            self::STATUS_INCOMPLETE => 'text-blue-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(): bool
    {
        return $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Reactivate the subscription.
     */
    public function reactivate(): bool
    {
        return $this->update([
            'status' => self::STATUS_ACTIVE,
            'cancelled_at' => null,
        ]);
    }

    /**
     * Create a new subscription.
     */
    public static function createForUser(
        User $user,
        CutsPlan $plan,
        array $stripeData = []
    ): self {
        $subscription = self::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_subscription_id' => $stripeData['subscription_id'] ?? null,
            'stripe_customer_id' => $stripeData['customer_id'] ?? null,
            'stripe_price_id' => $stripeData['price_id'] ?? null,
            'status' => self::STATUS_ACTIVE,
            'amount' => $plan->price,
            'cuts_received' => $plan->total_cuts,
            'starts_at' => now(),
            'ends_at' => $plan->isRecurring() ? null : now()->addYear(),
            'renews_at' => $plan->isRecurring() ? self::calculateNextRenewal($plan) : null,
            'metadata' => $stripeData['metadata'] ?? [],
        ]);

        // Add cuts to user's balance
        $user->increment('cuts', $plan->total_cuts);

        // Create wallet transaction
        WalletTransaction::create([
            'user_id' => $user->id,
            'type' => WalletTransaction::TYPE_PURCHASE,
            'amount' => $plan->price,
            'cuts_amount' => $plan->total_cuts,
            'exchange_rate' => $plan->total_cuts / $plan->price,
            'status' => WalletTransaction::STATUS_COMPLETED,
            'reference' => WalletTransaction::generateReference(),
            'description' => "Purchase of {$plan->getLocalizedName()} plan",
            'metadata' => [
                'subscription_id' => $subscription->id,
                'plan_code' => $plan->code,
            ],
            'processed_at' => now(),
        ]);

        return $subscription;
    }

    /**
     * Calculate next renewal date based on plan interval.
     */
    private static function calculateNextRenewal(CutsPlan $plan): ?\Carbon\Carbon
    {
        return match($plan->interval) {
            CutsPlan::INTERVAL_MONTHLY => now()->addMonth(),
            CutsPlan::INTERVAL_YEARLY => now()->addYear(),
            default => null,
        };
    }

    /**
     * Update subscription from Stripe webhook.
     */
    public function updateFromStripe(array $stripeSubscription): bool
    {
        $status = match($stripeSubscription['status']) {
            'active' => self::STATUS_ACTIVE,
            'canceled' => self::STATUS_CANCELLED,
            'past_due' => self::STATUS_PAST_DUE,
            'unpaid' => self::STATUS_UNPAID,
            'incomplete' => self::STATUS_INCOMPLETE,
            default => $this->status,
        };

        $data = [
            'status' => $status,
        ];

        if (isset($stripeSubscription['current_period_end'])) {
            $data['renews_at'] = \Carbon\Carbon::createFromTimestamp($stripeSubscription['current_period_end']);
        }

        if ($status === self::STATUS_CANCELLED && !$this->cancelled_at) {
            $data['cancelled_at'] = now();
        }

        return $this->update($data);
    }
}
