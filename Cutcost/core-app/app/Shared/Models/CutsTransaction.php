<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CutsTransaction extends Model
{
    const TYPE_REGISTRATION = 'registration';
    const TYPE_VERIFICATION = 'verification';
    const TYPE_FIRST_COUPON = 'first_coupon';
    const TYPE_COUPON_SCAN = 'coupon_scan';
    const TYPE_PURCHASE = 'purchase';

    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REVERSED = 'reversed';

    const LEVEL_1_REWARD = 10; // Direct referral
    const LEVEL_2_REWARD = 3;  // Second level
    const LEVEL_3_REWARD = 1;  // Third level

    protected $fillable = [
        'user_id',
        'source_user_id',
        'type',
        'amount',
        'level',
        'referral_id',
        'metadata',
        'description',
        'status',
        'processed_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'processed_at' => 'datetime',
        'amount' => 'integer',
        'level' => 'integer',
    ];

    /**
     * The user who received the cuts
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The user who triggered this reward
     */
    public function sourceUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'source_user_id');
    }

    /**
     * The referral relationship this transaction is based on
     */
    public function referral(): BelongsTo
    {
        return $this->belongsTo(Referral::class);
    }

    /**
     * Get the reward amount for a specific level and type
     */
    public static function getRewardAmount(string $type, int $level): int
    {
        $baseRewards = [
            self::TYPE_REGISTRATION => [
                1 => self::LEVEL_1_REWARD,
                2 => self::LEVEL_2_REWARD,
                3 => self::LEVEL_3_REWARD,
            ],
            self::TYPE_VERIFICATION => [
                1 => self::LEVEL_1_REWARD,
                2 => self::LEVEL_2_REWARD,
                3 => self::LEVEL_3_REWARD,
            ],
            self::TYPE_FIRST_COUPON => [
                1 => self::LEVEL_1_REWARD,
                2 => self::LEVEL_2_REWARD,
                3 => self::LEVEL_3_REWARD,
            ],
            self::TYPE_COUPON_SCAN => [
                1 => 5, // Lower reward for regular scans
                2 => 2,
                3 => 1,
            ],
            self::TYPE_PURCHASE => [
                1 => 15, // Higher reward for purchases
                2 => 5,
                3 => 2,
            ],
        ];

        return $baseRewards[$type][$level] ?? 0;
    }

    /**
     * Check if the transaction is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if the transaction is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the transaction failed
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if the transaction was reversed
     */
    public function isReversed(): bool
    {
        return $this->status === self::STATUS_REVERSED;
    }

    /**
     * Mark transaction as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark transaction as failed
     */
    public function markAsFailed(): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Reverse the transaction
     */
    public function reverse(): void
    {
        $this->update([
            'status' => self::STATUS_REVERSED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Get formatted description
     */
    public function getFormattedDescription(): string
    {
        if ($this->description) {
            return $this->description;
        }

        $typeDescriptions = [
            self::TYPE_REGISTRATION => 'Registration reward',
            self::TYPE_VERIFICATION => 'Verification reward',
            self::TYPE_FIRST_COUPON => 'First coupon reward',
            self::TYPE_COUPON_SCAN => 'Coupon scan reward',
            self::TYPE_PURCHASE => 'Purchase reward',
        ];

        $baseDescription = $typeDescriptions[$this->type] ?? 'Unknown reward';
        
        return "{$baseDescription} (Level {$this->level})";
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for specific transaction type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for specific level
     */
    public function scopeAtLevel($query, int $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope for recent transactions
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
