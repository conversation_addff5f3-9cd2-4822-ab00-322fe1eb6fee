<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Model;

class Media extends Model
{
    protected $table = 'media';

    protected $fillable = [
        'temp_uuid',
        'model_type',
        'model_id',
        'path',
        'order_column',
    ];

    public function model()
    {
        return $this->morphTo();
    }

    // Получить URL (если используешь storage:link)
    public function getUrlAttribute(): string
    {
        return asset('storage/' . $this->path);
    }
}
