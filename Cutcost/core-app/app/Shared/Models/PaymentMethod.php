<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'provider',
        'provider_id',
        'type',
        'last_four',
        'brand',
        'country',
        'is_default',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    // Providers
    const PROVIDER_STRIPE = 'stripe';
    const PROVIDER_PAYPAL = 'paypal';

    // Types
    const TYPE_CARD = 'card';
    const TYPE_BANK_ACCOUNT = 'bank_account';
    const TYPE_PAYPAL = 'paypal';

    /**
     * Get the user that owns the payment method.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active payment methods.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default payment method.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for specific provider.
     */
    public function scopeProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Get formatted display name for payment method.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->type === self::TYPE_CARD) {
            return ucfirst($this->brand) . ' •••• ' . $this->last_four;
        }

        if ($this->type === self::TYPE_PAYPAL) {
            return 'PayPal';
        }

        if ($this->type === self::TYPE_BANK_ACCOUNT) {
            return 'Bank Account •••• ' . $this->last_four;
        }

        return ucfirst($this->type);
    }

    /**
     * Get icon class for payment method.
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_CARD => match(strtolower($this->brand)) {
                'visa' => 'fab fa-cc-visa',
                'mastercard' => 'fab fa-cc-mastercard',
                'amex' => 'fab fa-cc-amex',
                'discover' => 'fab fa-cc-discover',
                default => 'fas fa-credit-card',
            },
            self::TYPE_PAYPAL => 'fab fa-paypal',
            self::TYPE_BANK_ACCOUNT => 'fas fa-university',
            default => 'fas fa-credit-card',
        };
    }

    /**
     * Set as default payment method.
     */
    public function setAsDefault(): bool
    {
        // Remove default from other payment methods
        $this->user->paymentMethods()
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // Set this as default
        return $this->update(['is_default' => true]);
    }

    /**
     * Deactivate payment method.
     */
    public function deactivate(): bool
    {
        $updated = $this->update(['is_active' => false]);

        // If this was the default, set another as default
        if ($this->is_default) {
            $newDefault = $this->user->paymentMethods()
                ->active()
                ->where('id', '!=', $this->id)
                ->first();

            if ($newDefault) {
                $newDefault->setAsDefault();
            }
        }

        return $updated;
    }

    /**
     * Check if payment method can be used.
     */
    public function canBeUsed(): bool
    {
        return $this->is_active;
    }

    /**
     * Create a new Stripe payment method.
     */
    public static function createFromStripe(User $user, array $stripePaymentMethod): self
    {
        $data = [
            'user_id' => $user->id,
            'provider' => self::PROVIDER_STRIPE,
            'provider_id' => $stripePaymentMethod['id'],
            'type' => $stripePaymentMethod['type'],
            'is_active' => true,
        ];

        // Handle card details
        if ($stripePaymentMethod['type'] === 'card' && isset($stripePaymentMethod['card'])) {
            $card = $stripePaymentMethod['card'];
            $data['last_four'] = $card['last4'];
            $data['brand'] = $card['brand'];
            $data['country'] = $card['country'];
        }

        // Set as default if user has no payment methods
        $data['is_default'] = $user->paymentMethods()->count() === 0;

        $paymentMethod = self::create($data);

        // If set as default, ensure no other defaults exist
        if ($data['is_default']) {
            $paymentMethod->setAsDefault();
        }

        return $paymentMethod;
    }

    /**
     * Update from Stripe payment method.
     */
    public function updateFromStripe(array $stripePaymentMethod): bool
    {
        $data = [];

        if ($stripePaymentMethod['type'] === 'card' && isset($stripePaymentMethod['card'])) {
            $card = $stripePaymentMethod['card'];
            $data['last_four'] = $card['last4'];
            $data['brand'] = $card['brand'];
            $data['country'] = $card['country'];
        }

        return $this->update($data);
    }
}
