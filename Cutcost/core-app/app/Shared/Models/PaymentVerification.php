<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentVerification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'amount',
        'status',
        'stripe_payment_intent_id',
        'payment_method_id',
        'verified_at',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'verified_at' => 'datetime',
        'metadata' => 'array',
    ];

    // Verification statuses
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';

    // Verification amount (2 EUR)
    const VERIFICATION_AMOUNT = 2.00;

    /**
     * Get the user that owns the verification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment method used for verification.
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Scope for completed verifications.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for pending verifications.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Check if verification is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if verification is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if verification failed.
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if verification was refunded.
     */
    public function isRefunded(): bool
    {
        return $this->status === self::STATUS_REFUNDED;
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '€' . number_format($this->amount, 2);
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_REFUNDED => 'Refunded',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color class.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'text-yellow-600',
            self::STATUS_COMPLETED => 'text-green-600',
            self::STATUS_FAILED => 'text-red-600',
            self::STATUS_REFUNDED => 'text-blue-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Mark verification as completed.
     */
    public function markAsCompleted(): bool
    {
        $updated = $this->update([
            'status' => self::STATUS_COMPLETED,
            'verified_at' => now(),
        ]);

        if ($updated) {
            // Mark user as payment verified
            $this->user->update([
                'is_payment_verified' => true,
                'payment_verified_at' => now(),
            ]);

            // Create wallet transaction for verification
            WalletTransaction::create([
                'user_id' => $this->user_id,
                'type' => WalletTransaction::TYPE_DEPOSIT,
                'amount' => $this->amount,
                'status' => WalletTransaction::STATUS_COMPLETED,
                'payment_method' => 'stripe',
                'payment_id' => $this->stripe_payment_intent_id,
                'reference' => WalletTransaction::generateReference(),
                'description' => 'Account verification payment',
                'metadata' => [
                    'verification_id' => $this->id,
                    'is_verification' => true,
                ],
                'processed_at' => now(),
            ]);
        }

        return $updated;
    }

    /**
     * Mark verification as failed.
     */
    public function markAsFailed(string $reason = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($reason) {
            $metadata['failure_reason'] = $reason;
        }

        return $this->update([
            'status' => self::STATUS_FAILED,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Mark verification as refunded.
     */
    public function markAsRefunded(): bool
    {
        return $this->update([
            'status' => self::STATUS_REFUNDED,
        ]);
    }

    /**
     * Create a new payment verification.
     */
    public static function createForUser(
        User $user,
        string $stripePaymentIntentId,
        string $paymentMethodId = null
    ): self {
        return self::create([
            'user_id' => $user->id,
            'amount' => self::VERIFICATION_AMOUNT,
            'status' => self::STATUS_PENDING,
            'stripe_payment_intent_id' => $stripePaymentIntentId,
            'payment_method_id' => $paymentMethodId,
            'metadata' => [
                'created_via' => 'verification_flow',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ],
        ]);
    }

    /**
     * Get the latest verification for a user.
     */
    public static function getLatestForUser(User $user): ?self
    {
        return self::where('user_id', $user->id)
            ->latest()
            ->first();
    }

    /**
     * Check if user has a pending verification.
     */
    public static function hasPendingVerification(User $user): bool
    {
        return self::where('user_id', $user->id)
            ->where('status', self::STATUS_PENDING)
            ->exists();
    }

    /**
     * Check if user has completed verification.
     */
    public static function hasCompletedVerification(User $user): bool
    {
        return self::where('user_id', $user->id)
            ->where('status', self::STATUS_COMPLETED)
            ->exists();
    }

    /**
     * Update verification from Stripe webhook.
     */
    public function updateFromStripe(array $stripePaymentIntent): bool
    {
        $status = match($stripePaymentIntent['status']) {
            'succeeded' => self::STATUS_COMPLETED,
            'requires_payment_method', 'requires_confirmation', 'requires_action' => self::STATUS_PENDING,
            'canceled' => self::STATUS_FAILED,
            default => $this->status,
        };

        $data = ['status' => $status];

        if ($status === self::STATUS_COMPLETED && !$this->verified_at) {
            $data['verified_at'] = now();
            
            // Mark user as verified
            $this->user->update([
                'is_payment_verified' => true,
                'payment_verified_at' => now(),
            ]);
        }

        return $this->update($data);
    }
}
