<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Referral extends Model
{
    protected $fillable = [
        'user_id',
        'referrer_id',
        'referral_code',
        'registered_at',
        'ip_address',
        'user_agent',
        'is_verified',
        'verified_at',
        'first_coupon_used',
        'first_coupon_used_at',
        'registration_reward_given',
        'verification_reward_given',
        'first_action_reward_given',
    ];

    protected $casts = [
        'registered_at' => 'datetime',
        'verified_at' => 'datetime',
        'first_coupon_used_at' => 'datetime',
        'is_verified' => 'boolean',
        'first_coupon_used' => 'boolean',
        'registration_reward_given' => 'boolean',
        'verification_reward_given' => 'boolean',
        'first_action_reward_given' => 'boolean',
    ];

    /**
     * The user who was referred
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The user who made the referral
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Cuts transactions related to this referral
     */
    public function cutsTransactions(): HasMany
    {
        return $this->hasMany(CutsTransaction::class);
    }

    /**
     * Check if the referred user has completed verification
     */
    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * Check if the referred user has used their first coupon
     */
    public function hasUsedFirstCoupon(): bool
    {
        return $this->first_coupon_used;
    }

    /**
     * Mark the referral as verified
     */
    public function markAsVerified(): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Mark the first coupon as used
     */
    public function markFirstCouponUsed(): void
    {
        $this->update([
            'first_coupon_used' => true,
            'first_coupon_used_at' => now(),
        ]);
    }

    /**
     * Check if registration reward has been given
     */
    public function hasRegistrationReward(): bool
    {
        return $this->registration_reward_given;
    }

    /**
     * Check if verification reward has been given
     */
    public function hasVerificationReward(): bool
    {
        return $this->verification_reward_given;
    }

    /**
     * Check if first action reward has been given
     */
    public function hasFirstActionReward(): bool
    {
        return $this->first_action_reward_given;
    }

    /**
     * Mark registration reward as given
     */
    public function markRegistrationRewardGiven(): void
    {
        $this->update(['registration_reward_given' => true]);
    }

    /**
     * Mark verification reward as given
     */
    public function markVerificationRewardGiven(): void
    {
        $this->update(['verification_reward_given' => true]);
    }

    /**
     * Mark first action reward as given
     */
    public function markFirstActionRewardGiven(): void
    {
        $this->update(['first_action_reward_given' => true]);
    }

    /**
     * Get the age of the referral in days
     */
    public function getAgeInDays(): int
    {
        return $this->registered_at->diffInDays(now());
    }

    /**
     * Scope to get verified referrals
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get referrals that have used their first coupon
     */
    public function scopeWithFirstCouponUsed($query)
    {
        return $query->where('first_coupon_used', true);
    }

    /**
     * Scope to get recent referrals
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('registered_at', '>=', now()->subDays($days));
    }
}
