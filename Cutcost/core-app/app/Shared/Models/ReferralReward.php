<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReferralReward extends Model
{
    protected $table = 'referral_rewards';

    protected $fillable = [
        'user_id',
        'referred_user_id',
        'type',
        'level',
        'amount',
    ];

    // Кто получил награду
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // За кого получена награда
    public function referredUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referred_user_id');
    }
}
