<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Strike extends Model
{
    protected $fillable = [
        'user_id',
        'actor_id',
        'type',
        'reason',
        'metadata',
        'expires_at',
        'revoked_at',
        'revoked_by',
    ];

    protected $casts = [
        'metadata' => 'array',
        'expires_at' => 'datetime',
        'revoked_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The user who received the strike
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * The user/admin who issued the strike
     */
    public function actor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'actor_id');
    }

    /**
     * The user who revoked the strike
     */
    public function revokedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'revoked_by');
    }

    /**
     * Scope to get only active (non-revoked) strikes
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNull('revoked_at');
    }

    /**
     * Scope to get only revoked strikes
     */
    public function scopeRevoked(Builder $query): Builder
    {
        return $query->whereNotNull('revoked_at');
    }

    /**
     * Scope to get only expired strikes
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Scope to get only non-expired strikes
     */
    public function scopeNotExpired(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->whereNull('expires_at')
                  ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope to filter by strike type
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get strikes for a specific user
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if the strike is currently active
     */
    public function isActive(): bool
    {
        return $this->revoked_at === null && 
               ($this->expires_at === null || $this->expires_at->isFuture());
    }

    /**
     * Check if the strike is revoked
     */
    public function isRevoked(): bool
    {
        return $this->revoked_at !== null;
    }

    /**
     * Check if the strike is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at !== null && $this->expires_at->isPast();
    }

    /**
     * Revoke the strike
     */
    public function revoke(int $revokedBy = null): bool
    {
        return $this->update([
            'revoked_at' => now(),
            'revoked_by' => $revokedBy,
        ]);
    }

    /**
     * Get the metadata value by key
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Set a metadata value
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }

    /**
     * Get formatted reason with fallback
     */
    public function getFormattedReasonAttribute(): string
    {
        if ($this->reason) {
            return $this->reason;
        }

        return match($this->type) {
            'spam' => 'Spam violation',
            'abuse' => 'Abusive behavior',
            'copyright' => 'Copyright infringement',
            'harassment' => 'Harassment',
            'inappropriate' => 'Inappropriate content',
            default => 'Policy violation'
        };
    }

    /**
     * Get the strike severity level based on type
     */
    public function getSeverityAttribute(): string
    {
        return match($this->type) {
            'spam', 'inappropriate' => 'low',
            'abuse', 'harassment' => 'medium',
            'copyright', 'impersonation' => 'high',
            default => 'medium'
        };
    }
}
