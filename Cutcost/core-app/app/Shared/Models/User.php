<?php

namespace App\Shared\Models;

use App\Modules\Messenger\Models\Chat;
use App\Modules\Partner\Models\Company;
use App\Modules\Seller\Models\Friend;
use App\Modules\Seller\Models\Post;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes, HasRoles;

    protected $fillable = [
        'nickname',
        'display_name',
        'email',
        'password',
        'avatar',
        'cover',
        'description',
        'is_banned',
        'company_id',
        'referrer_id',
        'referral_code',
        'cuts',
        'preferences',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'is_banned' => 'boolean',
        'preferences' => 'array',
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_public_profile' => 'boolean',
    ];

    public function companies()
    {
        return $this->hasMany(Company::class);
    }

    public function companiesSubscriptions()
    {
        return $this->belongsToMany(Company::class, 'company_user');
    }

    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    //subscribers
    public function incomingRequests()
    {
        // Кто прислал мне заявку
        return $this->belongsToMany(
            self::class,
            'friends',
            'friend_id', // я
            'user_id'    // кто отправил
        )->wherePivot('status', Friend::STATUS_PENDING)
            ->withTimestamps();
    }

    public function outgoingRequests()
    {
        // Я отправил заявку
        return $this->belongsToMany(
            self::class,
            'friends',
            'user_id',  // я
            'friend_id' // кому
        )->wherePivot('status', Friend::STATUS_PENDING)
            ->withTimestamps();
    }

    public function myFriends()
    {
        return $this->belongsToMany(
            self::class,
            'friends',
            'user_id',   // я
            'friend_id'  // друг
        )->wherePivot('status', Friend::STATUS_ACCEPTED)
            ->withTimestamps();
    }

    public function referralRewards()
    {
        return $this->hasMany(ReferralReward::class);
    }

    public function referredUsers()
    {
        return $this->hasMany(User::class, 'referrer_id');
    }

    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Referrals where this user is the referrer
     */
    public function referrals()
    {
        return $this->hasMany(Referral::class, 'referrer_id');
    }

    /**
     * Referral record where this user was referred
     */
    public function referralRecord()
    {
        return $this->hasOne(Referral::class, 'user_id');
    }

    /**
     * Cuts transactions where this user received cuts
     */
    public function cutsTransactions()
    {
        return $this->hasMany(CutsTransaction::class);
    }

    /**
     * Cuts transactions triggered by this user's actions
     */
    public function triggeredCutsTransactions()
    {
        return $this->hasMany(CutsTransaction::class, 'source_user_id');
    }


    public function chats()
    {
        return $this->belongsToMany(Chat::class, 'chat_user', 'user_id', 'chat_id')
            ->withTimestamps();
    }

    public function isFriend(User $user): bool
    {
        return $this->myFriends()->where('friend_id', $user->id)->exists();
    }

    public function scopeWithFriendStatus($query, ?User $authUser = null)
    {
        if (!$authUser) {
            return $query->selectRaw("users.*, 'not_friend' as friend_status");
        }

        $authId = (int) $authUser->id;

        return $query->select('users.*')
            ->selectRaw(
                "
                    CASE
                        WHEN EXISTS(
                            SELECT 1 FROM friends
                            WHERE ((user_id = ? AND friend_id = users.id) OR (user_id = users.id AND friend_id = ?))
                              AND status = ?
                        ) THEN 'friend'
                        WHEN EXISTS(
                            SELECT 1 FROM friends
                            WHERE user_id = ? AND friend_id = users.id AND status = ?
                        ) THEN 'sent'
                        WHEN EXISTS(
                            SELECT 1 FROM friends
                            WHERE friend_id = ? AND user_id = users.id AND status = ?
                        ) THEN 'received'
                        ELSE 'not_friend'
                    END as friend_status
                 ",
                [
                    $authId,
                    $authId,
                    Friend::STATUS_ACCEPTED,
                    $authId,
                    Friend::STATUS_PENDING,
                    $authId,
                    Friend::STATUS_PENDING,
                ]
            );
    }

    /**
     * Generate a unique referral code for the user
     */
    public function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        } while (self::where('referral_code', $code)->exists());

        $this->update(['referral_code' => $code]);

        return $code;
    }

    /**
     * Get or generate referral code
     */
    public function getReferralCode(): string
    {
        if (!$this->referral_code) {
            return $this->generateReferralCode();
        }

        return $this->referral_code;
    }

    /**
     * Get referral URL
     */
    public function getReferralUrl(): string
    {
        return url('/register?ref=' . $this->getReferralCode());
    }

    /**
     * Get total cuts earned from referrals
     */
    public function getTotalCutsEarned(): int
    {
        return $this->cutsTransactions()
            ->where('status', CutsTransaction::STATUS_COMPLETED)
            ->sum('amount');
    }

    /**
     * Get cuts earned by level
     */
    public function getCutsEarnedByLevel(): array
    {
        $transactions = $this->cutsTransactions()
            ->where('status', CutsTransaction::STATUS_COMPLETED)
            ->selectRaw('level, SUM(amount) as total')
            ->groupBy('level')
            ->pluck('total', 'level')
            ->toArray();

        return [
            1 => $transactions[1] ?? 0,
            2 => $transactions[2] ?? 0,
            3 => $transactions[3] ?? 0,
        ];
    }

  
    /**
     * Check if user was referred by someone
     */
    public function wasReferred(): bool
    {
        return !is_null($this->referrer_id);
    }

    /**
     * Get the referral chain up to 3 levels
     */
    public function getReferralChain(): array
    {
        $chain = [];
        $currentUser = $this;
        $level = 1;

        while ($currentUser->referrer && $level <= 3) {
            $chain[] = [
                'user' => $currentUser->referrer,
                'level' => $level,
            ];
            $currentUser = $currentUser->referrer;
            $level++;
        }

        return $chain;
    }

    /**
     * Check if user has verified their email
     */
    public function hasVerifiedEmail(): bool
    {
        return !is_null($this->email_verified_at);
    }

    // ===== WALLET RELATIONSHIPS =====

    /**
     * Get the user's wallet transactions.
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get the user's payment methods.
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class);
    }

    /**
     * Get the user's cuts subscriptions.
     */
    public function cutsSubscriptions(): HasMany
    {
        return $this->hasMany(CutsSubscription::class);
    }

    /**
     * Get the user's payment verifications.
     */
    public function paymentVerifications(): HasMany
    {
        return $this->hasMany(PaymentVerification::class);
    }

    // ===== WALLET SIMPLE GETTERS =====

    /**
     * Get user's total wallet balance (EUR).
     */
    public function getWalletBalance(): float
    {
        return (float) $this->wallet_balance;
    }

    /**
     * Get user's cuts balance.
     */
    public function getCutsBalance(): int
    {
        return (int) $this->cuts;
    }

    /**
     * Check if user has payment verification.
     */
    public function isPaymentVerified(): bool
    {
        return (bool) $this->is_payment_verified;
    }

    /**
     * Get formatted wallet balance.
     */
    public function getFormattedWalletBalanceAttribute(): string
    {
        return '€' . number_format($this->wallet_balance, 2);
    }

    /**
     * Get formatted cuts balance.
     */
    public function getFormattedCutsBalanceAttribute(): string
    {
        return number_format($this->cuts) . ' cuts';
    }
}
