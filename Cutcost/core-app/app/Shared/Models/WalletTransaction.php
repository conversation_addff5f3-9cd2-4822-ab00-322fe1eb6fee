<?php

namespace App\Shared\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WalletTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'amount',
        'cuts_amount',
        'exchange_rate',
        'status',
        'payment_method',
        'payment_id',
        'reference',
        'metadata',
        'description',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'cuts_amount' => 'integer',
        'exchange_rate' => 'decimal:4',
        'metadata' => 'array',
        'processed_at' => 'datetime',
    ];

    // Transaction types
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_PURCHASE = 'purchase';
    const TYPE_REFUND = 'refund';
    const TYPE_BONUS = 'bonus';
    const TYPE_REFERRAL = 'referral';

    // Transaction statuses
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the user that owns the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for specific transaction type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '€' . number_format($this->amount, 2);
    }

    /**
     * Get formatted cuts amount.
     */
    public function getFormattedCutsAttribute(): string
    {
        return number_format($this->cuts_amount) . ' cuts';
    }

    /**
     * Check if transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if transaction failed.
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Mark transaction as completed.
     */
    public function markAsCompleted(): bool
    {
        return $this->update([
            'status' => self::STATUS_COMPLETED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark transaction as failed.
     */
    public function markAsFailed(string $reason = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($reason) {
            $metadata['failure_reason'] = $reason;
        }

        return $this->update([
            'status' => self::STATUS_FAILED,
            'processed_at' => now(),
            'metadata' => $metadata,
        ]);
    }

    /**
     * Generate unique reference for transaction.
     */
    public static function generateReference(): string
    {
        return 'TXN-' . strtoupper(uniqid());
    }

    /**
     * Create a deposit transaction.
     */
    public static function createDeposit(
        User $user,
        float $amount,
        int $cutsAmount,
        string $paymentMethod,
        string $paymentId = null
    ): self {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_DEPOSIT,
            'amount' => $amount,
            'cuts_amount' => $cutsAmount,
            'exchange_rate' => $cutsAmount / $amount,
            'status' => self::STATUS_PENDING,
            'payment_method' => $paymentMethod,
            'payment_id' => $paymentId,
            'reference' => self::generateReference(),
            'description' => "Deposit of €{$amount} for {$cutsAmount} cuts",
        ]);
    }

    /**
     * Create a purchase transaction.
     */
    public static function createPurchase(
        User $user,
        float $amount,
        int $cutsAmount,
        string $description,
        array $metadata = []
    ): self {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_PURCHASE,
            'amount' => $amount,
            'cuts_amount' => $cutsAmount,
            'status' => self::STATUS_COMPLETED,
            'reference' => self::generateReference(),
            'description' => $description,
            'metadata' => $metadata,
            'processed_at' => now(),
        ]);
    }

    /**
     * Create a referral bonus transaction.
     */
    public static function createReferralBonus(
        User $user,
        int $cutsAmount,
        string $description,
        array $metadata = []
    ): self {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_REFERRAL,
            'cuts_amount' => $cutsAmount,
            'status' => self::STATUS_COMPLETED,
            'reference' => self::generateReference(),
            'description' => $description,
            'metadata' => $metadata,
            'processed_at' => now(),
        ]);
    }
}
