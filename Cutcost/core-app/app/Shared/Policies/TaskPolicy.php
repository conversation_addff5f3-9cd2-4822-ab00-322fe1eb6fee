<?php

namespace App\Shared\Policies;

use App\Shared\Models\Task;
use App\Shared\Models\User;
use Illuminate\Auth\Access\Response;

class TaskPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Task $Task): bool
    {
        return $user->company->id === $Task->company->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Task $Task): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Task $Task): bool
    {
        return $user->company->id === $Task->company->id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Task $Task): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Task $Task): bool
    {
        return false;
    }
}
