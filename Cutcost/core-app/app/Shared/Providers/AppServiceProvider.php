<?php

namespace App\Shared\Providers;

use App\Shared\Contracts\Repositories\UserRepositoryContract;
use App\Shared\Repositories\UserRepository;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\URL;
use Laravel\Octane\Events\RequestReceived;
use Illuminate\Support\Facades\Event;
use App\Shared\Services\MetaService;
use App\Shared\Models\User;
use App\Shared\Observers\UserObserver;

class AppServiceProvider extends ServiceProvider
{

    public function register(): void
    {
        if ($this->app->environment('production') || $this->app->environment('dev')) {
            URL::forceScheme('https');
        }

        $this->app->bind(UserRepositoryContract::class, UserRepository::class);
    }

    public function boot(): void
    {
        // prevent meta tags from leaking to other requests via cache
        Event::listen(function (RequestReceived $_) {
            MetaService::cleanup();
        });

        User::observe(UserObserver::class);
        JsonResource::withoutWrapping();
        Model::preventLazyLoading(! $this->app->isProduction());
    }
}
