<?php

namespace App\Shared\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Shared\Events\UserRegisteredViaReferral;
use App\Shared\Events\UserCompletedQualifyingAction;
use App\Shared\Events\ReferralRewardEarned;
use App\Shared\Listeners\CreateReferralRecord;
use App\Shared\Listeners\DistributeActionRewards;
use App\Shared\Listeners\NotifyUserOfReward;

class ReferralServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register services as singletons
        $this->app->singleton(\App\Shared\Services\ReferralService::class);
        $this->app->singleton(\App\Shared\Services\RewardDistributionService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register event listeners
        Event::listen(
            UserRegisteredViaReferral::class,
            CreateReferralRecord::class
        );

        Event::listen(
            UserCompletedQualifyingAction::class,
            DistributeActionRewards::class
        );

        Event::listen(
            ReferralRewardEarned::class,
            NotifyUserOfReward::class
        );

        // Register policies
        $this->registerPolicies();
    }

    /**
     * Register referral policies
     */
    protected function registerPolicies(): void
    {
        $gate = $this->app->make(\Illuminate\Contracts\Auth\Access\Gate::class);
        
        $gate->policy(\App\Shared\Models\Referral::class, \App\Shared\Policies\ReferralPolicy::class);
        $gate->policy(\App\Shared\Models\CutsTransaction::class, \App\Shared\Policies\ReferralPolicy::class);
    }
}
