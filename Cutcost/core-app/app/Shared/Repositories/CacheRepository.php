<?php

namespace App\Shared\Repositories;

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Industry;
use App\Shared\Enums\CacheKeys;
use App\Shared\Models\Country;
use App\Shared\Models\Locale;
use App\Shared\Services\CacheService as Cache;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;

final class CacheRepository
{
    // -------- CACHE ----------

    // counts

    public function companyCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_COMPANY_COUNT->value . $user->id, function () use ($user) {
            return DB::table('companies')->where('user_id', $user->id)->count();
        });
    }

    public function locationCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_LOCATION_COUNT->value . $user->id, function () use ($user) {
            return DB::table('locations')->where('user_id', $user->id)->count();
        });
    }

    public function couponCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_COUPON_COUNT->value . $user->id, function () use ($user) {
            return DB::table('coupons')->where('user_id', $user->id)->count();
        });
    }

    public function followingCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_FOLLOWING_COUNT->value . $user->id, function () use ($user) {
            return $user->outgoingRequests()->count();
        });
    }

    public function subscriberCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_SUBSCRIBER_COUNT->value . $user->id, function () use ($user) {
            return $user->incomingRequests()->count();
        });
    }

    public function friendCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_FRIEND_COUNT->value . $user->id, function () use ($user) {
            return $user->myFriends()->count();
        });
    }

    public function postCount(User $user): int
    {
        return Cache::use(CacheKeys::USER_POST_COUNT->value . $user->id, function () use ($user) {
            return $user->posts->count();
        });
    }

    public function subscribersCount(Company $company): int
    {
        return Cache::use(CacheKeys::COMPANY_SUBSCRIBER_COUNT->value . $company->id, function () use ($company) {
            return $company->subscribers()->count();
        });
    }

    public function couponsCount(Company $company): int
    {
        return Cache::use(CacheKeys::COMPANY_COUPON_COUNT->value . $company->id, function () use ($company) {
            return $company->coupons()->count();
        });
    }

    public function locationsCount(Company $company): int
    {
        return Cache::use(CacheKeys::COMPANY_LOCATION_COUNT->value . $company->id, function () use ($company) {
            return $company->locations()->count();
        });
    }

    // static 

    public function getUsedCountries()
    {
        return Cache::use(CacheKeys::USED_COUNTRIES->value, function () {
            return Country::whereIn(
                'id',
                DB::table('companies')
                    ->select('country_id')
                    ->distinct()
                    ->pluck('country_id')
                    ->toArray()
            )
                ->select('name', 'id', 'code')
                ->get();
        });
    }


    public function getAllLocales()
    {
        return Cache::use(CacheKeys::LOCALES->value, function () {
            return Locale::select(['id', 'name', 'code'])->get();
        });
    }

    public function getAllCategories()
    {
        return Cache::use('categories', function () {
            return Industry::select(['id', 'name'])->get();
        });
    }

    // boolean 
    public function isViewed(Model $model, $ip): bool
    {
        return Cache::useBoolean($model->getTable() . '_' . $model->id . '_' . $ip);
    }

    public function isNotificationSent(int $userId, int $friendId): bool
    {
        return Cache::useBoolean('notification_sent_' . $userId . '_' . $friendId);
    }


    // VALUES

    public function preferences(User $user): array
    {
        return Cache::use(CacheKeys::USER_PREFERENCES->value . $user->id, function () use ($user) {
            $pref = $user->preferences;

            if (empty($pref['timezone']))
                $pref['timezone'] = Cookie::get('timezone');

            return $pref;
        });
    }

    // =====================    FORGET    ===============================

    // userId
    public function forgetPreferences(int $id): void
    {
        Cache::destroy(CacheKeys::USER_PREFERENCES->value . $id);
    }

    public function forgetPostCount(int $id): void
    {
        Cache::destroy(CacheKeys::USER_POST_COUNT->value . $id);
    }
    public function forgetCompanyCount(int $id): void
    {
        Cache::destroy(CacheKeys::USER_COMPANY_COUNT->value . $id);
    }

    public function forgetSubscriberCount(int $id): void
    {
        Cache::destroy(CacheKeys::USER_SUBSCRIBER_COUNT->value . $id);
    }

    public function forgetFriendCount(int $id): void
    {
        Cache::destroy(CacheKeys::USER_FRIEND_COUNT->value . $id);
    }

    public function forgetFollowingCount(int $id): void
    {
        Cache::destroy(CacheKeys::USER_FOLLOWING_COUNT->value . $id);
    }

    // companyId
    public function forgetCompanySubscribersCount(int $id): void
    {
        Cache::destroy(CacheKeys::COMPANY_SUBSCRIBER_COUNT->value . $id);
    }
    public function forgetCompanyCouponsCount(int $id): void
    {
        Cache::destroy(CacheKeys::COMPANY_COUPON_COUNT->value . $id);
    }
    public function forgetCompanyLocationsCount(int $id): void
    {
        Cache::destroy(CacheKeys::COMPANY_LOCATION_COUNT->value . $id);
    }
}
