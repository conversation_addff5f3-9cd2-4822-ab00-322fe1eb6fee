<?php

namespace App\Shared\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class BusinessHours implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_array($value) || !isset($value['days']) || !is_array($value['days']) || count($value['days']) < 7) {
            $fail(__('validation.custom.working_hours.required_full_week'));
            return;
        }

        foreach ($value['days'] as $index => $day) {
            $dayName = $this->getDayName($index);

            if (!is_array($day)) {
                $fail(__('validation.custom.working_hours.invalid_format', ['day' => $dayName]));
                continue;
            }

            if (!isset($day['name']) || !is_string($day['name'])) {
                $fail(__('validation.custom.working_hours.missing_field', ['field' => 'name', 'day' => $dayName]));
            }

            if (!isset($day['workDay']) || !is_bool($day['workDay'])) {
                $fail(__('validation.custom.working_hours.missing_field', ['field' => 'workDay', 'day' => $dayName]));
            }

            if (!isset($day['diapasons']) || !is_array($day['diapasons'])) {
                $fail(__('validation.custom.working_hours.missing_field', ['field' => 'diapasons', 'day' => $dayName]));
                continue;
            }

            foreach ($day['diapasons'] as $idx => $range) {
                if (!isset($range['start']) || !isset($range['end'])) {
                    $fail(__('validation.custom.working_hours.missing_field', ['field' => "diapasons[$idx]", 'day' => $dayName]));
                    continue;
                }

                if (!$this->isValidTimeFormat($range['start']) || !$this->isValidTimeFormat($range['end'])) {
                    $fail(__('validation.custom.working_hours.invalid_format', ['day' => $dayName]));
                }

                // Allow full-day 00:00–24:00
                if ($range['start'] === '00:00' && $range['end'] === '24:00') {
                    continue;
                }

                // Allow overnight ranges (end < start)
                if ($range['start'] === $range['end']) {
                    $fail(__('validation.custom.working_hours.invalid_range', ['day' => $dayName]));
                }
            }
        }
    }

    protected function isValidTimeFormat(string $time): bool
    {
        // Allow 24:00 as valid end
        return preg_match('/^(?:2[0-3]|[01][0-9]):[0-5][0-9]$|^24:00$/', $time);
    }

    protected function getDayName(int $index): string
    {
        return match ($index) {
            0 => __('app.monday'),
            1 => __('app.tuesday'),
            2 => __('app.wednesday'),
            3 => __('app.thursday'),
            4 => __('app.friday'),
            5 => __('app.saturday'),
            6 => __('app.sunday'),
            default => __('app.unknown_day'),
        };
    }
}
