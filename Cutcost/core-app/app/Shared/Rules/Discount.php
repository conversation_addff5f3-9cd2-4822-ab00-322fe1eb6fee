<?php

namespace App\Shared\Rules;

use Closure;

use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class Discount implements ValidationRule, DataAwareRule
{

    public $implicit = true;

    protected array $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;
        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Удалим пробелы
        $value = trim($value);

        if (str_ends_with($value, '%')) {
            $percent = floatval(rtrim($value, '%'));
            if ($percent > 100 || $percent < 0) {
                $fail(__('validation.custom.discount.invalid_percentage'));
            }
        } else {
            $amount = floatval(rtrim($value, '€'));
            if ($amount > $this->data['price_in_store']) {
                $fail(__('validation.custom.discount.invalid_amount'));
            }
        }
    }
}
