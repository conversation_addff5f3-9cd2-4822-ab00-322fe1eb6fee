<?php

declare(strict_types=1);

namespace App\Shared\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class LangsText implements ValidationRule, DataAwareRule
{
    public $implicit = true;

    protected array $data = [];

    public function __construct(
        protected int $length,
        protected bool $required = false,
    ) {}

    public function setData(array $data): static
    {
        $this->data = $data;
        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $rules = [
            "$attribute" => [Rule::requiredIf($this->required), 'array'],
        ];

        if (is_array($value)) {
            foreach ($value as $lang => $text) {
                $rules["$attribute.$lang"] = [Rule::requiredIf(fn() => $lang === app()->getLocale() && $this->required), 'nullable', 'string', "between:5,{$this->length}"];
            }
        }

        $validator = Validator::make($this->data, $rules);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $fail($error);
            }
        }
    }
}
