<?php

declare(strict_types=1);

namespace App\Shared\Services;

use App\Shared\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class AuthService
{
    public function authUser(array $userData)
    {
        $user = User::where('email', $userData['email'])->first();

        if (!$user) {

            $userData['password'] = Hash::make(Str::uuid()->toString());
            $userData['nickname'] = 'Cut' . uniqid();
            $userData['display_name'] = 'Cut' . uniqid();
            $userData['referral_code'] = Str::uuid()->toString();

            $user = User::create($userData)->assignRole('seller');
        }

        session()->regenerate();
        
        Auth::login($user);
    }
}
