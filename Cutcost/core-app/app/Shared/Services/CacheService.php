<?php

namespace App\Shared\Services;  

use Illuminate\Support\Facades\Cache;

final class CacheService
{
    public static function use(string $key, callable $callback)
    {
        return Cache::remember($key, config('cutcost.ttl'), $callback);
    }

    public static function destroy(string $key)
    {
        return Cache::forget($key);
    }

    public static function useBoolean(string $key)
    {
        if (Cache::has($key)) {
            return true;
        } else {
            Cache::set($key, true, config('cutcost.ttl'));
            return false;
        }
    }
}
