<?php


declare(strict_types=1);

namespace App\Shared\Services;

use App\Shared\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MediaService
{

    private function attachToModel(string $tempUuid, string $modelType, int $modelId)
    {
        Media::where('temp_uuid', $tempUuid)
            ->update([
                'model_type' => $modelType,
                'model_id'   => $modelId,
                'temp_uuid'  => null,
            ]);
    }

    public function storeRequest(
        array $upload,
        string $modelType,
        int $modelId
    ): void {

        $tempUuid = data_get($upload, 'temp_uuid');

        if ($tempUuid) {
            $this->attachToModel($tempUuid, $modelType, $modelId);
        }
    }

    public function updateRequest(
        array $upload,
        string $modelType,
        int $modelId
    ): void {

        if (data_get($upload, 'temp_uuid'))
            $this
                ->attachToModel(
                    $upload['temp_uuid'],
                    $modelType,
                    $modelId
                );

        // delete files
        if (data_get($upload, 'upload.deleted_ids'))
            $this->deleteById($upload['upload.deleted_ids']);
    }

    /**
     * Удалить медиа по id (одного или массива)
     *
     * @param int|int[] $ids
     */
    public function deleteById($ids): void
    {
        // Собираем коллекцию Media по переданным ID
        $query = Media::query();
        if (is_array($ids)) {
            $query->whereIn('id', $ids);
        } else {
            $query->where('id', $ids);
        }

        $medias = $query->get();

        foreach ($medias as $media) {
            // Удаляем файл из публичного диска
            Storage::disk('public')->delete($media->path);
            // Удаляем запись из БД
            $media->delete();
        }
    }


    /**
     * Сохраняет массив изображений:
     * - Если переданы modelType+modelId — сразу привязывает к модели
     * - Иначе — сохраняет во временные с temp_uuid
     *
     * @param \Illuminate\Http\UploadedFile[] $images
     * @param string|null                     $modelType
     * @param int|null                        $modelId
     * @param string|null                     $tempUuid
     * @return \App\Shared\Models\Media[]     загруженные модели Media
     */
    public function store(array $images, ?string $modelType = null, ?int $modelId = null, ?string $tempUuid = null): array
    {
        if (! $modelType && ! $modelId && ! $tempUuid) {
            $tempUuid = (string) Str::uuid();
        }

        $result = [];

        // Получаем текущий максимальный order_column
        $maxOrder = 0;
        if ($modelType && $modelId) {
            $maxOrder = Media::where('model_type', $modelType)
                ->where('model_id', $modelId)
                ->max('order_column') ?? 0;
        } elseif ($tempUuid) {
            $maxOrder = Media::where('temp_uuid', $tempUuid)
                ->max('order_column') ?? 0;
        }

        foreach ($images as $image) {
            $file = $this->compress($image);
            $path = $file->store('media', 'public');

            $mediaData = [
                'path'          => $path,
                'order_column'  => ++$maxOrder,
            ];

            if ($modelType && $modelId) {
                $model = (new $modelType)->findOrFail($modelId);
                $media = $model->media()->create($mediaData);
            } else {
                $media = Media::create(array_merge($mediaData, [
                    'temp_uuid' => $tempUuid,
                ]));
            }

            $result[] = $media;
        }

        return $result;
    }



    public function storeIn(UploadedFile $file, string $path): string
    {
        $file = $this->compress($file);
        return $file->store($path);
    }

    public function deleteIfExists(?string $path): void
    {
        if (isset($path) && Storage::exists($path)) {
            Storage::delete($path);
        }
    }

    private function compress(UploadedFile $file, ?int $quality = 40): UploadedFile
    {
        if (!extension_loaded('gd')) {
            throw new \RuntimeException('GD extension is not loaded.');
        }

        if ($quality < 0 || $quality > 100 || $quality % 10 !== 0) {
            throw new \InvalidArgumentException('Quality must be a multiple of 10 between 0 and 100');
        }

        // Пропускаем файлы меньше 1 МБ
        if ($file->getSize() <= 1 * 1024 * 1024) {
            return $file;
        }

        $mime = $file->getMimeType();
        $originalPath = $file->getPathname();

        $image = null;
        $saveFunction = null;

        switch ($mime) {
            case 'image/jpeg':
            case 'image/jpg':
                $image = imagecreatefromjpeg($originalPath);
                $saveFunction = fn($img, $path, $q) => imagejpeg($img, $path, $q);
                break;

            case 'image/png':
                $image = imagecreatefrompng($originalPath);
                $pngCompression = min(9, max(0, intval($quality / 10)));
                $saveFunction = fn($img, $path, $q) => imagepng($img, $path, $pngCompression);
                break;

            case 'image/gif':
                return $file;
                break;

            case 'image/webp':
                if (function_exists('imagecreatefromwebp')) {
                    $image = imagecreatefromwebp($originalPath);
                    $saveFunction = fn($img, $path, $q) => imagewebp($img, $path, $q);
                }
                break;

            case 'image/bmp':
            case 'image/x-ms-bmp':
                if (function_exists('imagecreatefrombmp')) {
                    $image = imagecreatefrombmp($originalPath);
                    $saveFunction = fn($img, $path, $q) => imagebmp($img, $path);
                    $quality = null;
                }
                break;

            default:
                // Unsupported type
                return $file;
        }

        if (!$image || !$saveFunction) {
            throw new \RuntimeException('Failed to process image for compression.');
        }

        // Временный путь
        $tmpPath = sys_get_temp_dir() . '/' . uniqid('compressed_', true) . '.' . $file->getClientOriginalExtension();

        // Сохраняем сжатое изображение
        $saveFunction($image, $tmpPath, $quality);

        imagedestroy($image);

        return new UploadedFile(
            $tmpPath,
            $file->getClientOriginalName(),
            $file->getClientMimeType(),
            null,
            true
        );
    }
}
