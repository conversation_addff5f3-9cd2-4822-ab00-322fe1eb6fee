<?php

declare(strict_types=1);

namespace App\Shared\Services;

use Illuminate\Database\Eloquent\Model;

final class MetaService
{
    private static array $meta = [];

    private static array $basicTags = [
        // Lithuanian
        'Nuolaidos',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON>inės nuolaid<PERSON>',
        'CutCost pasiūlymai',
        'Nemokamas pristatymas',
        'Nuolaidų kodai',
        '<PERSON><PERSON><PERSON>lyma<PERSON>',
        'Pirk pigiau',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'Tau<PERSON>k',
        'Elektronin<PERSON> prekyba',
        'Geriausios kainos',
        'Nuolaidų puslapis',
        '<PERSON><PERSON> prekė<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        'Cashback',
        '<PERSON><PERSON><PERSON> kaino<PERSON>',
        'CutCost kuponas',
        'Nuolaida šiandien',
        'Akcija internete',
        'Vartotojo nuolaida',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasiūly<PERSON>',
        '<PERSON><PERSON> nuolaidai',
        '<PERSON><PERSON><PERSON><PERSON>s CutCost',
        '<PERSON><PERSON><PERSON> nuolaid<PERSON>',
        '<PERSON><PERSON> pasiūly<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON> kodai',
        '<PERSON><PERSON><PERSON><PERSON>o patarimai',

        // Russian
        'Скидки',
        '<PERSON>у<PERSON>оны',
        '<PERSON><PERSON><PERSON>и<PERSON>',
        'Промокоды',
        'Дешевле',
        'Бесплатная доставка',
        'Экономия',
        'Онлайн покупки',
        'Лучшая цена',
        'Секретные предложения',
        'CutCost скидка',
        'Промокод CutCost',
        'Сегодняшние скидки',
        'Хит продаж',
        'Товары со скидкой',
        'Где купить дешевле',
        'Экономь с умом',
        'Шопинг онлайн',
        'Горящие предложения',
        'Лайфхаки экономии',
        'Распродажа',
        'Топ скидок',
        'Сэкономить деньги',
        'Скидка на товар',
        'Скидка на доставку',
        'Акции недели',
        'Купоны на скидку',
        'Сезонная распродажа',
        'Интернет-магазин',
        'Лучшая скидка',

        // English
        'Discounts',
        'Coupons',
        'Deals',
        'Promo codes',
        'Save money',
        'Free shipping',
        'Online shopping',
        'Best price',
        'CutCost coupon',
        'CutCost discount',
        'Flash sale',
        'Daily deals',
        'Limited offer',
        'Shop smart',
        'Cheap deals',
        'Affordable prices',
        'Hot offers',
        'Sale today',
        'Voucher codes',
        'Exclusive deals',
        'Bargain',
        'Save big',
        'Cashback deals',
        'Shopping discounts',
        'Online deals',
        'New deals',
        'Weekend sale',
        'Holiday discounts',
        'Last-minute deals',
        'Seasonal sale',
        'Money saving tips',
        'CutCost promo'
    ];

    public static function addMeta(string $name, string $content): void
    {
        static::$meta[$name] = $content;
    }

    public static function setMetaForModel(Model $model, array $options = []): void
    {
        $locale = app()->getLocale();


        $rawTitle = $model->title ?? $model->name ?? '';
        $rawDescription = $model->description ?? '';


        $title = self::resolveLocalizedField($rawTitle, $locale);
        $description = self::resolveLocalizedField($rawDescription, $locale);

        $image = $model?->images[0] ?? $options['image'] ?? null;

        $tags = array_merge(
            explode(', ', $model->tags ?? ''),
            $options['tags'] ?? []
        );

        static::setBasicMeta($title, $description, $image, $tags);
    }

    public static function setBasicMeta(
        string $title,
        string $description,
        ?string $image,
        array|string $tags = [],
    ) {

        $title .= ' | Great deals on CutCost';
        $description .= ' | Find sellers, partners, 
        create your own marketing strategy on CutCost.';

        if (is_string($tags)) {
            $tags = explode(', ', $tags);
        }

        array_push(
            $tags,
            ...static::$basicTags
        );

        // SEO tags
        static::addMeta('title', $title);
        static::addMeta('description', $description);
        static::addMeta('keywords', is_array($tags) ? implode(', ', $tags) : $tags);

        // Open Graph protocol
        static::addMeta('og:title', $title);
        static::addMeta('og:description', $description);

        if ($image)
            static::addMeta('og:image', $image);

        static::addMeta('og:type', 'website');
        static::addMeta('og:url', url()->current());
        static::addMeta('og:locale', app()->getLocale());
        static::addMeta('og:site_name', config('app.name'));

        // Twitter protocol
        static::addMeta('twitter:card', 'summary_large_image');
        static::addMeta('twitter:title', $title);
        static::addMeta('twitter:description', $description);

        if ($image)
            static::addMeta('twitter:image', $image);
        static::addMeta('twitter:site', '@cutcost');
        static::addMeta('twitter:creator', '@cutcost');

        // Robots & Canonical
        static::addMeta('robots', 'index, follow');
        static::addMeta('canonical', url()->current());
    }

    public static function render(): string
    {
        $html = '';
        foreach (static::$meta as $key => $value) {
            $attr = str_starts_with($key, 'og:') || str_starts_with($key, 'twitter:') ? 'property' : 'name';
            $html .= "<meta {$attr}=\"{$key}\" content=\"" . e($value) . "\" />\n";
        }
        return $html;
    }

    public static function cleanup(): void
    {
        static::$meta = [];
    }

    private static function resolveLocalizedField($value, string $locale): string
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return $decoded[$locale] ?? $decoded['en'] ?? '';
            }

            // @var string $value  
            return $value;
        }

        if (is_array($value)) {
            return $value[$locale] ?? $value['en'] ?? '';
        }

        return '';
    }
}
