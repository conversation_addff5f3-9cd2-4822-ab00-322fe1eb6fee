<?php

namespace App\Shared\Services\Pagination;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class PaginatorService
{
    protected Builder $query;
    protected Request $request;
    protected array $searchableColumns = [];
    protected array $filters = [];
    protected mixed $resourceTransformer;

    protected string $defaultSortColumn = 'id';
    protected string $defaultSortDirection = 'desc';

    public function __construct(protected string $modelClass)
    {
        $model = new $modelClass;

        $this->query   = $model->newQuery();
        $this->request = request();

        $table = $model->getTable();
        $this->defaultSortColumn = "$table.id";
    }

    /**
     * Инициализация по имени модели
     */
    public static function for(string $modelClass): self
    {
        return new self($modelClass);
    }

    /**
     * Инициализация по готовому Builder или Relation
     */
    public static function fromQuery(Builder|Relation $query): self
    {
        if ($query instanceof Relation) {
            // достаём Builder из Relation и модель-родитель
            $builder    = $query->getQuery();
            $modelClass = get_class($query->getRelated());
        } else {
            $builder    = $query;
            $modelClass = get_class($query->getModel());
        }

        $instance = new self($modelClass);
        $instance->query = $builder;

        return $instance;
    }

    /**
     * Задать колонки для полнотекстового поиска
     *
     * @param  string|string[]  $columns
     */
    public function search(string|array $columns): self
    {
        $this->searchableColumns = is_array($columns) ? $columns : [$columns];
        return $this;
    }

    /**
     * Задать список классов фильтров (по AbstractFilter)
     *
     * @param  string[]  $filters
     */
    public function filters(array $filters): self
    {
        $this->filters = $filters;
        return $this;
    }

    /**
     * Трансформация каждого элемента результата
     */
    public function transformWith(callable $callback): self
    {
        $this->resourceTransformer = $callback;
        return $this;
    }

    /**
     * Основной метод — возвращает пагинацию с мета‑данными
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        $this->applySearch();
        $this->applyFilters();

        // Проверяем, есть ли в запросе параметры сортировки
        $sortBy = $this->request->get('sort_by');
        $sortDir = $this->request->get('sort_dir');

        if (!$sortBy) {
            // Применяем сортировку по умолчанию, если не указан параметр сортировки
            $this->query->orderBy($this->defaultSortColumn, $this->defaultSortDirection);
        } else {
            // Если указана сортировка — применяем её
            $direction = strtolower($sortDir) === 'asc' ? 'asc' : 'desc';
            $this->query->orderBy($sortBy, $direction);
        }

        $paginator = $this->query
            ->paginate($perPage)
            ->appends($this->request->query());

        if ($this->resourceTransformer !== null) {
            $paginator->setCollection(
                $paginator->getCollection()->map($this->resourceTransformer)
            );
        }

        return $paginator->appends([
            'search' => request('search'),
            'searchable' => !empty($this->searchableColumns),
            'filters' => $this->filters,
            'sort_by' => $sortBy,
            'sort_dir' => $sortDir,
        ]);
    }


    /**
     * Применяем поиск по нескольким колонкам
     */
    protected function applySearch(): void
    {
        if (empty($this->searchableColumns)) {
            return;
        }

        if (! $this->request->filled('search')) {
            return;
        }

        $term = $this->request->get('search');
        // текущая локаль, например 'ru'

        $this->query->where(function (Builder $q) use ($term) {
            foreach ($this->searchableColumns as $col) {
                if (is_array($col)) {
                    $locale = app()->getLocale();
                    $q->orWhere("{$col}->{$locale}", 'like', "%{$term}%");
                } else {
                    $q->orWhere($col, 'like', "%{$term}%");
                }
            }
        });
    }


    /**
     * Применяем фильтры (классы-наследники AbstractFilter)
     */
    protected function applyFilters(): void
    {
        foreach ($this->filters as $filterClass) {
            /** @var \App\Filters\AbstractFilter $filter */
            $filter = new $filterClass($this->query, $this->request);
            $filter->apply();
        }
    }
}
