<?php

namespace App\Shared\Services;

use App\Shared\Models\User;
use App\Shared\Models\Referral;
use App\Shared\Events\UserRegisteredViaReferral;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class ReferralService
{
    /**
     * Generate a unique referral code
     */
    public function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        } while (User::where('referral_code', $code)->exists());

        return $code;
    }

    /**
     * Validate referral code and return referrer
     */
    public function validateReferralCode(string $code): ?User
    {
        return User::where('referral_code', $code)->first();
    }

    /**
     * Process user registration with referral
     */
    public function processReferralRegistration(
        User $user,
        string $referralCode,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): bool {
        try {
            DB::beginTransaction();

            // Validate referral code
            $referrer = $this->validateReferralCode($referralCode);
            if (!$referrer) {
                throw ValidationException::withMessages([
                    'referral_code' => 'Invalid referral code.'
                ]);
            }

            // Prevent self-referral
            if ($referrer->id === $user->id) {
                throw ValidationException::withMessages([
                    'referral_code' => 'You cannot refer yourself.'
                ]);
            }

            // Check for circular references
            if ($this->wouldCreateCircularReference($user, $referrer)) {
                throw ValidationException::withMessages([
                    'referral_code' => 'This referral would create a circular reference.'
                ]);
            }

            // Update user with referrer
            $user->update(['referrer_id' => $referrer->id]);

            // Fire event to create referral record and distribute rewards
            event(new UserRegisteredViaReferral(
                $user,
                $referrer,
                $referralCode,
                $ipAddress,
                $userAgent
            ));

            DB::commit();

            Log::info('Referral registration processed successfully', [
                'user_id' => $user->id,
                'referrer_id' => $referrer->id,
                'referral_code' => $referralCode,
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to process referral registration', [
                'user_id' => $user->id,
                'referral_code' => $referralCode,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Check if adding this referrer would create a circular reference
     */
    public function wouldCreateCircularReference(User $user, User $potentialReferrer): bool
    {
        // Check if the potential referrer is in the user's referral chain
        $currentUser = $potentialReferrer;
        $maxDepth = 10; // Prevent infinite loops
        $depth = 0;

        while ($currentUser->referrer && $depth < $maxDepth) {
            if ($currentUser->referrer->id === $user->id) {
                return true; // Circular reference detected
            }
            $currentUser = $currentUser->referrer;
            $depth++;
        }

        return false;
    }

    /**
     * Get referral statistics for a user
     */
    public function getReferralStatistics(User $user): array
    {
        $stats = $user->getReferralStats();
        
        // Add additional statistics
        $recentReferrals = $user->referrals()->recent(30)->count();
        $totalEarningsThisMonth = $user->cutsTransactions()
            ->where('created_at', '>=', now()->startOfMonth())
            ->where('status', 'completed')
            ->sum('amount');

        return array_merge($stats, [
            'recent_referrals' => $recentReferrals,
            'earnings_this_month' => $totalEarningsThisMonth,
            'referral_url' => $user->getReferralUrl(),
        ]);
    }

    /**
     * Get referral leaderboard
     */
    public function getReferralLeaderboard(int $limit = 10): array
    {
        return User::select('users.*')
            ->selectRaw('COUNT(referrals.id) as total_referrals')
            ->selectRaw('SUM(cuts_transactions.amount) as total_earnings')
            ->leftJoin('referrals', 'users.id', '=', 'referrals.referrer_id')
            ->leftJoin('cuts_transactions', 'users.id', '=', 'cuts_transactions.user_id')
            ->where('cuts_transactions.status', 'completed')
            ->groupBy('users.id')
            ->orderByDesc('total_earnings')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get detailed referral chain for a user
     */
    public function getReferralChainDetails(User $user): array
    {
        $chain = [];
        $currentUser = $user;
        $level = 0;

        // Go up the chain
        while ($currentUser->referrer && $level < 3) {
            $level++;
            $referrer = $currentUser->referrer;
            
            $chain[] = [
                'level' => $level,
                'user' => [
                    'id' => $referrer->id,
                    'name' => $referrer->name,
                    'email' => $referrer->email,
                    'avatar' => $referrer->avatar,
                ],
                'referral_date' => $currentUser->referralRecord?->registered_at,
                'total_earned_from_user' => $referrer->cutsTransactions()
                    ->where('source_user_id', $user->id)
                    ->where('status', 'completed')
                    ->sum('amount'),
            ];
            
            $currentUser = $referrer;
        }

        return $chain;
    }

    /**
     * Validate referral eligibility
     */
    public function isEligibleForReferral(User $user): bool
    {
        // User must not already have a referrer
        if ($user->wasReferred()) {
            return false;
        }

        // User must not be banned
        if ($user->is_banned) {
            return false;
        }

        // Add any other business rules here
        return true;
    }
}
