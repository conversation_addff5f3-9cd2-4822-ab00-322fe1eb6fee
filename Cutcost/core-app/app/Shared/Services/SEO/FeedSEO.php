<?php

namespace App\Shared\Services\SEO;

use App\Shared\Contracts\Services\SeoContract;
use App\Shared\Services\MetaService;

class FeedSEO implements SeoContract
{

    public function make(): void
    {
        // test
        $titleRu = 'CutCost — Главная лента скидок и купонов';
        $titleLt = 'CutCost — Pagrindinė nuolaidų ir kuponų srautas';
        $titleEn = 'CutCost — Main Feed of Discounts and Coupons';

        $descriptionRu = 'На CutCost вы найдете актуальные купоны, скидки и специальные предложения от местных бизнесов. Экономьте деньги и следите за новыми акциями.';
        $descriptionLt = 'CutCost rasite naujausius kuponus, nuolaidas ir specialius pasiūlymus iš vietinių verslų. Taupykite pinigus ir sekite naujas akcijas.';
        $descriptionEn = 'On CutCost, find the latest coupons, discounts, and special offers from local businesses. Save money and stay updated with new deals.';

        $keywordsRu = [
            'CutCost',
            'купоны',
            'скидки',
            'акции',
            'главная лента',
            'лучшие предложения',
            'местные бизнесы',
            'экономия',
            'распродажи',
            'специальные предложения',
            'купонные коды',
            'сэкономить',
            'покупки',
            'сделки',
            'локальные скидки',
            'сохранить деньги',
            'актуальные купоны',
            'обзор скидок'
        ];

        $keywordsLt = [
            'CutCost',
            'kuponai',
            'nuolaidos',
            'akcijos',
            'pagrindinis srautas',
            'geriausi pasiūlymai',
            'vietiniai verslai',
            'taupymas',
            'išpardavimai',
            'specialūs pasiūlymai',
            'kuponų kodai',
            'sutaupyti',
            'pirkimai',
            'sandoriai',
            'vietinės nuolaidos',
            'sutaupyti pinigų',
            'aktyvūs kuponai',
            'nuolaidų apžvalga'
        ];

        $keywordsEn = [
            'CutCost',
            'coupons',
            'discounts',
            'sales',
            'main feed',
            'best offers',
            'local businesses',
            'savings',
            'clearance',
            'special deals',
            'coupon codes',
            'save money',
            'shopping',
            'deals',
            'local discounts',
            'money saving',
            'active coupons',
            'discount overview'
        ];

        MetaService::setBasicMeta(
            "$titleRu | $titleLt | $titleEn",
            "$descriptionRu $descriptionLt $descriptionEn",
            public_path('assets/images/cutcost.png'),
            implode(', ', array_merge($keywordsRu, $keywordsLt, $keywordsEn))
        );
    }
}
