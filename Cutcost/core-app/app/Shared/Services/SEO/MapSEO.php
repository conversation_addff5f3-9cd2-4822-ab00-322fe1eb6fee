<?php

namespace App\Shared\Services\SEO;

use App\Shared\Services\MetaService;

final class MapSEO
{

    public function make(): void
    {
        // Заголовки на разных языках
        $titleRu = 'CutCost — лучшие скидки и предложения рядом с вами';
        $titleLt = 'CutCost — geriausi pasiūlymai šalia jūsų';
        $titleEn = 'CutCost - Find the best deals near you';

        // Описания на разных языках
        $descriptionRu = 'CutCost — платформа для поиска лучших скидок и акций в вашем районе. Откройте местные бизнесы и партнеров, создайте свою маркетинговую стратегию.';
        $descriptionLt = 'CutCost — platforma, padedanti rasti geriausias nuolaidas ir pasiūlymus jūsų aplinkoje. Atraskite vietinius verslus ir partnerius, kurkite savo rinkodaros strategiją.';
        $descriptionEn = 'CutCost is a platform that helps you find the best deals near you. Discover local businesses, partners, and create your own marketing strategy on CutCost.';

        // Ключевые слова расширенные на 3 языках
        $keywordsRu = [
            'CutCost',
            'скидки',
            'предложения',
            'местные бизнесы',
            'магазины рядом',
            'партнеры',
            'маркетинговая стратегия',
            'распродажи',
            'купоны',
            'лучшие акции',
            'локальный бизнес',
            'поиск скидок',
            'ближайшие магазины',
            'экономия',
            'онлайн платформа'
        ];

        $keywordsLt = [
            'CutCost',
            'nuolaidos',
            'pasiūlymai',
            'vietiniai verslai',
            'parduotuvės šalia',
            'partneriai',
            'rinkodaros strategija',
            'išpardavimai',
            'kuponai',
            'geriausios akcijos',
            'lokalus verslas',
            'nuolaidų paieška',
            'artimiausios parduotuvės',
            'taupymas',
            'internetinė platforma'
        ];

        $keywordsEn = [
            'CutCost',
            'discounts',
            'deals',
            'local businesses',
            'stores near you',
            'partners',
            'marketing strategy',
            'sales',
            'coupons',
            'best offers',
            'local business',
            'deal finder',
            'nearest stores',
            'savings',
            'online platform'
        ];

        MetaService::setBasicMeta(
            "$titleRu | $titleLt | $titleEn",
            "$descriptionRu $descriptionLt $descriptionEn",
            null,
            implode(', ', array_merge($keywordsRu, $keywordsLt, $keywordsEn))
        );
    }
}
