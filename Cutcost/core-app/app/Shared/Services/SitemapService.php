<?php

namespace App\Shared\Services;

use Illuminate\Support\Facades\Route;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

final class SitemapService
{
    public function generate(): void
    {
        $appUrl = 'https://cutcost.net';

        $excludedPatterns = [
            '^_debugbar',
            '^sanctum',
            '^broadcasting',
            '^auth',
            '^dashboard',
            '^profile',
            '^settings',
            '^notifications',
            '^my-coupons',
            '^bookmarks',
            '^ref/',
            '^company/create',
            'create$',
            'generate-sitemap',
            'csrf-cookie',
            'authenticate'
        ];

        $sitemap = Sitemap::create();

        foreach (Route::getRoutes() as $route) {
            if (!in_array('GET', $route->methods())) continue;

            $uri = $route->uri();
            if (strpos($uri, '{') !== false) continue;

            $shouldExclude = false;
            foreach ($excludedPatterns as $pattern) {
                if (preg_match("#$pattern#", $uri)) {
                    $shouldExclude = true;
                    break;
                }
            }

            if (!$shouldExclude) {
                $sitemap->add(Url::create($appUrl . '/' . ltrim($uri, '/')));
            }
        }

        $sitemap->add(Url::create($appUrl . '/')); // Добавим главную

        $sitemap->writeToFile(public_path('sitemap.xml'));
    }
}
