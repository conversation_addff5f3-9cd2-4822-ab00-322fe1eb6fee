<?php

namespace App\Shared\Services;

use App\Shared\Models\CutsPlan;
use App\Shared\Models\CutsSubscription;
use App\Shared\Models\PaymentMethod;
use App\Shared\Models\PaymentVerification;
use App\Shared\Models\User;
use App\Shared\Models\WalletTransaction;
use Illuminate\Support\Facades\Log;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod as StripePaymentMethod;
use Stripe\Price;
use Stripe\Product;
use Stripe\SetupIntent;
use Stripe\Stripe;
use Stripe\Subscription;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create or get Stripe customer for user.
     */
    public function createOrGetCustomer(User $user): Customer
    {
        try {
            // Check if user already has a Stripe customer ID
            if ($user->stripe_customer_id) {
                return Customer::retrieve($user->stripe_customer_id);
            }

            // Create new Stripe customer
            $customer = Customer::create([
                'email' => $user->email,
                'name' => $user->display_name ?? $user->nickname,
                'metadata' => [
                    'user_id' => $user->id,
                    'platform' => 'cutcost',
                ],
            ]);

            // Save customer ID to user
            $user->update(['stripe_customer_id' => $customer->id]);

            return $customer;
        } catch (ApiErrorException $e) {
            Log::error('Stripe customer creation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Create setup intent for adding payment method.
     */
    public function createSetupIntent(User $user): SetupIntent
    {
        try {
            $customer = $this->createOrGetCustomer($user);

            return SetupIntent::create([
                'customer' => $customer->id,
                'usage' => 'off_session',
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Stripe setup intent creation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Save payment method from Stripe.
     */
    public function savePaymentMethod(User $user, string $paymentMethodId): PaymentMethod
    {
        try {
            $customer = $this->createOrGetCustomer($user);
            
            // Retrieve payment method from Stripe
            $stripePaymentMethod = StripePaymentMethod::retrieve($paymentMethodId);
            
            // Attach to customer if not already attached
            if (!$stripePaymentMethod->customer) {
                $stripePaymentMethod->attach(['customer' => $customer->id]);
            }

            // Save to database
            return PaymentMethod::createFromStripe($user, $stripePaymentMethod->toArray());
        } catch (ApiErrorException $e) {
            Log::error('Payment method save failed', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethodId,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Create payment intent for verification.
     */
    public function createVerificationPayment(User $user, string $paymentMethodId): PaymentIntent
    {
        try {
            $customer = $this->createOrGetCustomer($user);

            $paymentIntent = PaymentIntent::create([
                'amount' => PaymentVerification::VERIFICATION_AMOUNT * 100, // Convert to cents
                'currency' => 'eur',
                'customer' => $customer->id,
                'payment_method' => $paymentMethodId,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => route('wallet.verification.return'),
                'metadata' => [
                    'user_id' => $user->id,
                    'type' => 'verification',
                ],
            ]);

            // Create verification record
            PaymentVerification::createForUser($user, $paymentIntent->id, $paymentMethodId);

            return $paymentIntent;
        } catch (ApiErrorException $e) {
            Log::error('Verification payment creation failed', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethodId,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Create payment intent for cuts purchase.
     */
    public function createCutsPurchasePayment(User $user, CutsPlan $plan, string $paymentMethodId): PaymentIntent
    {
        try {
            $customer = $this->createOrGetCustomer($user);

            $paymentIntent = PaymentIntent::create([
                'amount' => $plan->price * 100, // Convert to cents
                'currency' => 'eur',
                'customer' => $customer->id,
                'payment_method' => $paymentMethodId,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => route('wallet.purchase.return'),
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'type' => 'cuts_purchase',
                ],
            ]);

            // Create wallet transaction
            WalletTransaction::createDeposit(
                $user,
                $plan->price,
                $plan->total_cuts,
                'stripe',
                $paymentIntent->id
            );

            return $paymentIntent;
        } catch (ApiErrorException $e) {
            Log::error('Cuts purchase payment creation failed', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Create Stripe product and price for plan.
     */
    public function createPlanProduct(CutsPlan $plan): array
    {
        try {
            // Create product
            $product = Product::create([
                'name' => $plan->getLocalizedName(),
                'description' => $plan->getLocalizedDescription(),
                'metadata' => [
                    'plan_id' => $plan->id,
                    'cuts_amount' => $plan->cuts_amount,
                    'bonus_cuts' => $plan->bonus_cuts,
                ],
            ]);

            // Create price
            $priceData = [
                'product' => $product->id,
                'unit_amount' => $plan->price * 100, // Convert to cents
                'currency' => 'eur',
                'metadata' => [
                    'plan_id' => $plan->id,
                ],
            ];

            // Add recurring data if plan is recurring
            if ($plan->isRecurring()) {
                $priceData['recurring'] = [
                    'interval' => $plan->interval === CutsPlan::INTERVAL_YEARLY ? 'year' : 'month',
                ];
            }

            $price = Price::create($priceData);

            return [
                'product' => $product,
                'price' => $price,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe product creation failed', [
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Create subscription for recurring plan.
     */
    public function createSubscription(User $user, CutsPlan $plan, string $paymentMethodId): Subscription
    {
        try {
            $customer = $this->createOrGetCustomer($user);

            // Create or get Stripe price
            $stripeData = $this->createPlanProduct($plan);

            $subscription = Subscription::create([
                'customer' => $customer->id,
                'items' => [
                    ['price' => $stripeData['price']->id],
                ],
                'default_payment_method' => $paymentMethodId,
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                ],
            ]);

            // Create subscription record
            CutsSubscription::createForUser($user, $plan, [
                'subscription_id' => $subscription->id,
                'customer_id' => $customer->id,
                'price_id' => $stripeData['price']->id,
            ]);

            return $subscription;
        } catch (ApiErrorException $e) {
            Log::error('Stripe subscription creation failed', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancelSubscription(CutsSubscription $subscription): bool
    {
        try {
            if ($subscription->stripe_subscription_id) {
                $stripeSubscription = Subscription::retrieve($subscription->stripe_subscription_id);
                $stripeSubscription->cancel();
            }

            $subscription->cancel();
            return true;
        } catch (ApiErrorException $e) {
            Log::error('Stripe subscription cancellation failed', [
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle webhook events.
     */
    public function handleWebhook(array $event): bool
    {
        try {
            switch ($event['type']) {
                case 'payment_intent.succeeded':
                    return $this->handlePaymentIntentSucceeded($event['data']['object']);
                
                case 'payment_intent.payment_failed':
                    return $this->handlePaymentIntentFailed($event['data']['object']);
                
                case 'customer.subscription.updated':
                case 'customer.subscription.deleted':
                    return $this->handleSubscriptionUpdated($event['data']['object']);
                
                default:
                    Log::info('Unhandled Stripe webhook event', ['type' => $event['type']]);
                    return true;
            }
        } catch (\Exception $e) {
            Log::error('Stripe webhook handling failed', [
                'event_type' => $event['type'],
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle successful payment intent.
     */
    private function handlePaymentIntentSucceeded(array $paymentIntent): bool
    {
        $metadata = $paymentIntent['metadata'] ?? [];
        
        if ($metadata['type'] === 'verification') {
            $verification = PaymentVerification::where('stripe_payment_intent_id', $paymentIntent['id'])->first();
            if ($verification) {
                $verification->markAsCompleted();
            }
        } elseif ($metadata['type'] === 'cuts_purchase') {
            $transaction = WalletTransaction::where('payment_id', $paymentIntent['id'])->first();
            if ($transaction) {
                $transaction->markAsCompleted();
                // Add cuts to user balance
                $transaction->user->addCuts($transaction->cuts_amount, 'Cuts purchase completed');
            }
        }

        return true;
    }

    /**
     * Handle failed payment intent.
     */
    private function handlePaymentIntentFailed(array $paymentIntent): bool
    {
        $metadata = $paymentIntent['metadata'] ?? [];
        
        if ($metadata['type'] === 'verification') {
            $verification = PaymentVerification::where('stripe_payment_intent_id', $paymentIntent['id'])->first();
            if ($verification) {
                $verification->markAsFailed('Payment failed');
            }
        } elseif ($metadata['type'] === 'cuts_purchase') {
            $transaction = WalletTransaction::where('payment_id', $paymentIntent['id'])->first();
            if ($transaction) {
                $transaction->markAsFailed('Payment failed');
            }
        }

        return true;
    }

    /**
     * Handle subscription updates.
     */
    private function handleSubscriptionUpdated(array $subscription): bool
    {
        $cutsSubscription = CutsSubscription::where('stripe_subscription_id', $subscription['id'])->first();
        if ($cutsSubscription) {
            $cutsSubscription->updateFromStripe($subscription);
        }

        return true;
    }
}
