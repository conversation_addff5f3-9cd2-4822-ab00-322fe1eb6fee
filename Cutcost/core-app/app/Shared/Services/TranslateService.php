<?php

namespace App\Shared\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class TranslateService
{
    private Client $httpClient;
    private string $apiUrl;

    public function __construct()
    {
        $this->apiUrl = env('TRANSLATE_API_URL');
        $this->httpClient = new Client([
            'base_uri' => $this->apiUrl,
            'timeout'  => 10.0,
        ]);
    }

    public function translate(string $text, string $sourceLang, string $targetLang): string
    {
        $response = $this->httpClient->post('/translate', [
            'json' => [
                'q' => $text,
                'source' => $sourceLang,
                'target' => $targetLang,
                'format' => 'text'
            ]
        ]);

        $data = json_decode($response->getBody()->getContents(), true);

        return $data['translatedText'] ?? $text;
    }

    public function detect(string|array|null $text): ?string
    {

        if (is_array($text)) {
            // get first non-empty value
            $textToDetect = array_shift(array_filter($text, fn($v) => trim((string)$v) !== '')) ?: null;
        } else {
            $textToDetect = $text ?? null;
        }

        if (!$textToDetect) return null;

        try {
            $response = $this->httpClient->post('/detect', [
                'json' => ['q' => $textToDetect],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return data_get($data, '0.language', null);
        } catch (GuzzleException $e) {
            Log::info($e->getMessage());
            return null;
        }
    }
}
