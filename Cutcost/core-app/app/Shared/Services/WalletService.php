<?php

namespace App\Shared\Services;

use App\Shared\Models\CutsPlan;
use App\Shared\Models\CutsSubscription;
use App\Shared\Models\PaymentMethod;
use App\Shared\Models\PaymentVerification;
use App\Shared\Models\User;
use App\Shared\Models\WalletTransaction;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WalletService
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Get user's wallet overview.
     */
    public function getWalletOverview(User $user): array
    {
        return [
            'wallet_balance' => $user->getWalletBalance(),
            'cuts_balance' => $user->getCutsBalance(),
            'formatted_wallet_balance' => $user->formatted_wallet_balance,
            'formatted_cuts_balance' => $user->formatted_cuts_balance,
            'is_payment_verified' => $user->isPaymentVerified(),
            'has_active_subscription' => $user->hasActiveSubscription(),
            'active_subscription' => $user->getActiveSubscription(),
            'default_payment_method' => $user->getDefaultPaymentMethod(),
            'total_spent' => $this->getTotalSpent($user),
            'total_earned' => $this->getTotalEarned($user),
        ];
    }

    /**
     * Get user's transaction history.
     */
    public function getTransactionHistory(User $user, array $filters = []): Collection
    {
        $query = $user->walletTransactions()->latest();

        // Apply filters
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        $limit = $filters['limit'] ?? 50;
        return $query->limit($limit)->get();
    }

    /**
     * Process payment verification.
     */
    public function processPaymentVerification(User $user, string $paymentMethodId): array
    {
        try {
            // Check if user already has pending verification
            if (PaymentVerification::hasPendingVerification($user)) {
                return [
                    'success' => false,
                    'message' => 'You already have a pending verification payment.',
                ];
            }

            // Check if user is already verified
            if ($user->isPaymentVerified()) {
                return [
                    'success' => false,
                    'message' => 'Your account is already verified.',
                ];
            }

            DB::beginTransaction();

            // Create Stripe payment intent
            $paymentIntent = $this->stripeService->createVerificationPayment($user, $paymentMethodId);

            DB::commit();

            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'client_secret' => $paymentIntent->client_secret,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment verification failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed. Please try again.',
            ];
        }
    }

    /**
     * Purchase cuts plan.
     */
    public function purchaseCutsPlan(User $user, CutsPlan $plan, string $paymentMethodId): array
    {
        try {
            if (!$plan->is_active) {
                return [
                    'success' => false,
                    'message' => 'This plan is not available.',
                ];
            }

            DB::beginTransaction();

            if ($plan->isRecurring()) {
                // Create subscription for recurring plans
                $subscription = $this->stripeService->createSubscription($user, $plan, $paymentMethodId);
                
                DB::commit();

                return [
                    'success' => true,
                    'type' => 'subscription',
                    'subscription' => $subscription,
                ];
            } else {
                // Create one-time payment for non-recurring plans
                $paymentIntent = $this->stripeService->createCutsPurchasePayment($user, $plan, $paymentMethodId);
                
                DB::commit();

                return [
                    'success' => true,
                    'type' => 'payment',
                    'payment_intent' => $paymentIntent,
                    'client_secret' => $paymentIntent->client_secret,
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cuts plan purchase failed', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Purchase failed. Please try again.',
            ];
        }
    }

    /**
     * Cancel user's subscription.
     */
    public function cancelSubscription(User $user): array
    {
        try {
            $subscription = $user->getActiveSubscription();
            
            if (!$subscription) {
                return [
                    'success' => false,
                    'message' => 'No active subscription found.',
                ];
            }

            $cancelled = $this->stripeService->cancelSubscription($subscription);

            if ($cancelled) {
                return [
                    'success' => true,
                    'message' => 'Subscription cancelled successfully.',
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to cancel subscription. Please try again.',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Subscription cancellation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel subscription. Please contact support.',
            ];
        }
    }

    /**
     * Add payment method for user.
     */
    public function addPaymentMethod(User $user, string $paymentMethodId): array
    {
        try {
            $paymentMethod = $this->stripeService->savePaymentMethod($user, $paymentMethodId);

            return [
                'success' => true,
                'payment_method' => $paymentMethod,
            ];
        } catch (\Exception $e) {
            Log::error('Payment method addition failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to add payment method. Please try again.',
            ];
        }
    }

    /**
     * Remove payment method.
     */
    public function removePaymentMethod(User $user, PaymentMethod $paymentMethod): array
    {
        try {
            if ($paymentMethod->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Payment method not found.',
                ];
            }

            $paymentMethod->deactivate();

            return [
                'success' => true,
                'message' => 'Payment method removed successfully.',
            ];
        } catch (\Exception $e) {
            Log::error('Payment method removal failed', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to remove payment method. Please try again.',
            ];
        }
    }

    /**
     * Set default payment method.
     */
    public function setDefaultPaymentMethod(User $user, PaymentMethod $paymentMethod): array
    {
        try {
            if ($paymentMethod->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Payment method not found.',
                ];
            }

            $paymentMethod->setAsDefault();

            return [
                'success' => true,
                'message' => 'Default payment method updated.',
            ];
        } catch (\Exception $e) {
            Log::error('Default payment method update failed', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to update default payment method.',
            ];
        }
    }

    /**
     * Get available cuts plans.
     */
    public function getAvailablePlans(): Collection
    {
        return CutsPlan::active()
            ->regular()
            ->orderBy('sort_order')
            ->orderBy('price')
            ->get();
    }

    /**
     * Get verification plan.
     */
    public function getVerificationPlan(): ?CutsPlan
    {
        return CutsPlan::active()
            ->verification()
            ->first();
    }

    /**
     * Get wallet statistics.
     */
    public function getWalletStatistics(User $user): array
    {
        $transactions = $user->walletTransactions()->completed();

        return [
            'total_transactions' => $transactions->count(),
            'total_deposits' => $transactions->ofType(WalletTransaction::TYPE_DEPOSIT)->sum('amount'),
            'total_purchases' => $transactions->ofType(WalletTransaction::TYPE_PURCHASE)->sum('amount'),
            'total_cuts_earned' => $transactions->whereNotNull('cuts_amount')->sum('cuts_amount'),
            'avg_transaction_amount' => $transactions->avg('amount') ?? 0,
            'last_transaction_date' => $transactions->latest()->first()?->created_at,
        ];
    }

    /**
     * Get total amount spent by user.
     */
    private function getTotalSpent(User $user): float
    {
        return $user->walletTransactions()
            ->completed()
            ->whereIn('type', [WalletTransaction::TYPE_PURCHASE, WalletTransaction::TYPE_WITHDRAWAL])
            ->sum('amount');
    }

    /**
     * Get total amount earned by user.
     */
    private function getTotalEarned(User $user): float
    {
        return $user->walletTransactions()
            ->completed()
            ->whereIn('type', [WalletTransaction::TYPE_DEPOSIT, WalletTransaction::TYPE_BONUS, WalletTransaction::TYPE_REFERRAL])
            ->sum('amount');
    }

    /**
     * Process cuts spending.
     */
    public function spendCuts(User $user, int $amount, string $description, array $metadata = []): array
    {
        try {
            if ($user->getCutsBalance() < $amount) {
                return [
                    'success' => false,
                    'message' => 'Insufficient cuts balance.',
                ];
            }

            DB::beginTransaction();

            $success = $user->deductCuts($amount, $description);

            if ($success) {
                DB::commit();
                return [
                    'success' => true,
                    'new_balance' => $user->getCutsBalance(),
                ];
            } else {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Failed to process cuts spending.',
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cuts spending failed', [
                'user_id' => $user->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process cuts spending.',
            ];
        }
    }
}
