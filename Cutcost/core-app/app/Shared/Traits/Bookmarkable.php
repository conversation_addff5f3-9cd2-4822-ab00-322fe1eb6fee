<?php

namespace App\Shared\Traits;

use App\Shared\Models\Bookmark;

trait Bookmarkable
{
    public function bookmarks()
    {
        return $this->morphMany(Bookmark::class, 'bookmarkable');
    }

    public function bookmarkers()
    {
        return $this->bookmarks()->with('user');
    }

    public function isBookmarkedBy($user): bool
    {
        $userId = is_int($user) ? $user : $user->id;
        return (bool) $this->bookmarks()->where('user_id', $userId)->exists();
    }
}
