<?php

declare(strict_types=1);

namespace App\Shared\Traits;

use App\Shared\Models\FileDriver;

// deprecated use FileDriverService instead
trait CutcostFileDriver
{

    /**
     * Used for Controllers for uploading files // Use it for upload fronted component
     * @return array
     */
    public function withDriver(): array
    {
        $driver = $this->getDriver();
        return array_reverse($driver->only('paths')['paths']);
    }



    /**
     * Used by fileDriverController
     * @return FileDriver
     */
    public function getDriver(): FileDriver
    {
        $driver = auth()->user()->fileDriver()->first();

        if (empty($driver->user_id)) {
            $driver = FileDriver::create([
                'paths' => [],
                'user_id' => auth()->id(),
            ]);
        }

        return $driver;
    }
}
