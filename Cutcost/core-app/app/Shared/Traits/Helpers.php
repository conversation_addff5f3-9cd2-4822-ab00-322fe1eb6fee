<?php

declare(strict_types=1);

namespace App\Shared\Traits;

use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Location;
use App\Modules\Seller\Models\Post;
use App\Shared\Models\Comment;

trait Helpers
{
    public function classFromType(string $type): string
    {
        
        $type = mb_strtolower($type, 'UTF-8');

        return [
            'post' => Post::class,
            'coupon' => Coupon::class,
            'comment' => Comment::class,
            'company' => Company::class,
            'location' => Location::class,
            
        ][$type];
    }
}
