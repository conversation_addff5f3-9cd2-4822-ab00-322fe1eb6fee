<?php

namespace App\Shared\Traits;

use App\Shared\Models\Like;

trait Likable
{
    public function likes()
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    public function likers()
    {
        return $this->likes()->liked()->with('user');
    }

    public function isLikedBy($user): bool
    {
        $userId = is_int($user) ? $user : $user->id;
        return (bool) $this->likes()->where('user_id', $userId)->where('value', 1)->exists();
    }
}
