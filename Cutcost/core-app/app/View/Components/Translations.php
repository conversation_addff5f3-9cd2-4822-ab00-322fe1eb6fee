<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\View\Component;

class Translations extends Component
{
    public function render(): View|Closure|string
    {
        $translations = [];

        if (app()->environment(['local', 'testing'])) {
            $translations = $this->withoutCache();
        } else {
            $translations = $this->withCache();
        }

        return view(
            'components.translations',
            ['translations' => $translations]
        );
    }

    protected function withoutCache()
    {
        return $this->translations();
    }

    protected function withCache()
    {
        $locale = App::getLocale();

        return Cache::rememberForever(
            "translations_$locale",
            function () {
                return $this->translations();
            }
        );
    }

    protected function translations()
    {
        $locale = App::getLocale();

        $jsonTranslations = [];

        $localePath = resource_path("lang/$locale");

        if (File::exists($localePath) && File::isDirectory($localePath)) {
            $jsonFiles = File::allFiles($localePath);
            foreach ($jsonFiles as $jsonFile) {
                if ($jsonFile->getExtension() === 'json') {
                    $jsonTranslations = array_merge($jsonTranslations, json_decode(File::get($jsonFile->getRealPath()), true));
                }
            }
        }

        return $jsonTranslations;
    }
}
