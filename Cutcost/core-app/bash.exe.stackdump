Stack trace:
Frame         Function      Args
0007FFFFB6C0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5C0) msys-2.0.dll+0x2118E
0007FFFFB6C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6C0  0002100469F2 (00021028DF99, 0007FFFFB578, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6C0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6C0  00021006A545 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD48CD0000 ntdll.dll
7FFD47150000 KERNEL32.DLL
7FFD46330000 KERNELBASE.dll
7FFD46B60000 USER32.dll
7FFD467D0000 win32u.dll
7FFD47FE0000 GDI32.dll
7FFD46800000 gdi32full.dll
7FFD45F60000 msvcp_win.dll
7FFD45DC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD47450000 advapi32.dll
7FFD47220000 msvcrt.dll
7FFD48140000 sechost.dll
7FFD46930000 bcrypt.dll
7FFD47330000 RPCRT4.dll
7FFD45550000 CRYPTBASE.DLL
7FFD45EE0000 bcryptPrimitives.dll
7FFD48C50000 IMM32.DLL
