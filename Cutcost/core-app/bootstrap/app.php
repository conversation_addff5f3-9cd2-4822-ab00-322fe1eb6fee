<?php

use App\Modules\Partner\Http\Middleware\CompanyMiddleware;
use App\Shared\Http\Middleware\AuthMiddleware;
use App\Shared\Http\Middleware\HandleInertiaRequests;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;
use Inertia\Inertia;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
        using: function () {

            // CORE SHARED ROUTES 
            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            # BASE APP MODULES

            ## PARTNER ROUTES
            Route::middleware([
                'web',
                AuthMiddleware::class,
                CompanyMiddleware::class,
            ])
                ->prefix('partner/')
                ->name('partner.')
                ->group(base_path('routes/partner.php'));

            ## PUBLIC ROUTES
            Route::middleware('web')
                ->group(base_path('routes/public.php'));

            ## SELLER ROUTES
            Route::middleware([
                'web',
                AuthMiddleware::class
            ])
                ->name('seller.')
                ->group(base_path('routes/seller.php'));


            # APP MODULES

            ## GAME COSTSABER 
            Route::middleware('web')
                ->prefix('costsaber')
                ->name('game.')
                ->group(base_path('routes/game.php'));

            ## MESSENGER CUTTALK 
            Route::middleware([
                'web',
                AuthMiddleware::class
            ])
                ->prefix('cuttalk')
                ->name('messenger.')
                ->group(base_path('routes/messenger.php'));

            ## WAllET

            Route::middleware([
                'web',
                // 'auth' TODO: create wallet
            ])
                ->name('wallet.')
                ->group(base_path('routes/wallet.php'));


            // api
            Route::middleware([
                'web',
            ])
                ->name('api.')
                ->prefix('api')
                ->group(base_path('routes/api.php'));
        },


    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            HandleInertiaRequests::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->respond(function (Response $response, Throwable $exception, Request $request) {
            if (app()->isProduction() && in_array($response->getStatusCode(), [500, 503, 404, 403])) {
                return to_route('feed');
            } elseif ($response->getStatusCode() === 419) {
                return to_route('feed')->with([
                    'info' => 'The page expired, please try again.',
                ]);
            }

            return $response;
        });
    })->create();
