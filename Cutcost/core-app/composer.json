{"$schema": "https://getcomposer.org/schema.json", "name": "cutcost/cutcost", "type": "project", "description": "Cutcost discount, social network and messenger", "keywords": ["cutcost", "discount"], "license": "MIT", "require": {"php": "^8.4.5", "inertiajs/inertia-laravel": "2.0.1", "laravel/framework": "^12", "laravel/octane": "^2.11", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "spatie/laravel-permission": "^6.13", "spatie/laravel-sitemap": "^7.3", "stripe/stripe-php": "^18.0", "tightenco/ziggy": "^2.5", "workos/workos-php-laravel": "^4.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}