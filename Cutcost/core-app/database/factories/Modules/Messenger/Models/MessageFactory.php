<?php

namespace Database\Factories\Messenger\Models;

use App\Modules\Messenger\Models\Message;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;


class MessageFactory extends Factory
{
    protected $model = Message::class;

    public function definition(): array
    {
        $users = User::whereIn('email', ['1@1.1', '2@2.2', '3@3.3'])->get();

        if ($users->isEmpty()) {
            throw new \Exception('No users found for the specified emails.');
        }


        return [
            'content' => $this->faker->sentence(),
            'messageable_type' => 'App\Modules\Messenger\Models\Chat',
            'status' => 'seen',
            'messageable_id' => 1,
            'user_id' => $users[random_int(0, 1)],
        ];
    }
}
