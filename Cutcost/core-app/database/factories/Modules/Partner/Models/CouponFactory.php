<?php

namespace Database\Factories\Modules\Partner\Models;

use App\Modules\Partner\Models\Coupon;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Coupon>
 */
class CouponFactory extends Factory
{
    protected $model = Coupon::class;

    public function definition(): array
    {

        $types = ['free', 'subscription', 'paid_with_money', 'paid_with_stars'];
        $type =  array_rand($types);

        $users = [User::where('email', '1@1.1')->first()->id, 2];

        $user = $users[random_int(0, 1)];

        return [
            'type' => $types[$type],
            'couponable_type' => 'App\Shared\Models\User',
            'couponable_id' => $user,
            'name' => [
                'en' => $this->faker->state,
                'lt' => $this->faker->state,
                'ru' => $this->faker->state,
            ],
            'images' => ['/user_files/PNDmqxVOpoGBhRVtoqIxf2aKG39HO1oTPjEuW9El.jpg'],
            'price_in_store' => random_int(1, 20),
            'coupon_price' => random_int(1, 1000),
            'buy_price' => random_int(1, 100),
            'subscriptions_prices' => [
                'day_subscription' => random_int(1, 20),
                'week_subscription' => random_int(1, 20),
                'month_subscription' => random_int(1, 20),
            ],
            'discount' => '10%',

            'slug' => $this->faker->slug,
            'description' => [
                'en' => $this->faker->paragraph,
                'lt' => $this->faker->paragraph,
                'ru' => $this->faker->paragraph,
            ],
            'is_draft' => false,
            'user_id' => $user,
        ];
    }
}
