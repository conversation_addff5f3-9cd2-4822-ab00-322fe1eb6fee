<?php

namespace Database\Factories\Modules\Partner\Models;

use App\Modules\Partner\Models\Location;
use App\Shared\Models\City;
use App\Shared\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Location>
 */
class LocationFactory extends Factory
{
    protected $model = Location::class;

    public function definition()
    {
        $user = User::where('email', '1@1.1')->first();
        return [
            'name' => [
                'en' => $this->faker->company,
                'lt' => $this->faker->company,
                'ru' => $this->faker->company,
            ],
            'slug' => $this->faker->slug,
            'description' => [
                'en' => $this->faker->paragraph,
                'lt' => $this->faker->paragraph,
                'ru' => $this->faker->paragraph,
            ],
            'address' => $this->faker->address,
            'latlng' => ['lat' => $this->faker->latitude, 'lng' => $this->faker->longitude],
            'rent_expires_at' => $this->faker->dateTimeBetween('now', '+1 year'),
            'working_hours' => json_decode('
            [
    {
        "type": "work_days",
        "breaks": [],
        "timeEnd": "14:00",
        "weekdays": [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday"
        ],
        "timeStart": "8:00"
    },
    {
        "type": "off_days",
        "breaks": null,
        "timeEnd": null,
        "weekdays": [
            "Saturday"
        ],
        "timeStart": null
    },
    {
        "type": "work_days",
        "breaks": [],
        "timeEnd": "23:00",
        "weekdays": [
            "Sunday"
        ],
        "timeStart": "12:00"
    }
]
            '),
            'is_open' => $this->faker->boolean,
            'images' => ['test.jpg'],
            'locationable_type' => 'App\Shared\Models\User',
            'locationable_id' => $user->id,
            'type' => $this->faker->randomElement(['online', 'office', 'event', 'shop']),
            'city_id' => City::inRandomOrder()->first()->id, // Get a random city ID
            'user_id' => $user->id,
        ];
    }
}
