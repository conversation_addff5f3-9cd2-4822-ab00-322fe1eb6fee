<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            // Add fields for help system
            $table->string('type')->default('article')->index(); // 'article', 'help', 'faq'
            $table->string('category')->nullable()->index(); // Help category
            $table->text('excerpt')->nullable(); // Short description
            $table->boolean('is_published')->default(true)->index();
            $table->integer('order')->default(0)->index(); // Display order
            $table->json('meta')->nullable(); // Additional metadata
            
            // Add indexes for better performance
            $table->index(['type', 'is_published', 'order']);
            $table->index(['type', 'category', 'is_published']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->dropIndex(['type', 'is_published', 'order']);
            $table->dropIndex(['type', 'category', 'is_published']);
            $table->dropColumn([
                'type',
                'category', 
                'excerpt',
                'is_published',
                'order',
                'meta'
            ]);
        });
    }
};
