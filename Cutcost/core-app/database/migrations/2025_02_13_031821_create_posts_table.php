<?php

use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->json('images')->nullable();
            $table->string('slug');
            $table->string('video')->nullable();
            $table->integer('rating')->default(0);
            $table->text('content');
            $table->timestamps();

            $table->foreignIdFor(User::class)
                ->constrained()->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('posts');
    }
};
