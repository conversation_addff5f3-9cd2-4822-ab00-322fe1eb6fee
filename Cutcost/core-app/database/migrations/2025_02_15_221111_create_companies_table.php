<?php

use App\Modules\Partner\Models\Company;
use App\Shared\Models\Country;
use App\Shared\Models\Currency;
use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->json('socials')->nullable();
            $table->json('description');
            $table->string('avatar')->nullable();
            $table->integer('rating')->default(0);
            $table->boolean('is_global')->default(false);

            $table->foreignIdFor(Country::class);
            $table->foreignIdFor(Currency::class, 'currency_id');
            $table->foreignIdFor(User::class, 'user_id')
                ->constrained()->onDelete('cascade');

            $table->timestamps();
        });

        // subscribers
        Schema::create('company_user', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Company::class)->constrained()->onDelete('cascade');
            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_user');
        Schema::dropIfExists('companies');
    }
};
