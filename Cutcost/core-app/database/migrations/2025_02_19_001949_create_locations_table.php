<?php

use App\Modules\Partner\Models\Company;
use App\Shared\Models\City;
use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('slug')->unique();
            $table->json('name');
            $table->json('description')->nullable();
            $table->string('address')->nullable();
            $table->geography('lnglat', 'point')->nullable();
            $table->json('working_hours');
            $table->integer('rating')->default(0);
            $table->boolean('is_open')->default(false);

            $table->enum('type', ['online', 'offline']);

            $table->foreignIdFor(Company::class, 'company_id')->constrained()
                ->onDelete('cascade');
            $table->foreignIdFor(User::class, 'user_id')
                ->constrained()
                ->onDelete('cascade');
            $table->foreignIdFor(City::class, 'city_id')
                ->constrained()
                ->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
