<?php

use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partner_histories', function (Blueprint $table) {
            $table->id();
            $table->morphs('historable');
            $table->json('entry_before');
            $table->json('entry_after');
            $table->string('comment')->nullable();
            $table->boolean('reported')->default(false);
            $table->foreignIdFor(User::class, 'owner_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partner_histories');
    }
};
