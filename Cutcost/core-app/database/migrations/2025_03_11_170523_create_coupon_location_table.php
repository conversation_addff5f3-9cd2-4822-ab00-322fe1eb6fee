<?php

use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Location;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_location', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Location::class, 'location_id')->constrained()->onDelete('cascade');
            $table->foreignIdFor(Coupon::class, 'coupon_id')->constrained()->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_location');
    }
};
