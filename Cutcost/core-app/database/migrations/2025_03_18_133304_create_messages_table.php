<?php

use App\Modules\Messenger\Models\Chat;
use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->text('content')->nullable();
            $table->string('replied_content')->nullable();
            $table->string('type')->default('text');
            // sent, delivered
            $table->enum('status', ['unread', 'seen'])->default('unread');
            $table->boolean('edited')->default(false);
            $table->foreignIdFor(Chat::class);
            $table->foreignIdFor(User::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
