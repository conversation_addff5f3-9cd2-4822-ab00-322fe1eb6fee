<?php

use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('chats', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(User::class, 'created_by')->index();
            $table->foreignIdFor(User::class, 'blocked_by')->nullable()->index();
            $table->string('name')->nullable();
            $table->string('avatar')->nullable();
            $table->boolean('is_public')->default(false);
            $table->enum('type', ['chat', 'group', 'notes'])->default('chat');
            $table->foreignIdFor(Company::class)->nullable()->index();
            $table->timestamps();
        });

        Schema::create('chat_user', function (Blueprint $table) {
            $table->uuid('chat_id');
            $table->foreignIdFor(User::class);
            $table->timestamps();
            $table->timestamp('left_at')->nullable();

            $table->primary(['chat_id', 'user_id']);
        });
    }


    public function down(): void
    {
        Schema::dropIfExists('chats');
        Schema::dropIfExists('chat_user');
    }
};
