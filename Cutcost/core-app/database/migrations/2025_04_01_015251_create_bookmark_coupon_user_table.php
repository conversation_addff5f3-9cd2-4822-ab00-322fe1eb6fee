<?php

use App\Modules\Partner\Models\Coupon;
use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bookmark_coupon_user', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class);
            $table->foreignIdFor(Coupon::class);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bookmark_coupon_user');
    }
};
