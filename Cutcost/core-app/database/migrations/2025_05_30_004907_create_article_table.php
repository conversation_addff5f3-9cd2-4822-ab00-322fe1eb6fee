<?php

use App\Modules\Partner\Models\Company;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->string('slug')->unique();
            $table->string('name');
            $table->unsignedBigInteger('views')->default(0);
            $table->text('content');
            $table->string('type')->default('article')->index(); // 'article', 'help', 'faq'
            $table->string('category')->nullable()->index(); // Help category
            $table->text('excerpt')->nullable(); // Short description
            $table->boolean('is_published')->default(true)->index();
            $table->integer('order')->default(0)->index(); // Display order
            $table->json('meta')->nullable(); // Additional metadata

            $table->timestamps();

            $table->index(['type', 'is_published', 'order']);
            $table->index(['type', 'category', 'is_published']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
