<?php

use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Industry;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('industries')) {


            Schema::create('industries', function (Blueprint $table) {
                $table->id();
                $table->json('name');
                $table->string('slug')->unique();
                $table->string('icon')->nullable();
                $table->json('description')->nullable();
                $table->timestamps();
            });
        }
        if (!Schema::hasTable('company_industry')) {

            Schema::create('company_industry', function (Blueprint $table) {
                $table->id();
                $table->foreignIdFor(Company::class)->constrained()->onDelete('cascade');
                $table->foreignIdFor(Industry::class)->constrained()->onDelete('cascade');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_industry');
        Schema::dropIfExists('industries');
    }
};
