<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropColumn('sales_builder');
            $table->string('base_locale')->nullable()->after('description');
            $table->dateTime('valid_until')->nullable();
            $table->integer('users_scan_limits')->nullable();
            $table->json('custom_conditions')->nullable();

            $table->boolean('first_time_only')->default(false)->after('users_scan_limits');
            $table->decimal('min_order_amount', 10, 2)->nullable()->after('first_time_only'); 
            $table->boolean('one_use_per_cart')->default(false)->after('min_order_amount'); 
            $table->integer('min_age')->nullable()->after('one_use_per_cart'); 
            $table->integer('max_age')->nullable()->after('min_age');
        });
    }
    public function down(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropColumn('base_locale', 'valid_until', 'users_scan_limits', 'custom_conditions');
            $table->json('sales_builder')->nullable();
        });
    }
};
