<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            $table->renameColumn('working_hours', 'business_hours');
            $table->json('business_hours')->nullable()->change();
            $table->text('name')->nullable()->change();
            $table->text('description')->nullable()->change();
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->text('description')->nullable()->change();
        });

        Schema::table('coupons', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
            $table->text('description')->nullable()->change();
            $table->text('custom_conditions')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
