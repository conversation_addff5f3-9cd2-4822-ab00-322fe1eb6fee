<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            $table->string('base_locale')->nullable()->after('description');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->string('base_locale')->nullable()->after('description');
        });
    }

    public function down(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            $table->dropColumn('base_locale');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('base_locale');
        });
    }
};
