<?php

use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('likes', function (Blueprint $table) {
            $table->id();
            $table->morphs('likeable'); // likeable_id, likeable_type
            $table->tinyInteger('value'); // 1 = like, -1 = dislike

            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            $table->unique(['user_id', 'likeable_id', 'likeable_type'], 'likes_user_likeable_unique');

        });
    }

    public function down(): void
    {
        Schema::dropIfExists('likes');
    }
};
