<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropColumn('out_of_stock');
            $table->enum('status', ['draft', 'published', 'out_of_stock'])
                ->default('published')->change();
        });
    }

    public function down(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->enum('status', ['draft', 'published'])->default('published')->change();
            $table->boolean('out_of_stock')->default(false)->after('description');
        });
    }
};
