<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('is_public_profile');

            $table->enum('visibility', [
                'everyone',
                'friends',
                'nobody',
            ])->default('everyone');
        });
    }

    public function down(): void {}
};
