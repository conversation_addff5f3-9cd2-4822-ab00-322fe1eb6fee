<?php

use App\Shared\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('bookmark_coupon_user');

        Schema::create('bookmarks', function (Blueprint $table) {
            $table->id();
            $table->morphs('bookmarkable');

            
            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            $table->unique(['user_id', 'bookmarkable_id', 'bookmarkable_type'], 'likes_user_likeable_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookmarks');
    }
};
