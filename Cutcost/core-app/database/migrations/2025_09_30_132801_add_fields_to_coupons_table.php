<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->string('benefits')->nullable();
            $table->dropColumn('users_scan_limits');
            $table->dropColumn('one_use_per_cart');
        });
    }

    public function down(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->integer('users_scan_limits')->nullable();
            $table->boolean('one_use_per_cart')->nullable();
            $table->dropColumn('benefits');
        });
    }
};
