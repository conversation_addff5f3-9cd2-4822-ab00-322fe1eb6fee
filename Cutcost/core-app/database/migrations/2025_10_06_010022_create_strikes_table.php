<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('strikes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');        // the user who receives the strike
            $table->unsignedBigInteger('actor_id')->nullable(); // user/admin who issued the strike
            $table->string('type')->nullable();           // e.g. 'spam', 'abuse', 'copyright'
            $table->text('reason')->nullable();
            $table->json('metadata')->nullable();         // store context (post_id, ip, etc.)
            $table->timestamp('expires_at')->nullable();  // optional expiration for temporary strikes
            $table->timestamp('revoked_at')->nullable();
            $table->unsignedBigInteger('revoked_by')->nullable();
            $table->timestamps();

            // indexes & FK
            $table->index(['user_id']);
            $table->index(['actor_id']);
            $table->index(['type']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('actor_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('revoked_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('strikes');
    }
};
