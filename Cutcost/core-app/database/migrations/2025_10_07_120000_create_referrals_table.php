<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referrals', function (Blueprint $table) {
            $table->id();
            
            // The user who was referred
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // The user who made the referral
            $table->foreignId('referrer_id')->constrained('users')->onDelete('cascade');
            
            // The referral code used
            $table->string('referral_code', 20)->index();
            
            // Registration details
            $table->timestamp('registered_at');
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            
            // Verification status
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            
            // First action tracking
            $table->boolean('first_coupon_used')->default(false);
            $table->timestamp('first_coupon_used_at')->nullable();
            
            // Reward tracking
            $table->boolean('registration_reward_given')->default(false);
            $table->boolean('verification_reward_given')->default(false);
            $table->boolean('first_action_reward_given')->default(false);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['referrer_id', 'created_at']);
            $table->index(['user_id', 'referrer_id']);
            $table->unique(['user_id', 'referrer_id']); // Prevent duplicate referrals
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referrals');
    }
};
