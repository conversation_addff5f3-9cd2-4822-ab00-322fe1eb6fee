<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cuts_transactions', function (Blueprint $table) {
            $table->id();
            
            // The user receiving the cuts
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // The user who triggered this reward (the referred user)
            $table->foreignId('source_user_id')->constrained('users')->onDelete('cascade');
            
            // Transaction details
            $table->enum('type', ['registration', 'verification', 'first_coupon', 'coupon_scan', 'purchase']);
            $table->unsignedInteger('amount'); // Amount of cuts earned
            $table->unsignedTinyInteger('level'); // MLM level (1, 2, or 3)
            
            // Reference to the referral relationship
            $table->foreignId('referral_id')->nullable()->constrained()->onDelete('set null');
            
            // Additional metadata
            $table->json('metadata')->nullable(); // Store additional context
            $table->string('description')->nullable(); // Human-readable description
            
            // Status tracking
            $table->enum('status', ['pending', 'completed', 'failed', 'reversed'])->default('completed');
            $table->timestamp('processed_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['user_id', 'created_at']);
            $table->index(['source_user_id', 'type']);
            $table->index(['type', 'level']);
            $table->index(['status', 'processed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cuts_transactions');
    }
};
