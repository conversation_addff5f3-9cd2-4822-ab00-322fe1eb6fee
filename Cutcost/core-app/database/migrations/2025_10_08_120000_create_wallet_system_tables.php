<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Shared\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Wallet transactions table
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            
            // Transaction details
            $table->enum('type', ['deposit', 'withdrawal', 'purchase', 'refund', 'bonus', 'referral']);
            $table->decimal('amount', 10, 2); // Amount in EUR
            $table->unsignedInteger('cuts_amount')->nullable(); // Amount in cuts
            $table->decimal('exchange_rate', 8, 4)->nullable(); // EUR to cuts rate
            
            // Status and processing
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled']);
            $table->string('payment_method')->nullable(); // stripe, paypal, bank_transfer
            $table->string('payment_id')->nullable(); // External payment ID
            $table->string('reference')->nullable(); // Internal reference
            
            // Metadata
            $table->json('metadata')->nullable();
            $table->text('description')->nullable();
            $table->timestamp('processed_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['user_id', 'type']);
            $table->index(['status', 'created_at']);
        });

        // Payment methods table
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            
            $table->string('provider'); // stripe, paypal
            $table->string('provider_id'); // External ID from provider
            $table->string('type'); // card, bank_account, paypal
            $table->string('last_four')->nullable();
            $table->string('brand')->nullable(); // visa, mastercard, etc.
            $table->string('country')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'provider_id']);
        });

        // Subscription plans table (enhanced)
        Schema::create('cuts_plans', function (Blueprint $table) {
            $table->id();
            
            $table->string('code')->unique(); // starter, premium, enterprise
            $table->json('name'); // Multilingual names
            $table->json('description'); // Multilingual descriptions
            $table->json('features'); // Plan features array
            
            // Pricing
            $table->decimal('price', 8, 2); // Price in EUR
            $table->unsignedInteger('cuts_amount'); // Cuts included
            $table->unsignedInteger('bonus_cuts')->default(0); // Bonus cuts
            $table->enum('interval', ['one_time', 'monthly', 'yearly']);
            
            // Verification plan
            $table->boolean('is_verification_plan')->default(false);
            $table->decimal('verification_price', 8, 2)->nullable(); // 2 EUR for verification
            
            // Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            
            $table->timestamps();
        });

        // User subscriptions table (enhanced)
        Schema::create('cuts_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            $table->foreignId('plan_id')->constrained('cuts_plans')->onDelete('cascade');
            
            // Stripe integration
            $table->string('stripe_subscription_id')->nullable();
            $table->string('stripe_customer_id')->nullable();
            $table->string('stripe_price_id')->nullable();
            
            // Subscription details
            $table->enum('status', ['active', 'cancelled', 'past_due', 'unpaid', 'incomplete']);
            $table->decimal('amount', 8, 2); // Amount paid
            $table->unsignedInteger('cuts_received'); // Cuts received
            
            // Dates
            $table->timestamp('starts_at');
            $table->timestamp('ends_at')->nullable();
            $table->timestamp('renews_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            
            // Metadata
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['stripe_subscription_id']);
        });

        // Payment verifications table
        Schema::create('payment_verifications', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->onDelete('cascade');
            
            $table->decimal('amount', 8, 2); // 2.00 EUR
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded']);
            $table->string('stripe_payment_intent_id')->nullable();
            $table->string('payment_method_id')->nullable();
            
            $table->timestamp('verified_at')->nullable();
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
        });

        // Add payment verification status to users table
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_payment_verified')->default(false)->after('email_verified_at');
            $table->timestamp('payment_verified_at')->nullable()->after('is_payment_verified');
            $table->decimal('wallet_balance', 10, 2)->default(0)->after('cuts'); // EUR balance
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['is_payment_verified', 'payment_verified_at', 'wallet_balance']);
        });
        
        Schema::dropIfExists('payment_verifications');
        Schema::dropIfExists('cuts_subscriptions');
        Schema::dropIfExists('cuts_plans');
        Schema::dropIfExists('payment_methods');
        Schema::dropIfExists('wallet_transactions');
    }
};
