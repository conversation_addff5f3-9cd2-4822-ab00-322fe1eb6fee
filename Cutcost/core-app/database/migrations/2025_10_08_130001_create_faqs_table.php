<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('faqs', function (Blueprint $table) {
            $table->id();
            $table->string('question');
            $table->text('answer');
            $table->string('category', 100)->index();
            $table->integer('order')->default(0)->index();
            $table->boolean('is_published')->default(true)->index();
            $table->integer('views')->default(0);
            $table->json('meta')->nullable(); // For additional metadata like tags, related articles, etc.
            $table->timestamps();

            // Indexes for better performance
            $table->index(['category', 'is_published', 'order']);
            $table->index(['is_published', 'created_at']);
            $table->fullText(['question', 'answer']); // For search functionality
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faqs');
    }
};
