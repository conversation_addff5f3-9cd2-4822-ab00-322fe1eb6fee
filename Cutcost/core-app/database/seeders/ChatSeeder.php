<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Models\Message;
use App\Shared\Models\User;
use App\Modules\Partner\Models\Company;

class ChatSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::take(3)->get(); // например 3 юзера

        if ($users->count() < 2) {
            $this->command->info('Not enough users to create chats.');
            return;
        }

        // Пример 1: чат между юзерами
        $chat1 = Chat::create([
            'type' => 'chat',
            'name' => '<PERSON> & Jane Chat',
            'is_public' => false,
            'created_by' => $users[0]->id,
        ]);

        $chat1->users()->attach([$users[0]->id, $users[1]->id]);

        Message::create([
            'chat_id' => $chat1->id,
            'user_id' => $users[0]->id,
            'content' => 'Привет, как дела?',
        ]);

        Message::create([
            'chat_id' => $chat1->id,
            'user_id' => $users[1]->id,
            'content' => 'Всё отлично, спасибо!',
        ]);

        // Пример 2: групповой чат с компанией
        $company = Company::first(); // привязываем первую компанию
        if ($company) {
            $chat2 = Chat::create([
                'type' => 'group',
                'name' => $company->name . ' Team Chat',
                'company_id' => $company->id,
                'is_public' => true,
                'created_by' => $users[0]->id,
            ]);

            // Все 3 юзера в чате
            $chat2->users()->attach($users->pluck('id')->toArray());

            Message::create([
                'chat_id' => $chat2->id,
                'user_id' => $users[1]->id,
                'content' => 'Добро пожаловать в командный чат компании!',
            ]);

            Message::create([
                'chat_id' => $chat2->id,
                'user_id' => $users[2]->id,
                'content' => 'Спасибо! Рад присоединиться.',
            ]);
        }

        $this->command->info('Chats seeded successfully.');
    }
}
