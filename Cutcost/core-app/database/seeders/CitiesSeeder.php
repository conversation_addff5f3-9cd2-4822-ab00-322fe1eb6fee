<?php

namespace Database\Seeders;

use App\Shared\Models\City;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CitiesSeeder extends Seeder
{
    public function run()
    {
        $filePath = base_path('resources/cities.csv');

        if (!file_exists($filePath) || !is_readable($filePath)) {
            $this->command->error("CSV file not found or not readable at path: $filePath");
            return;
        }

        if (($handle = fopen($filePath, 'r')) !== false) {
            $header = fgetcsv($handle, 0, ';');
            $header[0] = preg_replace('/^\x{FEFF}/u', '', $header[0]);

            $map = [
                'Name' => 'name',
                'Alternate Names' => 'alternate_names',
                'Timezone' => 'timezone',
                'Country Code' => 'country_code',
                'Admin1 Code' => 'admin1_code',
                'Coordinates' => 'coordinates',
            ];

            $normalizedHeader = array_map('trim', $header);
            $normalizedHeader = array_map(function ($h) use ($map) {
                return $map[$h] ?? strtolower(str_replace(' ', '_', $h));
            }, $normalizedHeader);

            $batchSize = 1000;
            $chunk = [];
            $total = 0;

            while (($row = fgetcsv($handle, 0, ';')) !== false) {
                if (count($row) !== count($normalizedHeader)) {
                    continue;
                }

                $data = array_combine($normalizedHeader, $row);

                $coords = isset($data['coordinates']) ? explode(',', $data['coordinates']) : [null, null];
                $latitude = isset($coords[0]) ? trim($coords[0]) : null;
                $longitude = isset($coords[1]) ? trim($coords[1]) : null;

                $chunk[] = [
                    'name' => $data['name'],
                    'alternate_names' => $data['alternate_names'] ?? null,
                    'timezone' => $data['timezone'] ?? null,
                    'country_code' => $data['country_code'],
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                ];

                if (count($chunk) >= $batchSize) {
                    DB::table('cities')->insert($chunk);
                    $total += count($chunk);
                    $chunk = [];
                }
            }

            if (!empty($chunk)) {
                DB::table('cities')->insert($chunk);
                $total += count($chunk);
            }

            fclose($handle);
            $this->command->info("Imported $total cities successfully.");
        } else {
            $this->command->error("Failed to open the CSV file.");
        }
    }
}
