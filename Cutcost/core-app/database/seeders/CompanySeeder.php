<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Partner\Models\Industry;
use App\Modules\Partner\Models\Company;
use App\Shared\Models\User;
use App\Modules\Partner\Enums\CompanyTypesEnum;

class CompanySeeder extends Seeder
{
    public function run(): void
    {
        $countryId = 1;
        $currencyId = 1;

        // Основной менеджер
        $manager = User::where('email', '<EMAIL>')->first();

        if (!$manager) {
            $this->command->info('Manager not found, skipping company seeding.');
            return;
        }

        // Основные компании
        $companies = [
            [
                'name' => 'Cutcost Official',
                'slug' => 'cutcost-official',
                'description' => 'Официальная компания Cutcost',
                'type' => CompanyTypesEnum::ORGANIZATION,
                'industry_slugs' => ['technology', 'retail'],
            ],
            [
                'name' => 'Baltic Software Group',
                'slug' => 'baltic-software-group',
                'description' => 'Компания по разработке программного обеспечения',
                'type' => CompanyTypesEnum::ORGANIZATION,
                'industry_slugs' => ['technology', 'professional-services'],
            ],
            [
                'name' => 'Vilnius Wellness Spa',
                'slug' => 'vilnius-wellness-spa',
                'description' => 'Спа и велнес центр',
                'type' => CompanyTypesEnum::ORGANIZATION,
                'industry_slugs' => ['beauty-wellness', 'hospitality'],
            ],
            [
                'name' => 'Green Farm Co.',
                'slug' => 'green-farm-co',
                'description' => 'Производство и продажа органической продукции',
                'type' => CompanyTypesEnum::ORGANIZATION,
                'industry_slugs' => ['agriculture', 'retail'],
            ],
            [
                'name' => 'Pet Paradise',
                'slug' => 'pet-paradise',
                'description' => 'Магазин товаров и услуг для животных',
                'type' => CompanyTypesEnum::INDIVIDUAL,
                'industry_slugs' => ['retail'],
            ],
            [
                'name' => 'EcoBuild Construction',
                'slug' => 'ecobuild-construction',
                'description' => 'Строительство жилых и коммерческих объектов',
                'type' => CompanyTypesEnum::ORGANIZATION,
                'industry_slugs' => ['construction', 'real-estate'],
            ],
            [
                'name' => 'Quick Logistics',
                'slug' => 'quick-logistics',
                'description' => 'Служба экспресс-доставки и грузоперевозок',
                'type' => CompanyTypesEnum::ORGANIZATION,
                'industry_slugs' => ['transport-logistics'],
            ],
        ];

        foreach ($companies as $data) {
            /** @var Company $company */
            $company = $manager->companies()->create([
                'name' => $data['name'],
                'slug' => $data['slug'],
                'user_id' => $manager->id,
                'description' => $data['description'],
                'country_id' => $countryId,
                'currency_id' => $currencyId,
                'type' => $data['type'],
                'avatar' => 'user_files/company-avatar-placeholder.jpg',
                'socials' => [],
            ]);

            // Привязка индустрий по slug
            $industries = Industry::whereIn('name', $data['industry_slugs'])->pluck('id')->toArray();
            if (!empty($industries)) {
                $company->industries()->sync($industries);
            }
        }

        $this->command->info('Companies seeded successfully.');
    }
}
