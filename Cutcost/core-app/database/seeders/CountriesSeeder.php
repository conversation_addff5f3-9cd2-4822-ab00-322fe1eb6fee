<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountriesSeeder extends Seeder
{
    public function run(): void
    {
        $allCountries = collect([
            ['name' => 'Worldwide / Worldwide', 'code' => 'WW'],
            ['name' => 'Afghanistan / افغانستان', 'code' => 'AF'],
            ['name' => 'Albania / Shqipëri', 'code' => 'AL'],
            ['name' => 'Algeria / الجزائر', 'code' => 'DZ'],
            ['name' => 'Andorra / Andorra', 'code' => 'AD'],
            ['name' => 'Angola / Angola', 'code' => 'AO'],
            ['name' => 'Argentina / Argentina', 'code' => 'AR'],
            ['name' => 'Armenia / Հայաստան', 'code' => 'AM'],
            ['name' => 'Australia / Australia', 'code' => 'AU'],
            ['name' => 'Austria / Österreich', 'code' => 'AT'],
            ['name' => 'Azerbaijan / Азәрбајҹан', 'code' => 'AZ'],
            ['name' => 'Bahamas / Bahamas', 'code' => 'BS'],
            ['name' => 'Bahrain / البحرين', 'code' => 'BH'],
            ['name' => 'Bangladesh / বাংলাদেশ', 'code' => 'BD'],
            ['name' => 'Barbados / Barbados', 'code' => 'BB'],
            ['name' => 'Belarus / Беларусь', 'code' => 'BY'],
            ['name' => 'Belgium / België', 'code' => 'BE'],
            ['name' => 'Belize / Belize', 'code' => 'BZ'],
            ['name' => 'Benin / Bénin', 'code' => 'BJ'],
            ['name' => 'Bhutan / Bhutan', 'code' => 'BT'],
            ['name' => 'Bolivia / Bolivia', 'code' => 'BO'],
            ['name' => 'Bosnia and Herzegovina / Bosna i Hercegovina', 'code' => 'BA'],
            ['name' => 'Botswana / Botswana', 'code' => 'BW'],
            ['name' => 'Brazil / Brasil', 'code' => 'BR'],
            ['name' => 'Brunei / Brunei Darussalam', 'code' => 'BN'],
            ['name' => 'Bulgaria / България', 'code' => 'BG'],
            ['name' => 'Burkina Faso / Burkina Faso', 'code' => 'BF'],
            ['name' => 'Burundi / Burundi', 'code' => 'BI'],
            ['name' => 'Cambodia / កម្ពុជា', 'code' => 'KH'],
            ['name' => 'Cameroon / Cameroun', 'code' => 'CM'],
            ['name' => 'Canada / Canada', 'code' => 'CA'],
            ['name' => 'Cape Verde / Cabo Verde', 'code' => 'CV'],
            ['name' => 'Central African Republic / République centrafricaine', 'code' => 'CF'],
            ['name' => 'Chad / Tchad', 'code' => 'TD'],
            ['name' => 'Chile / Chile', 'code' => 'CL'],
            ['name' => 'China / 中国', 'code' => 'CN'],
            ['name' => 'Colombia / Colombia', 'code' => 'CO'],
            ['name' => 'Comoros / Comores', 'code' => 'KM'],
            ['name' => 'Congo (Brazzaville) / Congo-Brazzaville', 'code' => 'CG'],
            ['name' => 'Congo (Kinshasa) / Congo-Kinshasa', 'code' => 'CD'],
            ['name' => 'Costa Rica / Costa Rica', 'code' => 'CR'],
            ['name' => 'Croatia / Hrvatska', 'code' => 'HR'],
            ['name' => 'Cuba / Cuba', 'code' => 'CU'],
            ['name' => 'Cyprus / Κύπρος', 'code' => 'CY'],
            ['name' => 'Czech Republic / Česko', 'code' => 'CZ'],
            ['name' => 'Denmark / Danmark', 'code' => 'DK'],
            ['name' => 'Djibouti / Djibouti', 'code' => 'DJ'],
            ['name' => 'Dominica / Dominica', 'code' => 'DM'],
            ['name' => 'Dominican Republic / República Dominicana', 'code' => 'DO'],
            ['name' => 'Ecuador / Ecuador', 'code' => 'EC'],
            ['name' => 'Egypt / مصر', 'code' => 'EG'],
            ['name' => 'El Salvador / El Salvador', 'code' => 'SV'],
            ['name' => 'Equatorial Guinea / Guinea Ecuatorial', 'code' => 'GQ'],
            ['name' => 'Eritrea / ኤርትራ', 'code' => 'ER'],
            ['name' => 'Estonia / Eesti', 'code' => 'EE'],
            ['name' => 'Eswatini / Eswatini', 'code' => 'SZ'],
            ['name' => 'Ethiopia / ኢትዮጵያ', 'code' => 'ET'],
            ['name' => 'Fiji / Fiji', 'code' => 'FJ'],
            ['name' => 'Finland / Suomi', 'code' => 'FI'],
            ['name' => 'France / France', 'code' => 'FR'],
            ['name' => 'Gabon / Gabon', 'code' => 'GA'],
            ['name' => 'Gambia / Gambia', 'code' => 'GM'],
            ['name' => 'Georgia / საქართველო', 'code' => 'GE'],
            ['name' => 'Germany / Deutschland', 'code' => 'DE'],
            ['name' => 'Ghana / Ghana', 'code' => 'GH'],
            ['name' => 'Greece / Ελλάδα', 'code' => 'GR'],
            ['name' => 'Grenada / Grenada', 'code' => 'GD'],
            ['name' => 'Guatemala / Guatemala', 'code' => 'GT'],
            ['name' => 'Guinea / Guinée', 'code' => 'GN'],
            ['name' => 'Guinea-Bissau / Guiné-Bissau', 'code' => 'GW'],
            ['name' => 'Guyana / Guyana', 'code' => 'GY'],
            ['name' => 'Haiti / Haïti', 'code' => 'HT'],
            ['name' => 'Honduras / Honduras', 'code' => 'HN'],
            ['name' => 'Hungary / Magyarország', 'code' => 'HU'],
            ['name' => 'Iceland / Ísland', 'code' => 'IS'],
            ['name' => 'India / भारत', 'code' => 'IN'],
            ['name' => 'Indonesia / Indonesia', 'code' => 'ID'],
            ['name' => 'Iran / ایران', 'code' => 'IR'],
            ['name' => 'Iraq / العراق', 'code' => 'IQ'],
            ['name' => 'Ireland / Éire', 'code' => 'IE'],
            ['name' => 'Israel / ישראל', 'code' => 'IL'],
            ['name' => 'Italy / Italia', 'code' => 'IT'],
            ['name' => 'Jamaica / Jamaica', 'code' => 'JM'],
            ['name' => 'Japan / 日本', 'code' => 'JP'],
            ['name' => 'Jordan / الأردن', 'code' => 'JO'],
            ['name' => 'Kazakhstan / Қазақстан', 'code' => 'KZ'],
            ['name' => 'Kenya / Kenya', 'code' => 'KE'],
            ['name' => 'Kiribati / Kiribati', 'code' => 'KI'],
            ['name' => 'Kuwait / الكويت', 'code' => 'KW'],
            ['name' => 'Kyrgyzstan / Кыргызстан', 'code' => 'KG'],
            ['name' => 'Laos / ລາວ', 'code' => 'LA'],
            ['name' => 'Latvia / Latvija', 'code' => 'LV'],
            ['name' => 'Lebanon / لبنان', 'code' => 'LB'],
            ['name' => 'Lesotho / Lesotho', 'code' => 'LS'],
            ['name' => 'Liberia / Liberia', 'code' => 'LR'],
            ['name' => 'Libya / ليبيا', 'code' => 'LY'],
            ['name' => 'Liechtenstein / Liechtenstein', 'code' => 'LI'],
            ['name' => 'Lithuania / Lietuva', 'code' => 'LT'],
            ['name' => 'Luxembourg / Lëtzebuerg', 'code' => 'LU'],
            ['name' => 'Madagascar / Madagasikara', 'code' => 'MG'],
            ['name' => 'Malawi / Malawi', 'code' => 'MW'],
            ['name' => 'Malaysia / Malaysia', 'code' => 'MY'],
            ['name' => 'Maldives / ދިވެހި ރާއްޖެ', 'code' => 'MV'],
            ['name' => 'Mali / Mali', 'code' => 'ML'],
            ['name' => 'Malta / Malta', 'code' => 'MT'],
            ['name' => 'Marshall Islands / Marshall Islands', 'code' => 'MH'],
            ['name' => 'Mauritania / موريتانيا', 'code' => 'MR'],
            ['name' => 'Mauritius / Mauritius', 'code' => 'MU'],
            ['name' => 'Mexico / México', 'code' => 'MX'],
            ['name' => 'Micronesia / Micronesia', 'code' => 'FM'],
            ['name' => 'Moldova / Moldova', 'code' => 'MD'],
            ['name' => 'Monaco / Monaco', 'code' => 'MC'],
            ['name' => 'Mongolia / Монгол улс', 'code' => 'MN'],
            ['name' => 'Montenegro / Crna Gora', 'code' => 'ME'],
            ['name' => 'Morocco / المغرب', 'code' => 'MA'],
            ['name' => 'Mozambique / Moçambique', 'code' => 'MZ'],
            ['name' => 'Myanmar / မြန်မာ', 'code' => 'MM'],
            ['name' => 'Namibia / Namibia', 'code' => 'NA'],
            ['name' => 'Nauru / Nauru', 'code' => 'NR'],
            ['name' => 'Nepal / नेपाल', 'code' => 'NP'],
            ['name' => 'Netherlands / Nederland', 'code' => 'NL'],
            ['name' => 'New Zealand / New Zealand', 'code' => 'NZ'],
            ['name' => 'Nicaragua / Nicaragua', 'code' => 'NI'],
            ['name' => 'Niger / Niger', 'code' => 'NE'],
            ['name' => 'Nigeria / Nigeria', 'code' => 'NG'],
            ['name' => 'North Macedonia / Северна Македонија', 'code' => 'MK'],
            ['name' => 'Norway / Norge', 'code' => 'NO'],
            ['name' => 'Oman / عمان', 'code' => 'OM'],
            ['name' => 'Pakistan / پاکستان', 'code' => 'PK'],
            ['name' => 'Palau / Palau', 'code' => 'PW'],
            ['name' => 'Panama / Panamá', 'code' => 'PA'],
            ['name' => 'Papua New Guinea / Papua New Guinea', 'code' => 'PG'],
            ['name' => 'Paraguay / Paraguay', 'code' => 'PY'],
            ['name' => 'Peru / Perú', 'code' => 'PE'],
            ['name' => 'Philippines / Pilipinas', 'code' => 'PH'],
            ['name' => 'Poland / Polska', 'code' => 'PL'],
            ['name' => 'Portugal / Portugal', 'code' => 'PT'],
            ['name' => 'Qatar / قطر', 'code' => 'QA'],
            ['name' => 'Romania / România', 'code' => 'RO'],
            ['name' => 'Russia / Россия', 'code' => 'RU'],
            ['name' => 'Rwanda / Rwanda', 'code' => 'RW'],
            ['name' => 'Saint Kitts and Nevis / Saint Kitts and Nevis', 'code' => 'KN'],
            ['name' => 'Saint Lucia / Saint Lucia', 'code' => 'LC'],
            ['name' => 'Saint Vincent and the Grenadines / Saint Vincent and the Grenadines', 'code' => 'VC'],
            ['name' => 'Samoa / Samoa', 'code' => 'WS'],
            ['name' => 'San Marino / San Marino', 'code' => 'SM'],
            ['name' => 'Sao Tome and Principe / São Tomé e Príncipe', 'code' => 'ST'],
            ['name' => 'Saudi Arabia / السعودية', 'code' => 'SA'],
            ['name' => 'Senegal / Sénégal', 'code' => 'SN'],
            ['name' => 'Serbia / Србија', 'code' => 'RS'],
            ['name' => 'Seychelles / Seychelles', 'code' => 'SC'],
            ['name' => 'Sierra Leone / Sierra Leone', 'code' => 'SL'],
            ['name' => 'Singapore / Singapore', 'code' => 'SG'],
            ['name' => 'Slovakia / Slovensko', 'code' => 'SK'],
            ['name' => 'Slovenia / Slovenija', 'code' => 'SI'],
            ['name' => 'Solomon Islands / Solomon Islands', 'code' => 'SB'],
            ['name' => 'Somalia / Soomaaliya', 'code' => 'SO'],
            ['name' => 'South Africa / South Africa', 'code' => 'ZA'],
            ['name' => 'South Korea / 대한민국', 'code' => 'KR'],
            ['name' => 'South Sudan / South Sudan', 'code' => 'SS'],
            ['name' => 'Spain / España', 'code' => 'ES'],
            ['name' => 'Sri Lanka / ශ්‍රී ලංකාව', 'code' => 'LK'],
            ['name' => 'Sudan / السودان', 'code' => 'SD'],
            ['name' => 'Suriname / Suriname', 'code' => 'SR'],
            ['name' => 'Sweden / Sverige', 'code' => 'SE'],
            ['name' => 'Switzerland / Schweiz', 'code' => 'CH'],
            ['name' => 'Syria / سوريا', 'code' => 'SY'],
            ['name' => 'Taiwan / 臺灣', 'code' => 'TW'],
            ['name' => 'Tajikistan / Тоҷикистон', 'code' => 'TJ'],
            ['name' => 'Tanzania / Tanzania', 'code' => 'TZ'],
            ['name' => 'Thailand / ประเทศไทย', 'code' => 'TH'],
            ['name' => 'Togo / Togo', 'code' => 'TG'],
            ['name' => 'Tonga / Tonga', 'code' => 'TO'],
            ['name' => 'Trinidad and Tobago / Trinidad and Tobago', 'code' => 'TT'],
            ['name' => 'Tunisia / تونس', 'code' => 'TN'],
            ['name' => 'Turkey / Türkiye', 'code' => 'TR'],
            ['name' => 'Turkmenistan / Türkmenistan', 'code' => 'TM'],
            ['name' => 'Tuvalu / Tuvalu', 'code' => 'TV'],
            ['name' => 'Uganda / Uganda', 'code' => 'UG'],
            ['name' => 'Ukraine / Україна', 'code' => 'UA'],
            ['name' => 'United Arab Emirates / الإمارات', 'code' => 'AE'],
            ['name' => 'United Kingdom / United Kingdom', 'code' => 'GB'],
            ['name' => 'United States / United States', 'code' => 'US'],
            ['name' => 'Uruguay / Uruguay', 'code' => 'UY'],
            ['name' => 'Uzbekistan / Oʻzbekiston', 'code' => 'UZ'],
            ['name' => 'Vanuatu / Vanuatu', 'code' => 'VU'],
            ['name' => 'Vatican City / Vatican City', 'code' => 'VA'],
            ['name' => 'Venezuela / Venezuela', 'code' => 'VE'],
            ['name' => 'Vietnam / Việt Nam', 'code' => 'VN'],
            ['name' => 'Yemen / اليمن', 'code' => 'YE'],
            ['name' => 'Zambia / Zambia', 'code' => 'ZM'],
            ['name' => 'Zimbabwe / Zimbabwe', 'code' => 'ZW'],

        ]);

        $excluded = ['BY', 'RU', 'CN', 'IR', 'IQ', 'AF', 'KP', 'SY', 'CU', 'VE', 'SA', 'ER', 'TM', 'SD', 'UZ', 'AE', 'ET'];

        $countries = $allCountries->reject(function ($country) use ($excluded) {
            return in_array($country['code'], $excluded);
        });

        foreach ($countries as $country) {
            // Берём только английское название до символа '/'
            $englishName = explode('/', $country['name'])[0];
            $englishName = trim($englishName);

            $name = $englishName;
            $code = $country['code'];

            // Обновляем или вставляем
            DB::table('countries')->updateOrInsert(
                ['code' => $code],
                ['name' => $name, 'code' => $code]
            );
        }
    }
}
