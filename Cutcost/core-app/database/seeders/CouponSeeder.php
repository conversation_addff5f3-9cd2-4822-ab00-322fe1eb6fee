<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Partner\Models\Coupon;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Location;
use App\Modules\Partner\Enums\CouponTypesEnum;
use Illuminate\Support\Str;

class CouponSeeder extends Seeder
{
    public function run(): void
    {
        $companies = Company::with('locations')->get();

        if ($companies->isEmpty()) {
            $this->command->info('No companies found, skipping coupon seeding.');
            return;
        }

        foreach ($companies as $company) {
            $onlineCoupon = Coupon::create([
                'name' => $company->name . ' Online Discount',
                'slug' => Str::slug($company->name . '-online'),
                'description' => 'Get 10% off on online orders at ' . $company->name,
                'discount' => 10,
                'price_in_store' => null,
                'tokens_price' => 100,
                'tokens_reward' => 10,
                'base_locale' => 'en',
                'promocode' => strtoupper(Str::random(8)),
                'link_to_product' => 'https://example.com/product',
                'link_clicks' => 0,
                'price_in_store' => 100,
                'type' => CouponTypesEnum::ONLINE,
                'company_id' => $company->id,
                'user_id' => $company->user_id,
                'valid_until' => now()->addMonth(),
                'status' => 'published',
                'first_time_only' => false,
                'benefits' => 'goods, parking, free delivery, fast delivery',
            ]);

            $locationCoupon = Coupon::create([
                'name' => $company->name . ' In-Store Offer',
                'slug' => Str::slug($company->name . '-location'),
                'description' => 'Special in-store discount at ' . $company->name,
                'discount' => 15,
                'price_in_store' => 50,
                'tokens_price' => null,
                'tokens_reward' => null,
                'type' => CouponTypesEnum::LOCATION,
                'company_id' => $company->id,
                'user_id' => $company->user_id,
                'valid_until' => now()->addMonth(),
                'status' => 'published',
                'promocode' => strtoupper(Str::random(8)),
                'first_time_only' => true,
                'benefits' => 'goods, parking, free delivery, fast delivery',
            ]);

            if ($company->locations->isNotEmpty()) {
                $locationCoupon->locations()->attach($company->locations->pluck('id'));
            }
        }

        $this->command->info('Coupons seeded successfully for all companies.');
    }
}
