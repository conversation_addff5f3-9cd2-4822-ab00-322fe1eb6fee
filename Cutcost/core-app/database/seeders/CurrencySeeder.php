<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrencySeeder extends Seeder
{
    public function run(): void
    {
        $currencies = [
            // Европа
            ['code' => 'EUR', 'name' => 'Euro', 'symbol' => '€', 'symbol_native' => '€', 'decimals' => 2],
            ['code' => 'GBP', 'name' => 'British Pound Sterling', 'symbol' => '£', 'symbol_native' => '£', 'decimals' => 2],
            ['code' => 'CHF', 'name' => 'Swiss Franc', 'symbol' => 'CHF', 'symbol_native' => 'CHF', 'decimals' => 2],
            ['code' => 'SEK', 'name' => 'Swedish Krona', 'symbol' => 'kr', 'symbol_native' => 'kr', 'decimals' => 2],
            ['code' => 'NOK', 'name' => 'Norwegian Krone', 'symbol' => 'kr', 'symbol_native' => 'kr', 'decimals' => 2],
            ['code' => 'DKK', 'name' => 'Danish Krone', 'symbol' => 'kr', 'symbol_native' => 'kr', 'decimals' => 2],
            ['code' => 'PLN', 'name' => 'Polish Zloty', 'symbol' => 'zł', 'symbol_native' => 'zł', 'decimals' => 2],
            ['code' => 'HUF', 'name' => 'Hungarian Forint', 'symbol' => 'Ft', 'symbol_native' => 'Ft', 'decimals' => 2],

            // Северная Америка
            ['code' => 'USD', 'name' => 'United States Dollar', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 2],
            ['code' => 'CAD', 'name' => 'Canadian Dollar', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 2],
            ['code' => 'MXN', 'name' => 'Mexican Peso', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 2],

            // Южная Америка
            ['code' => 'BRL', 'name' => 'Brazilian Real', 'symbol' => 'R$', 'symbol_native' => 'R$', 'decimals' => 2],
            ['code' => 'ARS', 'name' => 'Argentine Peso', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 2],
            ['code' => 'CLP', 'name' => 'Chilean Peso', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 0],
            ['code' => 'COP', 'name' => 'Colombian Peso', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 2],
            ['code' => 'PEN', 'name' => 'Peruvian Sol', 'symbol' => 'S/.', 'symbol_native' => 'S/.', 'decimals' => 2],
            ['code' => 'UYU', 'name' => 'Uruguayan Peso', 'symbol' => '$U', 'symbol_native' => '$U', 'decimals' => 2],

            // Азия
            ['code' => 'JPY', 'name' => 'Japanese Yen', 'symbol' => '¥', 'symbol_native' => '¥', 'decimals' => 0],
            ['code' => 'INR', 'name' => 'Indian Rupee', 'symbol' => '₹', 'symbol_native' => '₹', 'decimals' => 2],
            ['code' => 'KRW', 'name' => 'South Korean Won', 'symbol' => '₩', 'symbol_native' => '₩', 'decimals' => 0],
            ['code' => 'SGD', 'name' => 'Singapore Dollar', 'symbol' => '$', 'symbol_native' => '$', 'decimals' => 2],
        ];


        foreach ($currencies as $currency) {
            DB::table('currencies')->updateOrInsert(
                ['code' => $currency['code']],
                $currency
            );
        }
    }
}
