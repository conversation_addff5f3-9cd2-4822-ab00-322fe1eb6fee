<?php

namespace Database\Seeders;

use App\Shared\Models\CutsPlan;
use Illuminate\Database\Seeder;

class CutsPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create verification plan
        CutsPlan::updateOrCreate(
            ['code' => 'verification'],
            [
                'name' => [
                    'en' => 'Account Verification',
                    'de' => 'Kontoverifizierung',
                    'fr' => 'Vérification du compte',
                ],
                'description' => [
                    'en' => 'Verify your account with a small payment to unlock all features',
                    'de' => 'Verifizieren Sie Ihr Konto mit einer kleinen Zahlung, um alle Funktionen freizuschalten',
                    'fr' => 'Vérifiez votre compte avec un petit paiement pour débloquer toutes les fonctionnalités',
                ],
                'features' => [
                    'Account verification',
                    'Full platform access',
                    'Referral system access',
                    'Premium support',
                ],
                'price' => 2.00,
                'cuts_amount' => 0,
                'bonus_cuts' => 0,
                'interval' => CutsPlan::INTERVAL_ONE_TIME,
                'is_verification_plan' => true,
                'verification_price' => 2.00,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 0,
            ]
        );

        // Create regular plans
        $plans = [
            [
                'code' => 'starter',
                'name' => [
                    'en' => 'Starter Pack',
                    'de' => 'Starter-Paket',
                    'fr' => 'Pack de démarrage',
                ],
                'description' => [
                    'en' => 'Perfect for getting started with cuts',
                    'de' => 'Perfekt für den Einstieg mit Cuts',
                    'fr' => 'Parfait pour commencer avec les cuts',
                ],
                'features' => [
                    '100 cuts included',
                    '10 bonus cuts',
                    'Basic support',
                    'Mobile app access',
                ],
                'price' => 9.99,
                'cuts_amount' => 100,
                'bonus_cuts' => 10,
                'interval' => CutsPlan::INTERVAL_ONE_TIME,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 1,
            ],
            [
                'code' => 'premium',
                'name' => [
                    'en' => 'Premium Pack',
                    'de' => 'Premium-Paket',
                    'fr' => 'Pack Premium',
                ],
                'description' => [
                    'en' => 'Best value for regular users',
                    'de' => 'Bestes Preis-Leistungs-Verhältnis für regelmäßige Nutzer',
                    'fr' => 'Meilleur rapport qualité-prix pour les utilisateurs réguliers',
                ],
                'features' => [
                    '500 cuts included',
                    '75 bonus cuts',
                    'Priority support',
                    'Mobile app access',
                    'Exclusive offers',
                ],
                'price' => 39.99,
                'cuts_amount' => 500,
                'bonus_cuts' => 75,
                'interval' => CutsPlan::INTERVAL_ONE_TIME,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'enterprise',
                'name' => [
                    'en' => 'Enterprise Pack',
                    'de' => 'Enterprise-Paket',
                    'fr' => 'Pack Entreprise',
                ],
                'description' => [
                    'en' => 'Maximum value for power users',
                    'de' => 'Maximaler Wert für Power-User',
                    'fr' => 'Valeur maximale pour les utilisateurs avancés',
                ],
                'features' => [
                    '1500 cuts included',
                    '300 bonus cuts',
                    'Premium support',
                    'Mobile app access',
                    'Exclusive offers',
                    'Early access to features',
                ],
                'price' => 99.99,
                'cuts_amount' => 1500,
                'bonus_cuts' => 300,
                'interval' => CutsPlan::INTERVAL_ONE_TIME,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 3,
            ],
            [
                'code' => 'monthly_basic',
                'name' => [
                    'en' => 'Monthly Basic',
                    'de' => 'Monatlich Basic',
                    'fr' => 'Mensuel Basic',
                ],
                'description' => [
                    'en' => 'Monthly subscription with regular cuts',
                    'de' => 'Monatliches Abonnement mit regelmäßigen Cuts',
                    'fr' => 'Abonnement mensuel avec des cuts réguliers',
                ],
                'features' => [
                    '200 cuts per month',
                    '20 bonus cuts',
                    'Priority support',
                    'Mobile app access',
                    'Cancel anytime',
                ],
                'price' => 19.99,
                'cuts_amount' => 200,
                'bonus_cuts' => 20,
                'interval' => CutsPlan::INTERVAL_MONTHLY,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 4,
            ],
            [
                'code' => 'monthly_premium',
                'name' => [
                    'en' => 'Monthly Premium',
                    'de' => 'Monatlich Premium',
                    'fr' => 'Mensuel Premium',
                ],
                'description' => [
                    'en' => 'Monthly subscription with premium benefits',
                    'de' => 'Monatliches Abonnement mit Premium-Vorteilen',
                    'fr' => 'Abonnement mensuel avec avantages premium',
                ],
                'features' => [
                    '500 cuts per month',
                    '75 bonus cuts',
                    'Premium support',
                    'Mobile app access',
                    'Exclusive offers',
                    'Cancel anytime',
                ],
                'price' => 39.99,
                'cuts_amount' => 500,
                'bonus_cuts' => 75,
                'interval' => CutsPlan::INTERVAL_MONTHLY,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 5,
            ],
            [
                'code' => 'yearly_premium',
                'name' => [
                    'en' => 'Yearly Premium',
                    'de' => 'Jährlich Premium',
                    'fr' => 'Annuel Premium',
                ],
                'description' => [
                    'en' => 'Best value yearly subscription with maximum savings',
                    'de' => 'Bestes Jahresabonnement mit maximalen Einsparungen',
                    'fr' => 'Meilleur abonnement annuel avec économies maximales',
                ],
                'features' => [
                    '6000 cuts per year',
                    '1200 bonus cuts',
                    'Premium support',
                    'Mobile app access',
                    'Exclusive offers',
                    'Early access to features',
                    '2 months free',
                ],
                'price' => 399.99,
                'cuts_amount' => 6000,
                'bonus_cuts' => 1200,
                'interval' => CutsPlan::INTERVAL_YEARLY,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($plans as $planData) {
            CutsPlan::updateOrCreate(
                ['code' => $planData['code']],
                $planData
            );
        }

        $this->command->info('Cuts plans seeded successfully!');
    }
}
