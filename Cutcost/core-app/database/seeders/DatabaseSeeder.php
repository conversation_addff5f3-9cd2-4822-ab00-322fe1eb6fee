<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(PlanSeeder::class);
        $this->call(CurrencySeeder::class);
        $this->call(RoleSeeder::class);
        $this->call(IndustrySeeder::class);
        $this->call(LocalesSeeder::class);

        $this->call(CountriesSeeder::class);
        $this->call(CitiesSeeder::class);
        $this->call(UserSeeder::class);

        //required user

        $this->call(PostsSeeder::class);
        $this->call(CompanySeeder::class);
        $this->call(LocationSeeder::class);
        $this->call(CouponSeeder::class);
        $this->call(ChatSeeder::class);
        $this->call(MessageSeeder::class);
        $this->call(ArticleSeeder::class);
    }
}
