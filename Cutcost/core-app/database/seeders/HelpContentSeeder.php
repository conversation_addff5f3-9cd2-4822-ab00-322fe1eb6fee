<?php

namespace Database\Seeders;

use App\Modules\Public\Models\Article;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class HelpContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedHelpArticles();
        $this->seedFaqs();
    }

    private function seedHelpArticles(): void
    {
        $helpArticles = [
            [
                'name' => 'Getting Started with CutCost',
                'slug' => 'getting-started-with-cutcost',
                'category' => 'Getting Started',
                'excerpt' => 'Learn the basics of using CutCost to find deals and save money.',
                'content' => $this->getGettingStartedContent(),
                'order' => 1,
            ],
            [
                'name' => 'How to Create an Account',
                'slug' => 'how-to-create-an-account',
                'category' => 'Getting Started',
                'excerpt' => 'Step-by-step guide to creating your CutCost account.',
                'content' => $this->getCreateAccountContent(),
                'order' => 2,
            ],
            [
                'name' => 'Finding and Using Coupons',
                'slug' => 'finding-and-using-coupons',
                'category' => 'Using Coupons',
                'excerpt' => 'Discover how to find the best deals and redeem coupons.',
                'content' => $this->getFindingCouponsContent(),
                'order' => 3,
            ],
            [
                'name' => 'Managing Your Wallet',
                'slug' => 'managing-your-wallet',
                'category' => 'Wallet & Payments',
                'excerpt' => 'Learn how to manage your cuts, payments, and transactions.',
                'content' => $this->getWalletManagementContent(),
                'order' => 4,
            ],
            [
                'name' => 'Account Verification Process',
                'slug' => 'account-verification-process',
                'category' => 'Account Management',
                'excerpt' => 'Understand why and how to verify your account.',
                'content' => $this->getVerificationContent(),
                'order' => 5,
            ],
            [
                'name' => 'Business Partnership Guide',
                'slug' => 'business-partnership-guide',
                'category' => 'For Businesses',
                'excerpt' => 'Learn how businesses can partner with CutCost.',
                'content' => $this->getBusinessGuideContent(),
                'order' => 6,
            ],
        ];

        foreach ($helpArticles as $article) {
            Article::create([
                'type' => Article::TYPE_HELP,
                'is_published' => true,
                'views' => rand(50, 500),
                'meta' => [
                    'featured' => in_array($article['slug'], ['getting-started-with-cutcost', 'finding-and-using-coupons']),
                    'tags' => $this->getTagsForCategory($article['category']),
                ],
                ...$article
            ]);
        }
    }

    private function seedFaqs(): void
    {
        $faqs = [
            [
                'name' => 'What is CutCost?',
                'slug' => 'what-is-cutcost',
                'category' => 'General',
                'excerpt' => 'CutCost is a platform that connects customers with local businesses through exclusive deals and coupons.',
                'content' => '<p>CutCost is a comprehensive savings platform that helps you discover exclusive deals, coupons, and promotions from local businesses in your area. Our mission is to help people save money while supporting their local community.</p>',
                'order' => 1,
            ],
            [
                'name' => 'How do I redeem a coupon?',
                'slug' => 'how-do-i-redeem-a-coupon',
                'category' => 'Coupons',
                'excerpt' => 'Show your coupon code or QR code to the business when making a purchase.',
                'content' => '<p>To redeem a coupon:</p><ol><li>Find the coupon you want to use</li><li>Click "Get Coupon" to add it to your collection</li><li>Show the coupon code or QR code to the cashier</li><li>Enjoy your discount!</li></ol>',
                'order' => 2,
            ],
            [
                'name' => 'What are cuts and how do I earn them?',
                'slug' => 'what-are-cuts-and-how-do-i-earn-them',
                'category' => 'Cuts & Rewards',
                'excerpt' => 'Cuts are our virtual currency that you can earn and spend on the platform.',
                'content' => '<p>Cuts are CutCost\'s virtual currency. You can earn cuts by:</p><ul><li>Referring friends to the platform</li><li>Completing your profile</li><li>Regular platform usage</li><li>Participating in promotions</li></ul><p>You can spend cuts to unlock premium coupons and features.</p>',
                'order' => 3,
            ],
            [
                'name' => 'Why do I need to verify my account?',
                'slug' => 'why-do-i-need-to-verify-my-account',
                'category' => 'Account',
                'excerpt' => 'Account verification helps us ensure security and unlock all platform features.',
                'content' => '<p>Account verification is required to:</p><ul><li>Access all premium features</li><li>Make secure transactions</li><li>Protect against fraud</li><li>Comply with financial regulations</li></ul><p>The verification process involves a small €2 payment to confirm your payment method.</p>',
                'order' => 4,
            ],
            [
                'name' => 'How can my business join CutCost?',
                'slug' => 'how-can-my-business-join-cutcost',
                'category' => 'Business',
                'excerpt' => 'Businesses can join CutCost to reach new customers and increase sales.',
                'content' => '<p>To join CutCost as a business:</p><ol><li>Create a business account</li><li>Complete your business profile</li><li>Submit verification documents</li><li>Create your first coupon or deal</li><li>Start attracting customers!</li></ol><p>Contact our business team for more information.</p>',
                'order' => 5,
            ],
            [
                'name' => 'Is CutCost free to use?',
                'slug' => 'is-cutcost-free-to-use',
                'category' => 'General',
                'excerpt' => 'Yes, CutCost is free to use with optional premium features.',
                'content' => '<p>CutCost is completely free to use! You can:</p><ul><li>Browse and redeem coupons</li><li>Create an account</li><li>Earn and spend cuts</li><li>Access most features</li></ul><p>Some premium features may require account verification or cuts to unlock.</p>',
                'order' => 6,
            ],
        ];

        foreach ($faqs as $faq) {
            Article::create([
                'type' => Article::TYPE_FAQ,
                'is_published' => true,
                'views' => rand(100, 1000),
                'meta' => [
                    'featured' => in_array($faq['slug'], ['what-is-cutcost', 'how-do-i-redeem-a-coupon']),
                    'tags' => $this->getTagsForCategory($faq['category']),
                ],
                ...$faq
            ]);
        }
    }

    private function getTagsForCategory(string $category): array
    {
        $tagMap = [
            'General' => ['basics', 'overview', 'introduction'],
            'Getting Started' => ['setup', 'beginner', 'tutorial'],
            'Using Coupons' => ['coupons', 'deals', 'savings'],
            'Wallet & Payments' => ['wallet', 'payments', 'cuts'],
            'Account Management' => ['account', 'profile', 'settings'],
            'For Businesses' => ['business', 'partnership', 'merchants'],
            'Coupons' => ['redeem', 'discount', 'offers'],
            'Cuts & Rewards' => ['cuts', 'rewards', 'earning'],
            'Account' => ['verification', 'security', 'profile'],
            'Business' => ['merchant', 'partnership', 'registration'],
        ];

        return $tagMap[$category] ?? ['general'];
    }

    private function getGettingStartedContent(): string
    {
        return '<h2>Welcome to CutCost!</h2>
        <p>CutCost is your gateway to amazing deals and savings from local businesses. Here\'s how to get started:</p>
        
        <h3>1. Create Your Account</h3>
        <p>Sign up with your email address or social media account to access all features.</p>
        
        <h3>2. Explore Deals</h3>
        <p>Browse through hundreds of coupons and deals from businesses in your area.</p>
        
        <h3>3. Start Saving</h3>
        <p>Redeem coupons at participating businesses and start saving money immediately.</p>
        
        <h3>4. Earn Cuts</h3>
        <p>Collect cuts (our virtual currency) by using the platform and referring friends.</p>
        
        <p>Ready to start saving? Let\'s explore what CutCost has to offer!</p>';
    }

    private function getCreateAccountContent(): string
    {
        return '<h2>Creating Your CutCost Account</h2>
        <p>Follow these simple steps to create your account:</p>
        
        <h3>Step 1: Visit the Sign-Up Page</h3>
        <p>Click the "Sign Up" button on our homepage or app.</p>
        
        <h3>Step 2: Choose Your Sign-Up Method</h3>
        <ul>
            <li>Email and password</li>
            <li>Google account</li>
            <li>Facebook account</li>
        </ul>
        
        <h3>Step 3: Complete Your Profile</h3>
        <p>Add your basic information to personalize your experience.</p>
        
        <h3>Step 4: Verify Your Email</h3>
        <p>Check your email and click the verification link we sent you.</p>
        
        <p>That\'s it! You\'re now ready to start discovering amazing deals.</p>';
    }

    private function getFindingCouponsContent(): string
    {
        return '<h2>Finding and Using Coupons</h2>
        <p>Discover the best deals and learn how to use them effectively:</p>
        
        <h3>Finding Coupons</h3>
        <ul>
            <li>Browse by category (Food, Shopping, Services, etc.)</li>
            <li>Use the search function to find specific businesses</li>
            <li>Check the "Featured Deals" section for the best offers</li>
            <li>Enable location services to find nearby deals</li>
        </ul>
        
        <h3>Using Coupons</h3>
        <ol>
            <li>Click "Get Coupon" to add it to your collection</li>
            <li>Visit the business location</li>
            <li>Show your coupon code or QR code to the cashier</li>
            <li>Enjoy your discount!</li>
        </ol>
        
        <h3>Tips for Maximum Savings</h3>
        <ul>
            <li>Check expiration dates before visiting</li>
            <li>Read the terms and conditions</li>
            <li>Combine deals when possible</li>
            <li>Follow your favorite businesses for exclusive offers</li>
        </ul>';
    }

    private function getWalletManagementContent(): string
    {
        return '<h2>Managing Your CutCost Wallet</h2>
        <p>Your wallet is the central hub for managing your cuts and transactions:</p>
        
        <h3>Understanding Your Wallet</h3>
        <ul>
            <li><strong>Cuts Balance:</strong> Your virtual currency for premium features</li>
            <li><strong>Transaction History:</strong> Track all your cuts activity</li>
            <li><strong>Payment Methods:</strong> Manage your payment options</li>
        </ul>
        
        <h3>Earning Cuts</h3>
        <ul>
            <li>Refer friends to CutCost</li>
            <li>Complete profile information</li>
            <li>Regular platform usage</li>
            <li>Participate in special promotions</li>
        </ul>
        
        <h3>Spending Cuts</h3>
        <ul>
            <li>Unlock premium coupons</li>
            <li>Access exclusive deals</li>
            <li>Purchase cuts packages</li>
        </ul>
        
        <h3>Account Verification</h3>
        <p>Verify your account with a small payment to unlock all wallet features and ensure secure transactions.</p>';
    }

    private function getVerificationContent(): string
    {
        return '<h2>Account Verification Process</h2>
        <p>Account verification is a simple process that unlocks all CutCost features:</p>
        
        <h3>Why Verify?</h3>
        <ul>
            <li>Access all premium features</li>
            <li>Secure payment processing</li>
            <li>Enhanced account protection</li>
            <li>Compliance with financial regulations</li>
        </ul>
        
        <h3>Verification Steps</h3>
        <ol>
            <li>Go to your Wallet section</li>
            <li>Click "Verify Account"</li>
            <li>Add a payment method</li>
            <li>Complete the €2 verification payment</li>
            <li>Your account is now verified!</li>
        </ol>
        
        <h3>What Happens After Verification?</h3>
        <ul>
            <li>Immediate access to all features</li>
            <li>Ability to purchase cuts</li>
            <li>Enhanced security features</li>
            <li>Priority customer support</li>
        </ul>
        
        <p>The verification process is secure and your payment information is protected.</p>';
    }

    private function getBusinessGuideContent(): string
    {
        return '<h2>Business Partnership Guide</h2>
        <p>Join thousands of businesses already using CutCost to attract new customers:</p>
        
        <h3>Benefits for Businesses</h3>
        <ul>
            <li>Reach new customers in your area</li>
            <li>Increase foot traffic and sales</li>
            <li>Build customer loyalty</li>
            <li>Track campaign performance</li>
        </ul>
        
        <h3>Getting Started</h3>
        <ol>
            <li>Create a business account</li>
            <li>Complete your business profile</li>
            <li>Upload business verification documents</li>
            <li>Create your first coupon or deal</li>
            <li>Start attracting customers!</li>
        </ol>
        
        <h3>Creating Effective Deals</h3>
        <ul>
            <li>Offer meaningful discounts (15-30%)</li>
            <li>Use clear, attractive descriptions</li>
            <li>Set reasonable terms and conditions</li>
            <li>Include high-quality images</li>
        </ul>
        
        <h3>Support</h3>
        <p>Our business support team is here to help you succeed. Contact us for personalized assistance with your campaigns.</p>';
    }
}
