<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Partner\Models\Industry;

class IndustrySeeder extends Seeder
{
    private $industries = [
        'agriculture',
        'mining',
        'energy',
        'manufacturing',
        'construction',
        'retail',
        'wholesale',
        'ecommerce',
        'food beverage',
        'hospitality',
        'transport logistics',
        'healthcare',
        'beauty wellness',
        'education',
        'professional services',
        'finance',
        'insurance',
        'real estate',
        'technology',
        'media',
        'entertainment',
        'sports recreation',
        'home services',
        'legal',
        'accounting',
        'nonprofit',
        'government',
        'utilities',
        'telecommunications',
        'other',
    ];


    public function run(): void
    {
        foreach ($this->industries as $index => $name) {
            Industry::updateOrCreate(['id' => $index + 1], [
                'name' => $name,
            ]);
        }
    }
}
