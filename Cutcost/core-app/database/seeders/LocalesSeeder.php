<?php

namespace Database\Seeders;

use App\Shared\Models\Locale;
use Illuminate\Database\Seeder;

class LocalesSeeder extends Seeder
{
    public function run(): void
    {
        $locales = [
            ['name' => 'English / English', 'code' => 'en'],
            ['name' => 'Mandarin Chinese / 中文', 'code' => 'zh'],
            ['name' => 'Hindi / हिन्दी', 'code' => 'hi'],
            ['name' => 'Spanish / Español', 'code' => 'es'],
            ['name' => 'French / Français', 'code' => 'fr'],
            ['name' => 'Arabic / العربية', 'code' => 'ar'],
            ['name' => 'Bengali / বাংলা', 'code' => 'bn'],
            ['name' => 'Russian / Русский', 'code' => 'ru'],
            ['name' => 'Portuguese / Português', 'code' => 'pt'],
            ['name' => 'Urdu / اُردُو', 'code' => 'ur'],
            ['name' => 'Indonesian / Bahasa Indonesia', 'code' => 'id'],
            ['name' => 'German / Deutsch', 'code' => 'de'],
            ['name' => 'Japanese / 日本語', 'code' => 'ja'],
            ['name' => 'Swahili / Kiswahili', 'code' => 'sw'],
            ['name' => 'Marathi / मराठी', 'code' => 'mr'],
            ['name' => 'Telugu / తెలుగు', 'code' => 'te'],
            ['name' => 'Turkish / Türkçe', 'code' => 'tr'],
            ['name' => 'Tamil / தமிழ்', 'code' => 'ta'],
            ['name' => 'Western Punjabi / پنجابی', 'code' => 'pnb'],
            ['name' => 'Wu Chinese / 吴语', 'code' => 'wuu'],
            ['name' => 'Korean / 한국어', 'code' => 'ko'],
            ['name' => 'Vietnamese / Tiếng Việt', 'code' => 'vi'],
            ['name' => 'Javanese / Basa Jawa', 'code' => 'jv'],
            ['name' => 'Italian / Italiano', 'code' => 'it'],
            ['name' => 'Egyptian Arabic / اللهجة المصرية', 'code' => 'arz'],
            ['name' => 'Gujarati / ગુજરાતી', 'code' => 'gu'],
            ['name' => 'Persian / فارسی', 'code' => 'fa'],
            ['name' => 'Bhojpuri / भोजपुरी', 'code' => 'bho'],
            ['name' => 'Southern Min / 闽南语', 'code' => 'nan'],
            ['name' => 'Hausa / Harshen Hausa', 'code' => 'ha'],
            ['name' => 'Thai / ไทย', 'code' => 'th'],
            ['name' => 'Kannada / ಕನ್ನಡ', 'code' => 'kn'],
            ['name' => 'Polish / Polski', 'code' => 'pl'],
            ['name' => 'Xiang Chinese / 湘语', 'code' => 'hsn'],
            ['name' => 'Malayalam / മലയാളം', 'code' => 'ml'],
            ['name' => 'Sundanese / Basa Sunda', 'code' => 'su'],
            ['name' => 'Hakka / 客家語', 'code' => 'hak'],
            ['name' => 'Odia / ଓଡ଼ିଆ', 'code' => 'or'],
            ['name' => 'Burmese / မြန်မာဘာသာ', 'code' => 'my'],
            ['name' => 'Ukrainian / Українська', 'code' => 'uk'],
            ['name' => 'Yoruba / Yorùbá', 'code' => 'yo'],
            ['name' => 'Amharic / አማርኛ', 'code' => 'am'],
            ['name' => 'Maithili / मैथिली', 'code' => 'mai'],
            ['name' => 'Sindhi / سنڌي', 'code' => 'sd'],
            ['name' => 'Fula / Fulfulde', 'code' => 'ff'],
            ['name' => 'Romanian / Română', 'code' => 'ro'],
            ['name' => 'Oromo / Afaan Oromoo', 'code' => 'om'],
            ['name' => 'Igbo / Asụsụ Igbo', 'code' => 'ig'],
            ['name' => 'Dutch / Nederlands', 'code' => 'nl'],
            ['name' => 'Tagalog / Tagalog', 'code' => 'tl'],
            ['name' => 'Malay / Bahasa Melayu', 'code' => 'ms'],
            ['name' => 'Saraiki / سرائیکی', 'code' => 'skr'],
            ['name' => 'Nepali / नेपाली', 'code' => 'ne'],
            ['name' => 'Sinhalese / සිංහල', 'code' => 'si'],
            ['name' => 'Chittagonian / চাটগাঁইয়া', 'code' => 'ctg'],
            ['name' => 'Zhuang / 壮语', 'code' => 'za'],
            ['name' => 'Khmer / ខ្មែរ', 'code' => 'km'],
            ['name' => 'Assamese / অসমীয়া', 'code' => 'as'],
            ['name' => 'Madurese / Basa Madhura', 'code' => 'mad'],
            ['name' => 'Somali / Af-Soomaali', 'code' => 'so'],
            ['name' => 'Marwari / मारवाड़ी', 'code' => 'mwr'],
            ['name' => 'Magahi / मगही', 'code' => 'mag'],
            ['name' => 'Haryanvi / हरियाणवी', 'code' => 'bgc'],
            ['name' => 'Hungarian / Magyar', 'code' => 'hu'],
            ['name' => 'Kurdish / Kurdî', 'code' => 'ku'],
            ['name' => 'Zulu / isiZulu', 'code' => 'zu'],
            ['name' => 'Hebrew / עברית', 'code' => 'he'],
            ['name' => 'Greek / Ελληνικά', 'code' => 'el'],
            ['name' => 'Czech / Čeština', 'code' => 'cs'],
            ['name' => 'Swedish / Svenska', 'code' => 'sv'],
            ['name' => 'Belarusian / Беларуская', 'code' => 'be'],
            ['name' => 'Akan / Akan', 'code' => 'ak'],
            ['name' => 'Shona / chiShona', 'code' => 'sn'],
            ['name' => 'Kazakh / Қазақ тілі', 'code' => 'kk'],
            ['name' => 'Tatar / Татар теле', 'code' => 'tt'],
            ['name' => 'Uyghur / ئۇيغۇرچە', 'code' => 'ug'],
            ['name' => 'Finnish / Suomi', 'code' => 'fi'],
            ['name' => 'Norwegian / Norsk', 'code' => 'no'],
            ['name' => 'Slovak / Slovenčina', 'code' => 'sk'],
            ['name' => 'Danish / Dansk', 'code' => 'da'],
            ['name' => 'Bulgarian / Български', 'code' => 'bg'],
            ['name' => 'Serbian / Српски', 'code' => 'sr'],
            ['name' => 'Croatian / Hrvatski', 'code' => 'hr'],
            ['name' => 'Lithuanian / Lietuvių', 'code' => 'lt'],
            ['name' => 'Latvian / Latviešu', 'code' => 'lv'],
            ['name' => 'Estonian / Eesti', 'code' => 'et'],
            ['name' => 'Slovenian / Slovenščina', 'code' => 'sl'],
            ['name' => 'Georgian / ქართული', 'code' => 'ka'],
            ['name' => 'Armenian / Հայերեն', 'code' => 'hy'],
            ['name' => 'Albanian / Shqip', 'code' => 'sq'],
            ['name' => 'Macedonian / Македонски', 'code' => 'mk'],
            ['name' => 'Pashto / پښتو', 'code' => 'ps'],
            ['name' => 'Lao / ລາວ', 'code' => 'lo'],
            ['name' => 'Mongolian / Монгол хэл', 'code' => 'mn'],
            ['name' => 'Basque / Euskara', 'code' => 'eu'],
            ['name' => 'Galician / Galego', 'code' => 'gl'],
            ['name' => 'Irish / Gaeilge', 'code' => 'ga'],
            ['name' => 'Welsh / Cymraeg', 'code' => 'cy'],
            ['name' => 'Old Prussian / Prūsiskan', 'code' => 'prg'],
        ];

        foreach ($locales as $locale) {
            $englishName = explode('/', $locale['name'])[0];
            $englishName = trim($englishName);

            $englishName = ucwords(strtolower($englishName));

            Locale::updateOrCreate(
                ['code' => $locale['code']],
                [
                    'name' => $englishName,
                    'code' => $locale['code']
                ]
            );
        }
    }
}
