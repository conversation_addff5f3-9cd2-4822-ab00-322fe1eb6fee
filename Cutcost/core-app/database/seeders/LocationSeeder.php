<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Partner\Models\Company;
use App\Modules\Partner\Models\Location;
use App\Modules\Partner\Enums\LocationTypesEnum;
use App\Shared\Models\City;
use Illuminate\Support\Facades\DB;

class LocationSeeder extends Seeder
{
    public function run(): void
    {
        $city = City::first();
        if (!$city) {
            $this->command->info('No cities found, skipping location seeding.');
            return;
        }

        $companies = Company::whereIn('slug', [
            'cutcost-official',
            'baltic-software-group',
            'vilnius-wellness-spa',
            'green-farm-co',
            'pet-paradise',
            'ecobuild-construction',
            'quick-logistics',
        ])->get();

        $defaultBusinessHours = [
            'enabled' => true,
            'timezone' => date_default_timezone_get(),
            'days' => [
                ['name' => 'Monday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
                ['name' => 'Tuesday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
                ['name' => 'Wednesday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
                ['name' => 'Thursday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
                ['name' => 'Friday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
                ['name' => 'Saturday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
                ['name' => 'Sunday', 'diapasons' => [['start' => '09:00', 'end' => '18:00']], 'workDay' => true],
            ],
            'holidays' => [],
        ];

        
        foreach ($companies as $company) {
            $rand = rand(1, 1000000 - 1);
            Location::create([
                'is_open' => true,
                'base_locale' => 'en',
                'type' => LocationTypesEnum::OFFLINE,
                'name' => $company->name . ' Main Location',
                'slug' => $company->slug . '-location',
                'description' => 'Основная локация компании ' . $company->name,
                'address' => 'Main Street 1, Example City',
                'lnglat' => DB::raw("ST_GeomFromText('POINT(25.$rand 54.$rand)', 4326)"),
                'city_id' => $city->id,
                'user_id' => $company->user_id,
                'company_id' => $company->id,
                'business_hours' => $defaultBusinessHours,
            ]);
        }

        $this->command->info('Locations seeded successfully.');
    }
}
