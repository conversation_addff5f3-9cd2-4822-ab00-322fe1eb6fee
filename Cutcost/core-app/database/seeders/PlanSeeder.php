<?php

namespace Database\Seeders;

use App\Modules\Partner\Models\Billing\Plan;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    public function run(): void
    {
        $plans = [
            [
                'code' => 'basic',
                'name' => ['en' => 'Basic'],
                'description' => ['en' => 'Basic plan description'],
                'features' => ['en' => ['Feature A', 'Feature B']],
                'price' => 0,
                'interval' => 'month',
                'is_active' => true,
            ],
            [
                'code' => 'pro',
                'name' => ['en' => 'Pro'],
                'description' => ['en' => 'Pro plan description'],
                'features' => ['en' => ['Feature A', 'Feature B', 'Feature C']],
                'price' => 990,
                'interval' => 'month',
                'is_active' => true,
            ],
            [
                'code' => 'business',
                'name' => ['en' => 'Business'],
                'description' => ['en' => 'Business plan description'],
                'features' => ['en' => ['Feature A', 'Feature B', 'Feature C', 'Feature D']],
                'price' => 2990,
                'interval' => 'month',
                'is_active' => true,
            ],
        ];

        foreach ($plans as $planData) {
            Plan::updateOrCreate(
                ['code' => $planData['code']],
                $planData
            );
        }
    }
}
