<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Shared\Models\User;
use App\Modules\Seller\Models\Friend;
use App\Modules\Partner\Models\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Админ и саппорт
        $manager = User::factory()->create([
            'nickname' => config('cutcost.admins.manager.nickname'),
            'display_name' => config('cutcost.admins.manager.display_name'),
            'referral_code' => 'manager',
            'email' => config('cutcost.admins.manager.email'),
            'password' => Hash::make(config('cutcost.admins.manager.password')),
        ])->assignRole(['admin', 'partner']);

        DB::table('admins')->insert([
            'email' => config('cutcost.admins.manager.email'),
            'password' => Hash::make(config('cutcost.admins.manager.password')),
        ]);

        $support = User::factory()->create([
            'nickname' => config('cutcost.admins.support.nickname'),
            'display_name' => config('cutcost.admins.support.display_name'),
            'referral_code' => 'support',
            'email' => config('cutcost.admins.support.email'),
            'password' => Hash::make(config('cutcost.admins.support.password')),
        ])->assignRole(['admin', 'partner']);

        // Моковые юзеры
        $user1 = User::factory()->create([
            'nickname' => 'john_doe',
            'display_name' => 'John Doe',
            'referral_code' => 'john',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $user2 = User::factory()->create([
            'nickname' => 'jane_smith',
            'display_name' => 'Jane Smith',
            'referral_code' => 'jane',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Дружба между юзерами
        Friend::create([
            'user_id' => $user1->id,
            'friend_id' => $user2->id,
            'status' => Friend::STATUS_ACCEPTED,
        ]);

        Friend::create([
            'user_id' => $user2->id,
            'friend_id' => $user1->id,
            'status' => Friend::STATUS_ACCEPTED,
        ]);

        // Подписки на компании (моковые)
        $companies = Company::take(3)->get(); // первые 3 компании
        foreach ($companies as $company) {
            $user1->companiesSubscriptions()->attach($company->id);
            $user2->companiesSubscriptions()->attach($company->id);
        }

        $this->command->info('Users, friendships, and subscriptions seeded successfully.');
    }
}
