# Partner Analytics System Documentation

## Overview

The Partner Analytics System provides comprehensive business intelligence for partners in the CutCost platform. Unlike traditional coupon scanning systems, CutCost operates through a **chat-based interaction model** where all coupon interactions happen through messaging between users and partners.

## System Architecture

### Controller: `StatisticController`

**Location**: `app/Modules/Partner/Http/Controllers/StatisticController.php`

**Route**: `/business/statistic` (GET)

**Key Features**:
- Multi-company analytics support
- Flexible date range filtering (7, 30, 90, 365 days)
- Chat engagement metrics
- Coupon performance tracking
- Time-based trend analysis
- Top performer identification
- Recent activity monitoring

### Vue Component: `ShowStatistic.vue`

**Location**: `resources/js/Client/Modules/Partner/Pages/Statistic/ShowStatistic.vue`

**Features**:
- Interactive filtering (company, date range)
- Real-time data visualization
- Responsive design with dark mode support
- Export functionality placeholder
- Empty state handling

## Data Models and Relationships

### Core Models Used

1. **Chat** (`App\Modules\Messenger\Models\Chat`)
   - Tracks conversations between users and companies
   - Types: 'chat', 'group', 'notes'
   - Links users to companies through conversations

2. **Message** (`App\Modules\Messenger\Models\Message`)
   - Individual messages within chats
   - Tracks engagement and response patterns
   - Types: 'text', 'audio', 'notification', 'model', 'report'

3. **Scan** (`App\Modules\Partner\Models\Scan`)
   - **Important**: Despite the name "Scan", this represents chat-based coupon interactions
   - Tracks coupon redemptions through conversations
   - Records earnings from each interaction

4. **Coupon** (`App\Modules\Partner\Models\Coupon`)
   - Partner's coupon offerings
   - Tracks views, clicks, and performance metrics
   - Status: 'draft', 'published', 'out_of_stock'

5. **Company** (`App\Modules\Partner\Models\Company`)
   - Partner's business entities
   - Links to chats, coupons, and scans

## Analytics Metrics

### Overview Statistics

1. **Total Chats**: Number of conversations initiated with companies
2. **Active Conversations**: Chats with messages in the selected period
3. **Total Messages**: Messages exchanged in company chats
4. **Coupon Interactions**: Coupon redemptions through chat (from Scan model)
5. **Total Earnings**: Revenue from coupon interactions
6. **New Customers**: Unique users who started chats
7. **Conversion Rate**: Percentage of chats that led to coupon interactions

### Chat Engagement Metrics

1. **Average Messages per Chat**: Engagement depth indicator
2. **User Engagement Rate**: Users who sent multiple messages
3. **Peak Activity Hours**: Hourly message distribution (0-23)
4. **Average Response Time**: Placeholder for future implementation

### Coupon Performance

1. **Total Coupons**: All coupons created by companies
2. **Active Coupons**: Published coupons currently available
3. **Coupon Views**: Total views across all coupons
4. **Coupon Interactions**: Redemptions through chat conversations
5. **Top Performing Coupons**: Ranked by interaction count
6. **Conversion Rate**: Views to interactions ratio

### Time-Based Analytics

Charts showing trends over time for:
- Chats started per period
- Messages sent per period
- Coupon interactions per period
- Earnings per period

**Time Grouping**:
- 7/30 days: Daily grouping
- 90 days: Weekly grouping
- 365 days: Monthly grouping

### Top Performers

1. **Top Companies**: Ranked by interaction count
2. **Most Active Users**: Ranked by message count
3. **Peak Activity Days**: Days with highest message volume

## Key Implementation Details

### Chat-Based System Understanding

**Important**: The CutCost platform does NOT use traditional QR code scanning. Instead:

- Users discover coupons through the platform
- They initiate chats with companies to inquire about offers
- Coupon redemption happens through chat conversations
- The "Scan" model tracks these chat-based interactions
- Partners engage customers through messaging, not physical scanning

### Database Queries

The system uses optimized queries with:
- Proper indexing on date ranges
- Efficient joins between related models
- Aggregation functions for statistics
- Subqueries for complex metrics

### Performance Considerations

- Date range filtering to limit data scope
- Efficient use of `withCount()` for relationship counting
- Selective field loading with `select()` clauses
- Proper eager loading to avoid N+1 queries

## Frontend Features

### Interactive Elements

1. **Company Filter**: Filter analytics by specific company or view all
2. **Date Range Selector**: 7, 30, 90, or 365-day periods
3. **Export Button**: Placeholder for data export functionality
4. **Responsive Charts**: Simple bar charts showing trends

### Visual Design

- **Statistics Cards**: Key metrics with icons and colors
- **Performance Cards**: Detailed breakdowns with progress indicators
- **Chart Visualizations**: Simple CSS-based charts
- **Activity Feed**: Recent interactions with timestamps
- **Top Performer Lists**: Ranked lists with visual indicators

### User Experience

- **Loading States**: Proper handling of data loading
- **Empty States**: Helpful messages when no data exists
- **Error Handling**: Graceful degradation for missing data
- **Responsive Design**: Works on mobile and desktop
- **Dark Mode**: Full theme support

## API Endpoints

### Main Analytics Endpoint

```
GET /business/statistic
```

**Query Parameters**:
- `range`: Date range (7, 30, 90, 365)
- `company_id`: Specific company filter

**Response Structure**:
```json
{
  "companies": [...],
  "selected_company_id": null,
  "date_range": "30",
  "overview_stats": {
    "total_chats": 150,
    "active_conversations": 120,
    "total_messages": 1250,
    "coupon_interactions": 45,
    "total_earnings": 2250.50,
    "new_customers": 85,
    "conversion_rate": 30.0
  },
  "chat_metrics": {
    "avg_response_time": "~5 min",
    "messages_per_chat": 8.3,
    "active_chat_hours": [...],
    "user_engagement_rate": 65.5
  },
  "coupon_stats": {
    "total_coupons": 25,
    "active_coupons": 20,
    "coupon_views": 5500,
    "coupon_interactions": 45,
    "top_coupons": [...],
    "coupon_conversion_rate": 0.82
  },
  "time_analytics": {
    "chats_over_time": {...},
    "messages_over_time": {...},
    "interactions_over_time": {...},
    "earnings_over_time": {...}
  },
  "top_performers": {
    "top_companies": [...],
    "most_active_users": [...],
    "peak_days": [...]
  },
  "recent_activity": [...]
}
```

## Future Enhancements

### Planned Features

1. **Real-time Analytics**: WebSocket integration for live updates
2. **Advanced Charts**: Integration with Chart.js or similar library
3. **Export Functionality**: CSV/PDF export of analytics data
4. **Comparative Analysis**: Period-over-period comparisons
5. **Goal Setting**: Target setting and progress tracking
6. **Automated Reports**: Scheduled email reports
7. **Advanced Filtering**: More granular filtering options
8. **Predictive Analytics**: Trend forecasting

### Technical Improvements

1. **Caching**: Redis caching for frequently accessed data
2. **Background Jobs**: Async processing for heavy calculations
3. **Data Warehousing**: Separate analytics database
4. **API Rate Limiting**: Protect against excessive requests
5. **Real Response Time**: Actual chat response time calculation

## Testing

### Key Test Scenarios

1. **Data Accuracy**: Verify all metrics calculate correctly
2. **Date Filtering**: Test different date ranges
3. **Company Filtering**: Test single vs. all company views
4. **Empty States**: Test behavior with no data
5. **Performance**: Test with large datasets
6. **Responsive Design**: Test on various screen sizes

### Sample Test Data

The system works with existing seeders for:
- Chat conversations
- Messages between users and companies
- Coupon interactions (Scan records)
- Company and coupon data

## Deployment Notes

### Requirements

- Laravel 10+ with Inertia.js
- Vue 3 with Composition API
- MySQL/PostgreSQL for complex queries
- Proper database indexing on date fields

### Configuration

- Ensure all models have proper relationships
- Verify database migrations are applied
- Check that chat system is properly configured
- Confirm company-user relationships are established

The Partner Analytics System provides comprehensive insights into chat-based customer engagement and coupon performance, enabling partners to optimize their customer interaction strategies and improve business outcomes.
