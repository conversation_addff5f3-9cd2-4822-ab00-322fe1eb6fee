# CutCost Wallet System Documentation

## Overview

The CutCost Wallet System is a comprehensive payment and currency management solution that handles:
- **Cuts Currency**: Internal platform currency for transactions
- **EUR Wallet**: Real money balance for purchases
- **Stripe Integration**: Secure payment processing
- **Subscription Management**: Recurring and one-time plans
- **Payment Verification**: €2 verification for account security

## System Architecture

### Database Schema

#### Core Tables

1. **`wallet_transactions`** - All financial transactions
   - Tracks deposits, withdrawals, purchases, refunds, bonuses, referrals
   - Links to Stripe payment IDs
   - Stores both EUR amounts and cuts amounts

2. **`payment_methods`** - User payment methods
   - Stripe payment method integration
   - Card details (last 4 digits, brand, country)
   - Default payment method management

3. **`cuts_plans`** - Available subscription/purchase plans
   - Multilingual names and descriptions
   - One-time and recurring plans
   - Verification plans (€2 payment)

4. **`cuts_subscriptions`** - User subscriptions
   - Stripe subscription integration
   - Status tracking and renewal dates
   - Automatic cuts distribution

5. **`payment_verifications`** - Account verification payments
   - €2 verification requirement
   - Links to Stripe payment intents
   - Verification status tracking

#### Enhanced User Table

Added fields to `users` table:
- `cuts` - User's cuts balance (renamed from cutstars)
- `wallet_balance` - EUR balance
- `is_payment_verified` - Verification status
- `payment_verified_at` - Verification timestamp
- `stripe_customer_id` - Stripe customer reference

### Models

#### Core Models

1. **`WalletTransaction`** - Financial transaction records
2. **`PaymentMethod`** - User payment methods
3. **`CutsPlan`** - Available plans and pricing
4. **`CutsSubscription`** - User subscriptions
5. **`PaymentVerification`** - Account verification records

#### Enhanced User Model

Added wallet-related methods:
- `addToWallet()` / `deductFromWallet()` - EUR balance management
- `addCuts()` / `deductCuts()` - Cuts balance management
- `getDefaultPaymentMethod()` - Payment method retrieval
- `isPaymentVerified()` - Verification status
- `getActiveSubscription()` - Current subscription

### Services

#### StripeService

Handles all Stripe API interactions:
- Customer creation and management
- Payment intent creation
- Setup intent for payment methods
- Subscription management
- Webhook processing

#### WalletService

Business logic for wallet operations:
- Transaction processing
- Plan purchases
- Payment verification
- Subscription management
- Balance management

## Features

### 1. Payment Verification System

**Requirement**: €2 verification payment to unlock full platform features

**Process**:
1. User initiates verification
2. Stripe payment intent created for €2
3. Payment processed securely
4. User marked as verified
5. Full platform access granted

**Benefits**:
- Reduces fraud and spam accounts
- Ensures payment method validity
- Improves platform security

### 2. Cuts Currency System

**Cuts** are the internal currency used for:
- Coupon interactions
- Referral rewards
- Platform transactions
- Premium features

**Exchange Rates**:
- Flexible rates based on plan pricing
- Bonus cuts for larger purchases
- Referral bonus distributions

### 3. Subscription Plans

#### Plan Types

1. **Verification Plan** (€2.00)
   - Account verification
   - Full platform access
   - One-time payment

2. **One-Time Plans**
   - Starter Pack: €9.99 (100 + 10 bonus cuts)
   - Premium Pack: €39.99 (500 + 75 bonus cuts)
   - Enterprise Pack: €99.99 (1500 + 300 bonus cuts)

3. **Recurring Plans**
   - Monthly Basic: €19.99/month (200 + 20 bonus cuts)
   - Monthly Premium: €39.99/month (500 + 75 bonus cuts)
   - Yearly Premium: €399.99/year (6000 + 1200 bonus cuts)

#### Plan Features

- Multilingual support (EN, DE, FR)
- Flexible pricing and cuts allocation
- Bonus cuts for better value
- Featured plan highlighting
- Sort order management

### 4. Payment Methods

**Supported Providers**:
- Stripe (cards, bank accounts)
- PayPal integration ready

**Features**:
- Secure tokenization
- Default method selection
- Multiple payment methods per user
- Card brand and country detection

### 5. Transaction Management

**Transaction Types**:
- `deposit` - Money added to wallet
- `withdrawal` - Money removed from wallet
- `purchase` - Cuts or plan purchases
- `refund` - Payment refunds
- `bonus` - Free cuts (promotions, etc.)
- `referral` - Referral rewards

**Transaction Statuses**:
- `pending` - Awaiting processing
- `processing` - Currently being processed
- `completed` - Successfully completed
- `failed` - Processing failed
- `cancelled` - Cancelled by user/system

## API Endpoints

### Wallet Management

```
GET /wallet                          - Wallet dashboard
GET /wallet/plans                    - Available plans
GET /wallet/verification             - Verification page
GET /wallet/transactions             - Transaction history
```

### Payment Processing

```
POST /wallet/setup-intent            - Create setup intent
POST /wallet/payment-methods         - Add payment method
POST /wallet/verification/process    - Process verification
POST /wallet/plans/{plan}/purchase   - Purchase plan
```

### Subscriptions

```
DELETE /wallet/subscription          - Cancel subscription
```

### API Endpoints

```
GET /api/wallet/balance              - Get current balances
POST /api/wallet/spend-cuts          - Spend cuts (internal)
```

### Webhooks

```
POST /webhook/stripe                 - Stripe webhook handler
```

## Stripe Integration

### Configuration

Required environment variables:
```env
STRIPE_KEY=pk_test_...
STRIPE_SECRET=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Webhook Events

Handled events:
- `payment_intent.succeeded` - Payment completed
- `payment_intent.payment_failed` - Payment failed
- `customer.subscription.updated` - Subscription changed
- `customer.subscription.deleted` - Subscription cancelled

### Security

- Webhook signature verification
- Secure API key management
- PCI compliance through Stripe
- No sensitive card data stored

## Frontend Components

### Vue Components

1. **`Wallet/Dashboard.vue`** - Main wallet interface
2. **`Wallet/Plans.vue`** - Plan selection and purchase
3. **`Wallet/Verification.vue`** - Account verification
4. **`Wallet/Transactions.vue`** - Transaction history

### Features

- Responsive design
- Dark mode support
- Real-time balance updates
- Interactive payment forms
- Transaction filtering
- Subscription management

## Usage Examples

### Purchase Cuts Plan

```javascript
// Frontend
const purchasePlan = async (planId, paymentMethodId) => {
    const response = await axios.post(`/wallet/plans/${planId}/purchase`, {
        payment_method_id: paymentMethodId
    });
    
    if (response.data.success) {
        // Handle successful purchase
        if (response.data.type === 'subscription') {
            // Subscription created
        } else {
            // One-time payment
            // Handle 3D Secure if needed
        }
    }
};
```

### Spend Cuts

```php
// Backend
$result = $walletService->spendCuts(
    $user,
    50, // amount in cuts
    'Coupon interaction',
    ['coupon_id' => 123]
);

if ($result['success']) {
    // Cuts deducted successfully
    $newBalance = $result['new_balance'];
}
```

### Check Verification Status

```php
// Backend
if ($user->isPaymentVerified()) {
    // User has full access
} else {
    // Redirect to verification
    return redirect()->route('wallet.verification');
}
```

## Security Considerations

### Payment Security

- All payments processed through Stripe
- No card data stored locally
- PCI DSS compliance
- Webhook signature verification

### Account Security

- Payment verification requirement
- Fraud detection through verification
- Secure API endpoints
- Rate limiting on payment operations

### Data Protection

- Encrypted sensitive data
- GDPR compliance
- Audit trail for all transactions
- Secure webhook handling

## Testing

### Test Cards (Stripe)

```
**************** - Visa (succeeds)
**************** - Visa (declined)
**************** - Visa (3D Secure)
```

### Test Scenarios

1. **Account Verification**
   - Successful €2 payment
   - Failed payment handling
   - 3D Secure authentication

2. **Plan Purchases**
   - One-time plan purchase
   - Subscription creation
   - Payment failures

3. **Subscription Management**
   - Subscription updates
   - Cancellation handling
   - Renewal processing

4. **Webhook Processing**
   - Payment success events
   - Payment failure events
   - Subscription events

## Deployment

### Requirements

- Laravel 10+
- PHP 8.1+
- MySQL/PostgreSQL
- Redis (recommended for caching)
- Stripe account

### Setup Steps

1. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

2. **Run Migrations**
   ```bash
   php artisan migrate
   ```

3. **Seed Plans**
   ```bash
   php artisan db:seed --class=CutsPlansSeeder
   ```

4. **Configure Stripe**
   - Set environment variables
   - Configure webhook endpoints
   - Test payment flows

5. **Queue Configuration**
   ```bash
   php artisan queue:work
   ```

### Monitoring

- Transaction success/failure rates
- Webhook processing status
- Subscription renewal rates
- Payment method usage
- User verification rates

## Future Enhancements

### Planned Features

1. **Multi-Currency Support**
   - USD, GBP support
   - Dynamic exchange rates

2. **Advanced Analytics**
   - Revenue dashboards
   - User spending patterns
   - Plan performance metrics

3. **Payment Method Expansion**
   - Apple Pay / Google Pay
   - Bank transfers
   - Cryptocurrency support

4. **Loyalty Programs**
   - Spending rewards
   - Referral bonuses
   - VIP tiers

5. **Mobile App Integration**
   - Native payment flows
   - Push notifications
   - Offline transaction sync

The CutCost Wallet System provides a robust, secure, and scalable foundation for managing payments, subscriptions, and the internal cuts currency, with comprehensive Stripe integration and user-friendly interfaces.
