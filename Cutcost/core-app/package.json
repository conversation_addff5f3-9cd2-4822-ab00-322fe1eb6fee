{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "devDependencies": {"autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-echo": "^2.0.0", "laravel-vite-plugin": "^1.2.0", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "rollup-plugin-visualizer": "^6.0.3", "vite": "^6.0.11"}, "dependencies": {"@formkit/tempo": "^0.1.2", "@inertiajs/vue3": "^2.0.3", "@phosphor-icons/vue": "^2.2.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.4", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^13.4.0", "@vueuse/motion": "^3.0.3", "date-fns": "^4.1.0", "grapheme-splitter": "^1.0.4", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "nprogress": "^0.2.0", "pinia": "^3.0.1", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "tailwindcss": "^4.0.4", "vue": "^3.5.13", "vue-qrcode-reader": "^5.7.1", "vue-tippy": "^6.6.0", "vue3-emoji-picker": "^1.1.8", "vuedraggable": "^4.1.0"}}