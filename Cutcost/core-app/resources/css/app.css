@import "tailwindcss";
@plugin "@tailwindcss/typography";
@import "./progress.css";
@import "./map.css";
@source "../views";

@custom-variant dark (&:where(.dark, .dark *));

input::placeholder,
textarea::placeholder {
  @apply pl-[4px] text-slate-400;
}

.dark input::placeholder,
textarea::placeholder {
  @apply text-slate-500;
}

body {
  @apply bg-surface dark:bg-bg-dark overflow-x-hidden transition-colors duration-200;
}

@theme {

  --color-bg: #f1f1f1;          /* main app background (very light, almost icy) */
  --color-surface: #ffffff;     /* chat bubble / card surface */
  --color-border: #e6e6e6;      /* subtle divider lines */
  --color-text: #1c1c1e;        /* primary almost-black text */
  --color-muted: #6c6c6e;       /* secondary/muted text */

  --color-primary: #0088cc;     /* Telegram signature blue */
  --color-secondary: #2ea5e2;   /* lighter Telegram blue */
  --color-accent: #005ecb;      /* deeper, more futuristic blue accent */

  --color-success: oklch(79.2% 0.209 151.711); /* green for success */
  --color-error: #e33e3e;       /* red notifications / errors */
  --color-warning: #ffc93d;     /* yellow notifications / warnings */

  /* Dark Mode */
  --color-bg-dark: #1e1e20;        /* dark app background */
  --color-dark-surface: #2a2a2e;   /* dark chat bubble / card surface */
  --color-dark-border: #3b3b40;    /* divider lines in dark mode */
  --color-dark-text: #e1e1e6;      /* light primary text */
  --color-dark-muted: #8e8e93;     /* light muted text */

  --color-dark-primary: #009be1;   /* Telegram blue in dark mode */
  --color-dark-secondary: #3cb0f4; /* lighter accent blue */
  --color-dark-accent: #3c8cff;    /* bright blue accent for interactive elements */

  --color-dark-success:   oklch(79.2% 0.209 151.711);
  --color-dark-error: #ff5c5c;
  --color-dark-warning: #ffd966;
}

@layer base {
  input,
  textarea {
    @apply ring-0 outline-none focus:ring-0 focus:outline-none disabled:!opacity-70;
  }

  button {
    @apply dark:text-dark-text select-none focus:ring-0 focus:outline-none;
    -webkit-user-drag: none !important;
  }
}

body {
  scroll-behavior: smooth;
}

input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
  display: none;
}

input[type="search"]::-moz-search-cancel-button {
  display: none;
}

input[type="search"]::-ms-clear {
  display: none;
}


.border-muted {
  @apply border-border dark:border-dark-border;
}

.card-position {
  @apply mx-auto mt-4 sm:mt-2 sm:px-4;
}

.center-position {
  @apply mx-auto mt-6 mb-4 px-4 md:w-160;
}

.content-position {
  @apply max-sm:px-4;
}

.post-position {
  @apply mb-2 w-full min-[940px]:w-2/3;
}

.text-message {
  @apply break-before-column font-serif break-words;
}

.item-card {
  @apply hover:bg-primary dark:hover:bg-secondary/20 cursor-pointer rounded-lg;
}

/* text */
.format-accent {
  @apply !font-extrabold !tracking-wide !uppercase;
}

.text {
  @apply text-text dark:text-dark-text;
}

.bg {
  @apply bg-bg dark:bg-dark-surface;
}

.bg-reverse {
  @apply bg-surface dark:bg-bg-dark;
}

.muted {
  @apply text-muted dark:text-dark-muted;
}

.line-through-color {
  @apply relative after:absolute after:top-[40%] after:right-0 after:left-0 after:h-[2px] after:content-[''];
}

.h1 {
  @apply text-text dark:text-dark-text text-3xl font-semibold break-words max-sm:text-xl;
}

.h2 {
  @apply text-text dark:text-dark-text text-xl font-bold;
}

.h3 {
  @apply dark:text-dark-text text-lg font-semibold text-gray-800;
}

.tippy-box[data-theme~="cutcost"] {
  @apply bg-white/80 text-black shadow;
}

.tippy-box[data-theme~="cutcost"][data-placement^="bottom"]
  > .tippy-arrow::before {
  @apply border-b-secondary;
}

.mask-text {
  -webkit-mask-image: linear-gradient(to left, transparent, black 40%);
  mask-image: linear-gradient(to left, transparent, black 40%);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}
