<script setup>
import { router } from "@inertiajs/vue3";

const props = defineProps({
    current: {
        type: Boolean,
        default: false,
    },
    href: {
        type: String,
        default: null,
    },
    as: {
        type: String,
        default: "Link", // Link | a | button
    },
});

const goto = () => {
    if (!props.href) return;

    if (props.href === window.location.href) return;

    if (props.as === "a") {
        window.open(props.href, "_blank");
        return;
    }

    if (props.as === "button") return;

    router.get(props.href);
};
</script>

<template>
    <button @click="goto" :class="[
        'px-2 py-1  flex w-full flex-col relative dark:active:bg-bg-dark/40 active:bg-surface/80',
        current ? ' text-secondary ' : ''
    ]" style="-webkit-user-drag: none">
        <div v-if="current" class="absolute bottom-0 right-0 w-full h-0.5 bg-secondary"></div>
        <div class="mx-auto my-auto flex flex-col justify-center relative [&_svg]:mx-auto leading-4">
            <div v-show="!current">
                <slot name="before" />
            </div>
            <div v-show="current">
                <slot name="after" />
            </div>
        </div>
    </button>
</template>
