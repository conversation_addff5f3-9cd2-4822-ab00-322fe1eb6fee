<script setup>
import { ref, watch } from "vue";
import { router } from "@inertiajs/vue3";
import { inject } from "vue";
import { useGlobalStore } from "@s";

const route = inject("route");
const global = useGlobalStore();


const props = defineProps({
    cities: Object,
});

const cityId = ref(global.currentCity);

if (props.cities?.some((city) => city.id === global.currentCity)) {
    cityId.value = global.currentCity;
} else {
    cityId.value = 0;
}

watch(cityId, (newCityId) => {
    setCity(newCityId);
});

const setCity = (cityId) => {

    router.post(
        route("set.city", { city: cityId }),
        {},
        {
            preserveScroll: true,
            showProgress: false,
        },
    );
};

</script>

<template>
    <div>
        <x-select v-model="cityId" :data="cities" class="dark:bg-dark-surface" />
    </div>
</template>