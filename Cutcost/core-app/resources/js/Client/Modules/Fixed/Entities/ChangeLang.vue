<script setup>
import { computed } from "vue";
import { router } from "@inertiajs/vue3";
import { PhGlobe } from "@phosphor-icons/vue";
import { inject } from "vue";
import { useGlobalStore } from "@s";

const route = inject("route");
const global = useGlobalStore();

const currentLanguage = computed(() =>
    global.langs.find((lang) => lang.locale === global.currentLocale),
);

const languages = computed(() => {
    if (!global.isMobile)
        return global.langs.filter(
            (lang) => lang.locale !== global.currentLocale,
        );

    return global.langs;
});

const setLocale = (locale) => {
    router.post(
        route("set.locale", { locale: locale }),
        {},
        {
            preserveScroll: true,
            showProgress: false,
            onSuccess: () => {
                window.location.reload();
            },
        },
    );
};

</script>

<template>
    <div>
        <Dropdown :position="global.isMobile ? 'top-left' : 'bottom'" select>
            <template #button>
                <x-button intent="rounded">
                    <template #icon>
                        <PhGlobe :size="20" weight="bold" />
                    </template>
                    <span>
                        {{ currentLanguage.name }}
                    </span>
                </x-button>
            </template>
            <DropdownItem :class="language.locale === currentLanguage?.locale ? '!text-secondary' : ''
                " @click="setLocale(language.locale)" v-for="language in languages">
                {{ language.name }}
            </DropdownItem>
        </Dropdown>
    </div>
</template>