<script setup>
import { useTemplateRef } from 'vue';
import { useGlobalStore } from "@s";
import { router } from "@inertiajs/vue3";
import Companies from '../../Public/Widgets/Feeds/Companies.vue';
import BottomBarButton from '../Entities/BottomBarButton.vue';
import { PhPlus } from '@phosphor-icons/vue';

defineProps({
    is: {
        default: 'x-button',
    },
});

const modal = useTemplateRef("modal");
const global = useGlobalStore();

if (global.checkAuth())
    router.on('finish', () => {
        if (modal.value)
            modal.value.modalOpen = false;
    })

const openModal = () => {
    if (global.checkAuth()) {
        if (modal.value)
            modal.value.modalOpen = true;
    } else {
        global.auth();
    }
}
</script>

<template>
    <component :is="is === 'BottomBarButton' ? BottomBarButton : 'x-button'" @click="openModal" v-bind="$attrs">
        <template #before>
            <PhPlus :size="28" class="border p-1 rounded-full" />
        </template>
        <slot />
    </component>
    <Teleport to="body" v-if="global.user?.id">
        <Modal :title="__('Hub')" ref="modal">
            <div class="flex">
                <x-button class="w-full" :href="route('partner.business.dashboard')">
                    {{ __("Business dashboard") }}
                </x-button>
            </div>
            <div class="grid grid-cols-2 gap-2 my-2">
                <x-button :href="route('seller.post.create')">
                    {{ __("Create post") }}
                </x-button>
                <x-button :href="route('partner.companies.create')">
                    {{ __("Create business") }}
                </x-button>
            </div>

            <Companies type="company_user" :data="{
                user: global.user.id,
            }" />

        </Modal>
    </Teleport>
</template>