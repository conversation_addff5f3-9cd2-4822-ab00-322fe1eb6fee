<script setup>
import { useHeaderStore } from '../../Stores/use-header-store';
import NavigationButton from '../../Entities/NavigationButton.vue';

const header = useHeaderStore();

const select = (type) => {
    header.chosenTab = type;
    localStorage.setItem(header.navigation + '.selected', type);
}

</script>

<template>
    <div class="flex">
        <NavigationButton v-for="option in header.options" @click="select(option.id)"
            :active="header.chosenTab === option.id">
            {{ __(option.name) }}
        </NavigationButton>
    </div>
</template>
