import { defineStore } from "pinia";
import { ref, watch } from "vue";

export const useHeaderStore = defineStore("header", () => {

  const aside = ref(false);
  const chosenCompany = ref(false);
  const isConfirmationOpen = ref(false);
  const modalStack = ref([]);
  const options = ref(null);

  const title = ref(null);
  // available locations
  const navigation = ref(null);
  const chosenTab = ref(null);
  const logo = ref(null);
  const backUrl = ref(null);

  // internal flags for listener
  let listening = false;
  let onKeyDown = null;

  watch(aside, () => {
    if (aside.value) {
      document.body.classList.add("!overflow-hidden");
    } else if (modalStack.value.length === 0) {
      // only remove when no modals are open
      document.body.classList.remove("!overflow-hidden");
    }
  });

  const openAside = (persistent = null) => {
    if (typeof persistent === "boolean") {
      aside.value = persistent;
    } else {
      aside.value = !aside.value;
    }
  };

  function registerModal(entry) {
    modalStack.value.push(entry);

    if (!listening) {
      onKeyDown = (e) => {
        if (e.key === "Escape") {
          const top = modalStack.value.at(-1);
          if (top && typeof top.close === "function") {
            top.close();
          }
        }
      };
      document.addEventListener("keydown", onKeyDown);
      listening = true;
    }
  }

  function unregisterModal(uid) {
    const idx = modalStack.value.findIndex((m) => m.uid === uid);
    if (idx !== -1) modalStack.value.splice(idx, 1);

    if (modalStack.value.length === 0 && listening) {
      document.removeEventListener("keydown", onKeyDown);
      listening = false;
      if (!aside.value) {
        document.body.classList.remove("!overflow-hidden");
      }
    }
  }

  const clear = () => {
    title.value = null;
    navigation.value = null;
    chosenTab.value = null;
    logo.value = null;
    options.value = null;

    document.removeEventListener('touchend', handleSwipe);
  };

  let startX = 0;
  let startY = 0;
  let endX = 0;
  let endY = 0;

  const swipeThreshold = 40;

  document.addEventListener('touchstart', function (e) {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
  }, false);

  document.addEventListener('touchmove', function (e) {
    endX = e.touches[0].clientX;
    endY = e.touches[0].clientY;
  }, false);

  const swipeEvent = () => {
    document.addEventListener('touchend', handleSwipe);
  }

  const handleSwipe = () => {
    if (!navigation.value || chosenTab.value === 'map') return;

    const diffX = endX - startX;
    const diffY = endY - startY;

    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > swipeThreshold) {

      const currentIndex = options.value.findIndex(o => o.id === chosenTab.value);

      if (diffX < 0) {
        // swipe to right
        if (currentIndex < options.value.length - 1)
          chosenTab.value = options.value[currentIndex + 1]?.id;
      } else {
        // swipe to left
        if (currentIndex > 0)
          chosenTab.value = options.value[currentIndex - 1]?.id;
      }

    }
  }

  return {
    swipeEvent,
    clear,
    logo,
    chosenTab,
    aside,
    chosenCompany,
    isConfirmationOpen,
    modalStack,
    openAside,
    registerModal,
    unregisterModal,
    title,
    navigation,
    options,
    backUrl,
  };
});
