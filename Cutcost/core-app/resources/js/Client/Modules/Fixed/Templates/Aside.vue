<script setup>
import { ref } from "vue";

import MainNav from "../Widgets/MainNav.vue";
import { useGlobalStore } from "@s";
import { useHeaderStore } from "@m/Fixed";
import { router } from "@inertiajs/vue3";
import { PhHeadset, PhInfo, PhPlus } from "@phosphor-icons/vue";
import Footer from "../Widgets/Aside/Footer.vue";
import ChangeLang from "../Entities/ChangeLang.vue";
import HeaderSearch from "../Widgets/HeaderSearch.vue";
import CreationModal from "../Features/CreationModal.vue";

const global = useGlobalStore();
const header = useHeaderStore();
const aside = ref(null);

router.on("finish", () => {
  header.openAside(false);
});

</script>

<template>
  <x-overlay v-if="header.aside && global.isMobile" @click="header.openAside(false)">
  </x-overlay>

  <div ref="aside" :class="header.aside ? 'z-50 max-sm:translate-x-0' : 'max-sm:translate-x-full',
    global.isMobile ? 'overflow-visible' : 'overflow-y-auto scroll-hidden'
    "
    class="backdrop-opacity  max-sm:dark:bg-dark-surface max-sm:bg-bg  max-sm:border-border fixed z-10 h-full w-64   overscroll-contain transition-all max-sm:right-0 sm:max-lg:ml-3">
    <aside>
      <div
        class="dark:bg-dark-surface mt-2 bg-bg border-border flex-col sm:border-2 sm:p-1 sm:shadow-lg max-sm:rounded-l-xl sm:rounded-xl dark:border-0">
        <div class="flex gap-2 justify-between items-center px-4 ">
          <x-logo class="text-3xl" />
          <div>
            <HeaderSearch />
          </div>
        </div>
        <x-hr v-if="global.isMobile" />
        <nav>
          <MainNav />
        </nav>
        <div class=" mt-2 mx-4 flex  flex-col">
          <x-button class="!w-full !mb-3 " intent="success" @click="global.auth()" v-if="!global.checkAuth()">
            {{ __("Login") }}
          </x-button>
          <CreationModal class="!w-full" v-if="!global.isMobile">
            <PhPlus :size="20" />
            {{ __("Create") }}
          </CreationModal>
          <div class="flex justify-between sm:mt-2">
            <div>
              <ThemeToggle />
            </div>
            <div>
              <x-button intent="rounded" as="Link"
                :href="route('messenger.chat', { type: 'nickname', key: 'cutcost_support' })">
                <template #icon>
                  <PhHeadset :size="20" />
                </template>
              </x-button>
            </div>
            <div>
              <x-button intent="rounded" as="Link" :href="route('help.index')">
                <template #icon>
                  <PhInfo :size="20" />
                </template>
              </x-button>
            </div>
            <div>
              <ChangeLang class="!w-full" />
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </aside>
  </div>
</template>
