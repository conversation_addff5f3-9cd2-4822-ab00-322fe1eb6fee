<script setup>
import { PhChatCircleDots, PhHouse, PhMapPinLine, PhPlus, PhSword, PhUser } from "@phosphor-icons/vue";
import BottomBarButton from '../Entities/BottomBarButton.vue';
import CreationModal from '../Features/CreationModal.vue';
import { useGlobalStore } from "@s";

const global = useGlobalStore();

</script>

<template>
    <div class="fixed bottom-0 border-t-1 border-muted left-0 w-full bg-bg dark:bg-dark-surface z-50">
        <div id="upperbar">
        </div>
        <div class="flex gap-1">
            <BottomBarButton :current="$page.component === 'Discount/Feed'" :href="route('feed')">
                <template #before>
                    <PhHouse :size="28" />
                </template>
                <template #after>
                    <PhHouse :size="28" weight="fill" />
                </template>
            </BottomBarButton>
            <BottomBarButton :current="$page.component === 'Discount/ShowLocations'" :href="route('map.index')">
                <template #before>
                    <PhMapPinLine :size="28" />
                </template>
                <template #after>
                    <PhMapPinLine :size="28" weight="fill" />
                </template>
            </BottomBarButton>

            <CreationModal is="BottomBarButton" class="!w-full" :intent="global.isMobile ? 'rounded' : 'primary'" />

            <BottomBarButton :href="route('partner.business.dashboard')"
                :current="$page.component === 'Dashboard/ShowBusiness' || $page.component === 'Dashboard/ShowReferral'">
                <template #before>
                    <PhSword :size="28" />
                </template>
                <template #after>
                    <PhSword :size="28" weight="fill" />
                </template>
            </BottomBarButton>
            <BottomBarButton :href="route('profile')" :current="global.isMyProfile">
                <template #before>
                    <x-model-media static :url="global.user?.avatar" class="!h-8 !w-8 !ring-0">
                        <template #icon>
                            <PhUser :size="26" />
                        </template>
                    </x-model-media>
                </template>
                <template #after>
                    <x-model-media static :url="global.user?.avatar" class="!h-8 !w-8 !ring-0">
                        <template #icon>
                            <PhUser :size="26" />
                        </template>
                    </x-model-media>
                </template>
            </BottomBarButton>
        </div>
    </div>

</template>