<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useGlobalStore } from "@s";
import HeaderMobile from "../Widgets/HeaderMobile.vue";

const global = useGlobalStore();

const isHidden = ref(false);
let lastScrollY = 0;

function handleScroll() {
  const currentScrollY = window.scrollY;

  if (currentScrollY > 50 && currentScrollY > lastScrollY) {
    isHidden.value = true;
  } else if (currentScrollY < lastScrollY) {
    isHidden.value = false;
  }

  lastScrollY = currentScrollY;
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<template>
  <header v-if="global.isMobile" class="  bg-bg dark:bg-dark-surface fixed z-20 flex 
    w-full gap-2 sm:p-1  sm:px-8  backdrop-blur-3xl transition-transform duration-300"
    :style="{ transform: isHidden && global.isMobile ? 'translateY(-100%)' : 'translateY(0)' }">
    <div class="flex w-full lg:mx-auto lg:w-300">
      <HeaderMobile />
    </div>
  </header>
</template>
