<script setup>
import { useGlobalStore } from "@s";

const global = useGlobalStore();
</script>

<template>
    <footer class="text-xs text-text dark:text-dark-text px-4 py-6 select-none ">
        <div class="flex justify-center flex-wrap gap-x-1 gap-y-2 ">
            <Link :href="route('privacy')"
                class="hover:underline hover:text-accent dark:hover:text-dark-accent transition">
            {{ __("Privacy Policy") }}
            </Link>
            <span>·</span>
            <Link :href="route('terms')"
                class="hover:underline hover:text-accent dark:hover:text-dark-accent transition">
            {{ __("Terms") }}
            </Link>
            <span>·</span>
            <Link :href="route('game.costsaber')"
                class=" hover:underline hover:text-accent dark:hover:text-dark-accent transition">
            {{ __("Whac A Corp") }}
            </Link>
            <span>·</span>
            <Link :href="route('plans')"
                class="animate-pulse text-success hover:underline hover:text-accent dark:hover:text-dark-accent transition">
            {{ __("Premium") }}
            </Link>
        </div>
    </footer>
</template>
