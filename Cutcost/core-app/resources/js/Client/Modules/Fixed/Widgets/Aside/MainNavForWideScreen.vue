<script setup>
import { useGlobalStore } from "@s";
import {
    PhBellRinging,
    PhChatCircleDots,
    PhHouse,
    PhMapPinLine,
    PhUser,
} from "@phosphor-icons/vue";

const global = useGlobalStore();
</script>

<template>
    <AsideButton :href="route('feed')" :current="$page.component === 'Discount/Feed'">
        <template #icon>
            <PhHouse weight="bold" :size="20" />
        </template>
        <span>{{ __("Coupons") }}</span>
    </AsideButton>

    <AsideButton :href="route('map.index')" :current="$page.component === 'Discount/ShowLocations'">
        <template #icon>
            <PhMapPinLine weight="bold" :size="20" />
        </template>
        <span>{{ __("Locations") }}</span>
    </AsideButton>

    <x-hr />
    <AsideButton :href="route('profile', { user: global.user?.nickname })" :current="global.isMyProfile">
        <template #icon>
            <PhUser weight="bold" :size="20" />
        </template>
        <span>{{ __("Profile") }}</span>
    </AsideButton>
    <AsideButton :href="route('partner.business.dashboard')" :current="$page.component === 'Dashboard/ShowDashboard'">
        <template #icon>
            <PhBank weight="bold" :size="20" />
        </template>
        <span>{{ __("Business") }}</span>
    </AsideButton>
    <AsideButton :href="route('seller.saved')" :current="$page.component === 'Seller/Saved'">
        <template #icon>
            <PhBookmarksSimple weight="bold" :size="20" />
        </template>
        <span>{{ __("Saved") }}</span>
    </AsideButton>
    <AsideButton :href="route('seller.settings')" :current="$page.component === 'Seller/Settings'">
        <template #icon>
            <PhGear weight="bold" :size="20" />
        </template>
        <span>{{ __("Settings") }}</span>
    </AsideButton>
    <AsideButton :href="route('seller.notifications')" :current="$page.component === 'Seller/Notifications'"
        :badge="global?.notificationsCount">
        <template #icon>
            <PhBellRinging weight="bold" :size="20" />
        </template>
        <span>{{ __("Notifications") }}</span>
    </AsideButton>

    <x-hr />

    <AsideButton :href="route('messenger.index')"
        :current="$page.component === 'Messenger/Chats' || $page.component === 'Messenger/ShowChat'">
        <template #icon>
            <PhChatCircleDots weight="bold" :size="20" />
        </template>
        <span>{{ __("Cuttalk") }}</span>
    </AsideButton>


</template>