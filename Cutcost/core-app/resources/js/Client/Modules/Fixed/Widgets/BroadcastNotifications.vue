<script setup>
import { onMounted, ref } from "vue";
import { router } from "@inertiajs/vue3";
import { useGlobalStore } from "@s";

const global = useGlobalStore();

const depth = ref(0);

onMounted(() => {
  // to prefetch data and dont lost messages
  window.addEventListener("popstate", () => {
    if (global.isReload || depth.value > 0) {

      depth.value = depth.value === 0 ? 2 : depth.value;

      depth.value--;

      console.log('reloaded');
      router.reload({
        preserveState: false,
        preserveScroll: true
      });

      global.isReload = false;
    }
  });

  //to prevent data lost when popstate event
  router.on("before", () => {
    global.prevUrl = window.location.href;
  });
});
</script>
<template></template>
