<script setup>
import { <PERSON><PERSON><PERSON>, PhChatCircleDots, PhList } from '@phosphor-icons/vue';
import HeaderSearch from './HeaderSearch.vue';
import { useHeaderStore } from '../Stores/use-header-store';
import GoBack from "@s/Entities/GoBack.vue";
import NavigationHeader from '../Features/Navigation/NavigationHeader.vue';
import { useGlobalStore } from "@s";

const header = useHeaderStore();
const global = useGlobalStore();

</script>

<template>
    <div class="flex flex-col w-full">
        <div class="flex w-full px-2 border-b-1 border-muted items-center justify-between ">

            <div class="flex  items-center  text">
                <x-logo class="text-[1.7rem] mt-0.5 " />
            </div>
            <div class="flex items-center ">
                <div>
                    <HeaderSearch />
                </div>
                <div>
                    <x-button intent="fade" :href="route('seller.notifications')"
                        :active="$page.component === 'Seller/Notifications'" class="flex !px-2 !border-0">
                        <PhBell :size="24" />
                        <div v-if="global?.notificationsCount"
                            class=" px-1 -ml-2 bg-error/90 !text-white rounded-full ">{{
                                global?.notificationsCount }}</div>
                    </x-button>
                </div>
                <div>
                    <x-button :href="route('messenger.index')" intent="fade" class="flex !px-2  !border-0"
                        :class="{ '!text-secondary': $page.component === 'Messenger/ShowChat' || $page.component === 'Messenger/Chats' }">
                        <PhChatCircleDots :size="24" />
                    </x-button>
                </div>
                <div>
                    <x-button intent="fade" class="flex !px-2  !border-0" @click.stop="header.openAside(true)"
                        as="button">
                        <PhList :size="24" />
                    </x-button>
                </div>
            </div>

        </div>
        <div class="border-b-1 border-muted">
            <div class="flex flex-nowrap overflow-hidden !h-10 items-center gap-2" v-if="header?.title">
                <div class="ml-2">
                    <GoBack :url="header?.backUrl" />
                </div>
                <div class="uppercase text whitespace-nowrap overflow-ellipsis overflow-hidden">
                    {{ __(header?.title) }}
                </div>
            </div>
            <div>
                <NavigationHeader v-if="header?.navigation" />
            </div>
        </div>
    </div>


</template>