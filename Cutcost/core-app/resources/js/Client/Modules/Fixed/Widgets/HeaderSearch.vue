<script setup>
import {
  nextTick,
  shallowRef,
  useTemplateRef,
  ref,
  computed,
  getCurrentInstance,
} from "vue";
import { router } from "@inertiajs/vue3";
import { UsersSearch, CouponsSearch } from "@s";
import { PhMagnifyingGlass } from "@phosphor-icons/vue";

const modal = useTemplateRef("modal");
const searchField = useTemplateRef("searchField");
const searchType = shallowRef("coupons");

const { proxy } = getCurrentInstance();

const search = ref("");
const message = ref("");

const setStatus = (status) => {
  message.value = status;
};

const changeType = (type) => {
  message.value = null;
  searchType.value = type;
  openSearch();
};

const openSearch = () => {
  modal.value.modalOpen = true;
  nextTick(() => {
    searchField.value?.focus();
  });
};

router.on("finish", () => {
  modal.value.modalOpen = false;
});

const placeholder = computed(() => {
  if (searchType.value === "coupons")
    return proxy.__("Search coupons");
  return proxy.__("Search users");
});
</script>
<template>
  <x-button :href="route('advanced.search')" :active="$page.component === 'Search/AdvancedSearch'"
  intent="fade" class="flex !px-2 !border-0">
    <template #icon>
      <PhMagnifyingGlass :size="24" />
    </template>
  </x-button>
  <Modal ref="modal" :title="__('Search')">
    <x-input v-model="search" :placeholder="placeholder" type="search" ref="searchField" />
    <div class="mt-4 flex justify-between gap-2">
      <x-button intent="fade" class="w-full justify-center"
        :class="searchType === 'users' ? '!bg-secondary !text-bg' : ''" @click="changeType('users')">
        {{ __("Users") }}
      </x-button>
      <x-button :class="searchType === 'coupons' ? '!bg-secondary !text-bg' : ''" intent="fade"
        class="w-full justify-center" @click="changeType('coupons')">
        {{ __("Coupons") }}
      </x-button>
    </div>
    <div class="mt-2">
      <div v-show="searchType === 'users'">
        <UsersSearch :search :type="searchType" @status="setStatus" />
      </div>
      <div v-show="searchType === 'coupons'">
        <CouponsSearch :type="searchType" :search @status="setStatus" />
      </div>
      <div class="mt-6 rounded text-center text-2xl" v-if="message">
        <strong> {{ message }}</strong>
      </div>
    </div>
  </Modal>
</template>
