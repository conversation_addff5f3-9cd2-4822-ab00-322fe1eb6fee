<script setup>
import { onMounted, useTemplateRef } from "vue";
import { router } from "@inertiajs/vue3";
import { useGlobalStore } from "@s";
import {
    PhAppleLogo,
    PhAt,
    PhGoogleLogo,
    PhWindowsLogo,
} from "@phosphor-icons/vue";
import { inject } from "vue";

const route = inject("route");

const global = useGlobalStore();

const modal = useTemplateRef("modal");

onMounted(() => {
    document.addEventListener("login-modal-open", () => {
        modal.value.modalOpen = true;
    });

    const params = new URLSearchParams(window.location.search);

    if (params.get('login') === '1' && !global.checkAuth()) {
        modal.value.modalOpen = true;
    }
});

router.on('finish', (event) => {
    if (event.target.URL.includes('login=1') && !global.checkAuth()) {
        modal.value.modalOpen = true;
    }
});

</script>

<template>
    <Modal ref="modal" :title="__('Login with')">
        <div class="my-4 flex w-full flex-col gap-4">
            <x-button as="a" intent="rounded" current force :href="route('provider', { providerId: 1 })">
                <PhGoogleLogo :size="30" class="fill-red-400" weight="bold" /> Google
            </x-button>

            <x-button as="a" intent="rounded" current force :href="route('provider', { providerId: 4 })">
                <PhAppleLogo :size="30" weight="fill" /> Apple
            </x-button>

            <x-button as="a" intent="rounded" current force :href="route('provider', { providerId: 2 })">
                <PhWindowsLogo :size="30" weight="bold" />
                Microsoft
            </x-button>
        </div>

        <Link v-if="!global.isProd"
            class="flex w-full items-center justify-center gap-3 rounded-lg bg-gray-700 px-5 py-3 text-lg font-medium text-white transition-all duration-200 hover:bg-gray-800"
            :href="route('email.auth')">
        <PhAt :size="28" />
        Email
        </Link>
    </Modal>
</template>