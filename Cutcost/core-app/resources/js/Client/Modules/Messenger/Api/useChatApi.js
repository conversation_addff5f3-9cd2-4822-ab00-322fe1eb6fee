import { inject } from "vue";

export const useChatApi = () => {
  const route = inject("route");

  /**
   * @async
   * @param {Object} chat
   * @param {boolean|number} [cursor=false]
   * @returns {void}
   */
  const getPaginatedMessages = async (chat, cursor = false) => {
    try {
      return await axios.post(
        route("messenger.api.chat.paginate", {
          chat: chat.id,
          cursor: cursor,
        }),
      );
    } catch (error) {
      console.error("Error while updating:", error);
    }
  };

  const unBlockFriend = async (chat) => {
    try {
      const response = await axios.patch(
        route("messenger.api.chat.unblock", { chat: chat.id }),
      );

      return response.data.blocked_by;
    } catch (err) {
      console.error(err);
    }
  };

  const blockFriend = async (chat) => {
    try {
      const response = await axios.patch(
        route("messenger.api.chat.block", { chat: chat.id }),
      );
      return response.data.blocked_by;
    } catch (err) {
      console.error(err);
    }
  };

  return {
    getPaginatedMessages,
    blockFriend,
    unBlockFriend,
  };
};
