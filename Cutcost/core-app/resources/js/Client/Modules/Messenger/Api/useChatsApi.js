import { inject } from "vue";

export const useChatsApi = () => {
  const route = inject("route");

  const getChats = async (user, mode) => {
    try {
      const response = await axios.get(
        route("messenger.api.chats", {
          user: user.id,
          mode: mode,
        }),
      );
      return response.data;
    } catch (error) {
      console.error("Error while fetching chats:", error);
    }
  };

  const quitChat = async (chat) => {
    try {
      await axios.delete(
        route("messenger.api.chat.quit", {
          chat: chat.id,
        }),
      );
    } catch (error) {
      console.error("Error while quitting chat:", error);
    }
  };

  return {
    getChats,
    quitChat,
  };
};
