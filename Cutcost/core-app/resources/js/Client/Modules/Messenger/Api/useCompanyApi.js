import { inject } from "vue";

export const useCompanyApi = () => {
  const route = inject("route");

  const getCompany = async (company) => {
    try {
      const response = await axios.get(
        route("messenger.api.company.info", {
          company: company.slug,
        }),
      );
      return response.data;
    } catch (error) {
      console.error("Error while fetching company:", error);
    }
  };

  return {
    getCompany,
  };
};
