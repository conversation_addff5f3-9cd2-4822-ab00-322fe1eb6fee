import { inject } from "vue";
import { useToastStore } from "@s";

export const useGroupApi = () => {
  const route = inject("route");
  const toast = useToastStore();

  /**
   * removes user from group by admin
   *
   * @async
   * @param {Object} chat
   * @param {Object} user
   * @returns {boolean}
   */
  const removeUser = async (chat, user) => {
    let response;
    try {
      response = await axios.delete(
        route("messenger.groups.remove.user", { chat: chat.id, user: user.id }),
      );

      return response.status === 200;
    } catch (err) {
      console.error(err);
    } finally {
      if (response?.data) {
        toast.raw(response.data.message, response.data.status);
      }
    }
  };

  /**
   * add user to group by admin
   *
   * @async
   * @param {Object} chat
   * @param {Object} user
   * @returns {boolean}
   */
  const addUser = async (chat, nickname) => {
    try {
      const response = await axios.patch(
        route("messenger.groups.add.user", {
          chat: chat.id,
        }),
        {
          nickname: nickname,
        },
      );

      toast.raw(response.data.message, response.data.status);
      return [response.data.user, response.status === 200];
    } catch (err) {
      console.error(chat, nickname, err?.response?.data || err.message);
      const message =
        err?.response?.data?.message || "Ошибка при добавлении пользователя";
      const status = err?.response?.data?.status || "error";
      toast.raw(message, status);
      return [null, false];
    }
  };

  const updateGroupAvatar = async (chat, formData) => {
    try {
      const response = await axios.post(
        route("messenger.groups.update.group.avatar", {
          chat: chat.id,
        }),
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );

      toast.raw(response.data.message, response.data.status);
      return response.status === 200;
    } catch (err) {
      console.error(err);

      return false;
    }
  };

  const updateGroupName = async (chat, name) => {
    try {
      const response = await axios.put(
        route("messenger.groups.update.group.name", {
          chat: chat.id,
        }),
        {
          name: name,
        },
      );

      toast.raw(response.data.message, response.data.status);
      return response.status === 200;
    } catch (err) {
      console.error(err);

      return false;
    }
  };

  return {
    removeUser,
    addUser,
    updateGroupAvatar,
    updateGroupName,
  };
};
