import { inject } from "vue";

export const useMessageApi = () => {
  const route = inject("route");

  // messages readed when load new messages vie scroll or when open chat
  // here method only for read 1 received message
  const readMessage = async (message) => {
    try {
      await axios.patch(
        route("messenger.api.messages.read", {
          message: message.id,
        }),
      );
    } catch (err) {
      console.error(err);
    }
  };

  const deleteMessage = async (chat, message) => {
    try {
      await axios.delete(
        route("messenger.api.message.destroy", {
          chat: chat.id,
          message: message.id,
        }),
      );
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * void because all data is in sockets
   * @async
   * @param {Object} chat
   * @param {Object} request
   * @returns {void}
   */
  const sendMessage = async (chat, request) => {
    try {
      await axios.post(
        route("messenger.api.message.send", {
          chat: chat.id,
        }),
        request,
      );
    } catch (error) {
      console.error("Error while sending:", error);
    }
  };

  /**
   * void because all data is in sockets
   * @async
   * @param {Object} chat
   * @param {Object} message
   * @param {Object} request
   * @returns {void}
   */
  const updateMessage = async (chat, message, request) => {
    try {
      await axios.put(
        route("messenger.api.message.update", {
          message: message.id,
          chat: chat.id,
        }),
        request,
      );
    } catch (error) {
      console.error("Error while updating:", error);
    }
  };

  return {
    readMessage,
    deleteMessage,
    sendMessage,
    updateMessage,
  };
};
