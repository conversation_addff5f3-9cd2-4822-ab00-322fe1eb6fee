export const formatChatDate = () => {
  const months = {
    en: [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ],
    ru: [
      "янв",
      "фев",
      "мар",
      "апр",
      "май",
      "июн",
      "июл",
      "авг",
      "сен",
      "окт",
      "ноя",
      "дек",
    ],
    lt: [
      "sau",
      "vas",
      "kov",
      "bal",
      "geg",
      "bir",
      "lie",
      "rugp",
      "rugs",
      "spal",
      "lap",
      "gru",
    ],
    es: [
      "ene",
      "feb",
      "mar",
      "abr",
      "may",
      "jun",
      "jul",
      "ago",
      "sep",
      "oct",
      "nov",
      "dic",
    ],
    de: [
      "Jan",
      "Feb",
      "Mär",
      "Apr",
      "<PERSON>",
      "<PERSON>",
      "Jul",
      "Aug",
      "Sep",
      "Okt",
      "Nov",
      "Dez",
    ],
    pl: [
      "sty",
      "lut",
      "mar",
      "kwi",
      "maj",
      "cze",
      "lip",
      "sie",
      "wrz",
      "paź",
      "lis",
      "gru",
    ],
  };
  const now = new Date();
  const currentYear = now.getFullYear();

  const format = (dateString, language = "lt") => {
    const date = new Date(dateString);
    const day = date.getDate();
    const monthName = months[language][date.getMonth()];
    const dateYear = date.getFullYear();
    // If current year (20**), show only month and day, else show full date
    if (dateYear === currentYear) {
      return `${monthName} ${day}`;
    }

    return `${monthName} ${day}, ${dateYear}`;
  };
  return { format };
};
