import { onMounted, onUnmounted } from "vue";
import { useMessageActions } from "../../Services/useMessageActions";
import GraphemeSplitter from "grapheme-splitter";
import { useInputStore } from "../../Stores/useInputStore";
import { useChatStore } from "../../Stores/useChatStore";

export const useChatInput = () => {
  const { handleSendMessage, handleUpdateMessage } = useMessageActions();

  const inputStore = useInputStore();
  const chatStore = useChatStore();

  const inputSend = async (chat, request) => {
    if (!validate(request)) return;
    await handleSendMessage(chat, request);
  };

  const inputUpdate = async (chat, message, request) => {
    if (!validate(request)) return;
    await handleUpdateMessage(chat, message, request);
  };

  const validate = (request) => {
    const content = request.content;

    // if images esists, then we don't need content
    if (request?.upload?.temp_uuid || request?.media_changed) return true;

    if (getRealLength(content) === 0) return false;
    if (getRealLength(content) > 3000) return false;
    return true;
  };

  const getRealLength = (str) => {
    const splitter = new GraphemeSplitter();
    return splitter.countGraphemes(str.trim());
  };

  onMounted(() => {
    window.addEventListener("keyup", focusTextFieldWhenTyping);
  });

  onUnmounted(() => {
    window.removeEventListener("keyup", focusTextFieldWhenTyping);
  });

  const focusTextFieldWhenTyping = (e) => {
    // if user focused on input or textarea dont reset
    if (
      document.activeElement === inputStore.textField ||
      chatStore.blockedBy ||
      document.activeElement.tagName === "INPUT" ||
      document.activeElement.tagName === "TEXTAREA"
    )
      return;

    //focus on input
    inputStore.textField?.focus();

    const char = e.key;
    // to prevent char lost
    if (char?.length === 1) inputStore.content += char;
  };

  return {
    inputSend,
    inputUpdate,
  };
};
