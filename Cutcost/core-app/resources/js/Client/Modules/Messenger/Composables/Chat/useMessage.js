import { useChatStore } from "../../Stores/useChatStore";
import { useMessagesContainer } from "./useMessagesContainer";
import { useGlobalStore } from "@s";
import { useMessageApi } from "../../Api/useMessageApi";

// messages CUD + seen
export const useMessage = () => {
  // use global messages
  const chat = useChatStore();

  // use global user
  const global = useGlobalStore();

  // read message
  const api = useMessageApi();

  // scroll bottom when send|get message
  const { toBottom } = useMessagesContainer();

  /**
   * Add user message to chat
   * @param {Object} message
   */
  const addMessage = (message) => {
    chat.messages.push(message);

    if (message.user.id !== global.user.id) {
      api.readMessage(message);
    }

    toBottom();
  };

  /**
   * remove message from chat
   * @param {Number} messageId
   */
  const removeMessage = (messageId) => {
    chat.messages = chat.messages.filter((message) => message.id !== messageId);
  };

  /**
   * Description placeholder
   * @param {Object} message
   */
  const updateMessage = (message) => {
    const index = chat.messages.findIndex((m) => m.id === message.id);
    if (index !== -1) {
      chat.messages[index] = message;
    }
  };

  /**
   * change message status to seen
   * @param {Array} messageIds
   */
  const seeMessage = (messageIds) => {
    // console.log(messageIds);

    chat.messages = chat.messages.map((message) => {

      if (messageIds?.includes(message.id)) {
        message.status = "seen";
      }
      
      return message;
    });
  };

  return { addMessage, removeMessage, updateMessage, seeMessage };
};
