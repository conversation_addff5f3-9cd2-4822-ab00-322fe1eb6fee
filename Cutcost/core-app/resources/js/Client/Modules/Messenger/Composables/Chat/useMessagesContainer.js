import { nextTick, shallowRef } from "vue";
import { useChatApi } from "../../Api/useChatApi";
import { useChatStore } from "../../Stores/useChatStore";

export const useMessagesContainer = () => {
  const noMoreMessages = shallowRef(false);

  // api
  const { getPaginatedMessages } = useChatApi();

  // messages container
  const chatStore = useChatStore();

  const toBottom = (smooth = false) => {
    nextTick(() => {
      if (!chatStore.container) {
        console.warn("Container not available");
        return;
      }

      chatStore.container.scrollTo({
        top: chatStore.container.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
    });
  };

  const showToBottomButton = () => {
    if (!chatStore.container) {
      console.warn("Container not available");
      return false;
    }

    if (
      chatStore.container.scrollTop + chatStore.container.clientHeight <=
      chatStore.container.scrollHeight - 100
    ) {
      return true;
    } else {
      return false;
    }
  };

  const getMessages = async (chat, messages) => {
    try {
      // check if no more messages
      if (noMoreMessages.value) return null;

      // last message id (first message id, cause we are paginating in reverse order)
      const cursor = calculateCursor(messages);
      const response = await getPaginatedMessages(chat, cursor);

      // if no messages return, then no more messages
      if (response.data.messages.length === 0) noMoreMessages.value = true;

      return response.data.messages;
    } catch (err) {
      console.error(err);
    }
  };

  /**
   * Calculates first message id
   * private method
   *
   * @param {object} messages
   * @returns {void}
   */

  const calculateCursor = (messages) => {
    if (!messages.length) return false;
    return messages[0].id;
  };

  return { toBottom, showToBottomButton, getMessages };
};
