import { useChatsApi } from "../../Api/useChatsApi";
import { useChatsStore } from "../../Stores/useChatsStore";

export const useChats = () => {
  const { quitChat } = useChatsApi();
  const chatsStore = useChatsStore();

  const sortChats = (chats) => {
    if (!chats) return [];

    const notes = chats.filter((c) => c.type === "notes");

    const others = chats
      .filter((c) => c.type !== "notes")
      .toSorted((a, b) => {
        const aTime = new Date(a.last_message?.sent_date || a.created_at);
        const bTime = new Date(b.last_message?.sent_date || b.created_at);

        if (aTime > bTime) return -1;
        if (aTime < bTime) return 1;

        return (b.new_messages || 0) - (a.new_messages || 0);
      });

    return [...notes, ...others];
  };

  // TODO move to chats service
  const handleQuitChat = async (chat) => {
    await quitChat(chat);
    chatsStore.chats = chatsStore.chats.filter((c) => c.id !== chat.id);
  };

  return { sortChats, handleQuitChat };
};
