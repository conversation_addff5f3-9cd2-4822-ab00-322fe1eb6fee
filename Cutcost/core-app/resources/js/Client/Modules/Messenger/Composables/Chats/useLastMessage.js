import { useChatsStore } from "../../Stores/useChatsStore";
import { useChats } from "./useChats";

export const useLastMessage = () => {
  const chatsStore = useChatsStore();
  const { sortChats } = useChats();

  const updateLastMessageWhenSend = (chatId, message) => {
    // find chat inside chats and reactivly update it
    const chat = findChatById(chatId);
    chat.last_message = message;

    // increment new messages
    // NOW ONLY INCREMENT made correctly

    // WHEN MESSAGES DELETED new_messages STARTS COUNTING INCORRECTLY !!!
    // todo fix it | Priority "low"

    ++chat.new_messages;

    chatsStore.chats = sortChats(chatsStore.chats);
  };

  const updateLastMessageWhenUpdate = (chatId, message) => {
    // find chat inside chats and reactivly update it
    const chat = findChatById(chatId);

    // update only if updated this (last) message
    if (chat.last_message.id === message.id) {
      chat.last_message = message;
      // chatsStore.chats = sortChats(chatsStore.chats); // sort chats
      return;
    }
  };

  const updateLastMessageWhenDelete = (chatId, messageId, priorMessage) => {
    // find chat inside chats and reactivly update it
    const chat = findChatById(chatId);

    // update only if deleted this (last) message
    if (chat.last_message.id === messageId) {
      chat.last_message = priorMessage;

      chatsStore.chats = sortChats(chatsStore.chats); // sort chats

      //   chat.new_messages--; // decrement new messages counter look at 'updateLastMessageWhenSend'

      return;
    }
  };

  /**
   *
   * @param {number} chatId
   * @param {number[]} messagesIds
   */
  const seenLastMessage = (chatId, messagesIds) => {
    const chat = findChatById(chatId);

    // get last message id from viewed messages
    const lastMessageId = Math.max(...messagesIds);

    // update only if seen this (last) message
    if (chat.last_message.id === lastMessageId) {
      chat.last_message.status = "seen";
      return;
    }
  };

  // helper
  const findChatById = (chatId) => {
    return chatsStore.chats.find((chat) => chat.id === chatId);
  };

  return {
    updateLastMessageWhenSend,
    updateLastMessageWhenUpdate,
    updateLastMessageWhenDelete,
    seenLastMessage,
  };
};
