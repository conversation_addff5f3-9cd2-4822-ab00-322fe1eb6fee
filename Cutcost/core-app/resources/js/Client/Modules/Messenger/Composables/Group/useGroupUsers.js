import { useGroupApi } from "../../Api/useGroupApi";

export const useGroupUsers = () => {
  const api = useGroupApi();

  /**
   * retrives user from api and add to group
   *
   * @async
   * @param {Object} chat
   * @param {string} nickname
   * @returns {user}
   */
  const addUser = async (chat, nickname) => {
    const [addedUser, response] = await api.addUser(chat, nickname);

    if (!response) return;

    return addedUser;
  };

  /**
   * removes user from group by admin
   *
   * @async
   * @param {*} chat
   * @param {*} user
   * @returns {void}
   */
  const removeUser = async (chat, user) => {
    const response = await api.removeUser(chat, user);
    if (!response) return;
  };

  return { addUser, removeUser };
};
