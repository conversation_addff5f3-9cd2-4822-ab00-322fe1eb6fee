import { onUnmounted, ref } from "vue";
import { useLastMessage } from "../../Composables/Chats/useLastMessage";
import { useMessage } from "../Chat/useMessage";
import { useChat } from "../Chat/useChat";

/**
 * type set inside page mount
 *
 * type - chat|chats
 */
export const useMessengerSockets = (type) => {
  const initedIds = ref([]);

  const { blockFriend, unBlockFriend } = useChat();

  const {
    updateLastMessageWhenSend,
    updateLastMessageWhenUpdate,
    updateLastMessageWhenDelete,
    seenLastMessage,
  } = useLastMessage();

  const { addMessage, removeMessage, updateMessage, seeMessage } = useMessage();

  onUnmounted(() => {
    destroySockets(initedIds.value);
  });

  /**
   * @param {array} chatIds
   */
  const initSockets = (chatIds) => {
    // destroy old sockets
    if (initedIds.value.length) {
      destroySockets(initedIds.value);
    }

    // if no chat ids, do nothing
    if (!chatIds.length) {
      return;
    }

    // init sockets for each chat id
    // or jist for one chat
    // and make ui changes in dependency of type
    chatIds.forEach((chatId) => {
      Echo.private(`messenger-chat.${chatId}`)
        .listen(".message.sent", (e) => {
          if (type === "chats") {
            updateLastMessageWhenSend(e.chat_id, e.message);
            return;
          }
          if (type === "chat") {
            addMessage(e.message);
            return;
          }
        })
        .listen(".message.updated", (e) => {
          if (type === "chats") {
            updateLastMessageWhenUpdate(e.chat_id, e.message);
            return;
          }
          if (type === "chat") {
            updateMessage(e.message);
            return;
          }
        })
        .listen(".message.deleted", (e) => {
          if (type === "chats") {
            // message id for understanding which message was deleted !
            updateLastMessageWhenDelete(
              e.chat_id,
              e.message_id,
              e.prior_message,
            );
            return;
          }
          if (type === "chat") {
            removeMessage(e.message_id);
            return;
          }
        })
        .listen(".message.seen", (e) => {
          if (type === "chats") {
            seenLastMessage(e.chat_id, e.ids);
            return;
          }
          if (type === "chat") {
            seeMessage(e.ids);
            return;
          }
        });
    });

    // for chat specific channel
    initForChat(chatIds[0]);

    console.log("inited!");
    initedIds.value = chatIds;
  };

  /**
   * Specific for chat
   * @param {string} chatId
   */
  const initForChat = (chatId) => {
    Echo.private(`messenger-chat.${chatId}`).listen(".chat.blocked", (e) => {
      if (e.blocked_by) {
        blockFriend(e.blocked_by);
        return;
      }
      unBlockFriend();
    });
  };

  const destroySockets = (chatIds) => {
    chatIds.forEach((chatId) => {
      Echo.leave(`messenger-chat.${chatId}`);
    });
    initedIds.value = [];
    console.log("destroyed!");
  };

  return { initSockets };
};
