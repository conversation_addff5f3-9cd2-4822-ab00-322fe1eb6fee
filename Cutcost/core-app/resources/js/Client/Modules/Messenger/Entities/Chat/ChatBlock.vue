<script setup>
import { useChatService } from "../../Services/Chat/useChatService";
import { shallowRef, nextTick } from "vue";
import { useMessagesContainer } from "../../Composables/Chat/useMessagesContainer";

const blockUserDialog = shallowRef(false);
const { handleBlockChat } = useChatService();
const messagesContainer = useMessagesContainer();

const props = defineProps({
    chat: Object,
});

const emit = defineEmits(["closeModal"]);

const block = async () => {
    await handleBlockChat(props.chat);
    blockUserDialog.value = false;
    emit("closeModal");
    nextTick(() => {
        messagesContainer.toBottom();
    });
};


</script>

<template>

    <x-confirmation v-if="blockUserDialog" :message="__('Are you sure you want to block user?')
        " :for="chat.id + '_block_chat'" @confirm="block" @cancel="blockUserDialog = false" />
    <DropdownItem @click="blockUserDialog = true" class="!text-error">
        {{ __("Block") }}
    </DropdownItem>

</template>