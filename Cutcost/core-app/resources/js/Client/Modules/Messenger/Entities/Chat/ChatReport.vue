<script setup>
import { PhFlag } from "@phosphor-icons/vue";
import { Report } from "@s";

const props = defineProps({
  chat: Object,
  noIcon: Boolean
});

const reportSubject = {
  chat: props.chat,
  friend: props.chat.friend,
};
</script>
<template>
  <Report model-type="chat" :model-id="chat.id">
    <x-button class=" w-full flex-col" @click="$emit('closeModal')" intent="danger">
      <PhFlag :size="32" v-if="!noIcon" />
      {{ __("Report") }}
    </x-button>
  </Report>
</template>
