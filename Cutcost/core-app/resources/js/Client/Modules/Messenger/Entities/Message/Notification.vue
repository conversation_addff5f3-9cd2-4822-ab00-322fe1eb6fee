<script setup>
import { computed } from 'vue';
import { useTranslation } from "@s";

const props = defineProps({
  message: Object,
});

const notification = computed(() => useTranslation.__(props.message?.content));
</script>

<template>
  <div class="my-4 flex justify-center">
    <div
      class="dark:bg-dark-border dark:text-dark-accent bg-surface text-text rounded-full px-4 py-1 text-sm select-none">
      <span v-if="message.content === 'group_created'">
        {{ notification }}
      </span>
      <span v-else-if="message.content === 'joined_the_chat'">
        <Link :href="route('profile', { user: message.user.nickname })" class="hover:underline"> {{
          message.user.display_name }} </Link> {{ notification }}
      </span>
    </div>
  </div>
</template>
