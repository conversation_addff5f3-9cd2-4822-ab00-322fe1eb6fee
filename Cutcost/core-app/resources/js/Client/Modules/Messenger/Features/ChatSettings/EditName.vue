<script setup>
import { useGroupApi } from "../../Api/useGroupApi";
import { ref } from "vue";

const props = defineProps({
  chat: Object,
  groupCreator: Boolean,
});

const { updateGroupName } = useGroupApi();
const newName = ref(props.chat?.name);

const editName = async () => {
  await updateGroupName(props.chat, newName.value);
};
</script>
<template>
  <div class="flex items-center gap-1 max-sm:flex-col">
    <div v-if="!groupCreator" class="flex h1">
      {{ chat.name === 'Notes' ? __("Notes") : chat.name }}
    </div>
    <div v-else class="flex flex-col">
      <Label>{{ __("Group name") }}</Label>
      <div class="flex flex-row items-center gap-2">
        <x-input v-model="newName" :placeholder="__('New name')" />
        <x-button @click="editName">
          {{ __("Update") }}
        </x-button>
      </div>
    </div>
  </div>
</template>
