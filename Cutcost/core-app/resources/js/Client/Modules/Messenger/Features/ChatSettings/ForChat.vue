<script setup>
import { nextTick } from "vue";
import { inject } from "vue";
import { useMessagesContainer } from "../../Composables/Chat/useMessagesContainer";
import { useChatService } from "../../Services/Chat/useChatService";

const messagesContainer = useMessagesContainer();

const route = inject("route");

const props = defineProps({
  chat: Object,
});

const emit = defineEmits(["closeModal"]);

</script>

<template>
  <div>
    <div class="grid w-full grid-cols-2 items-center gap-2" v-if="chat.type !== 'notes'">
      <template>
        <x-button class="flex-col" as="Link" :href="route('profile', { user: chat.friend.nickname })">
          {{ __("Profile") }}
        </x-button>
      </template>
    </div>
  </div>
</template>
