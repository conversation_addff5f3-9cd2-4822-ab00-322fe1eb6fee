<script setup>
import { useGlobalStore } from "@s";
import { computed, onMounted, ref } from "vue";
import { useCompanyApi } from "../../Api/useCompanyApi";
import ShowCompanyContent from "@m/Public/Widgets/ShowCompany/ShowCompanyContent.vue";

const { getCompany } = useCompanyApi();

const company = ref({});
const locations = ref([]);
const subscribers = ref(0);

const global = useGlobalStore();

const props = defineProps({
  chat: Object,
  companies: Object,
});

const isYourCompany = computed(() => {
  return props.companies?.some((c) => c.id === props.chat.company.id);
});

onMounted(async () => {
  const data = await getCompany(props.chat.company);

  company.value = data.company;
  locations.value = data.locations;
  subscribers.value = data.subscribers;
});
</script>

<template>
  <div>
    <div class=" w-full flex  gap-2 mb-5">
      <x-button v-if="!isYourCompany" class=" text-center" as="Link"
        :href="route('company.show', { slug: chat.company.slug })">
        {{ __("View company") }}
      </x-button>
      <x-button v-else as="Link" :href="route('profile', { user: chat.friend.nickname })">
        {{ __("Profile") }}
      </x-button>

    </div>
    <div class="flex flex-col" v-if="!isYourCompany">
      <ShowCompanyContent :company :subscribers :chats="[]" noMessage v-if="company?.id" class="mb-4" />
    </div>
  </div>
</template>
