<script setup>
import { onMounted, ref, shallowRef } from "vue";
import { UserCard, useGlobalStore, useToastStore, useTranslation } from "@s";
import { useGroupUsers } from "../../Composables/Group/useGroupUsers";

const global = useGlobalStore();
const toast = useToastStore();

const { addUser: add, removeUser: remove } = useGroupUsers();

const nickname = shallowRef("");

const props = defineProps({
  groupCreator: Boolean,
  chat: Object,
});

const participants = ref([]);

onMounted(() => {
  participants.value = Object.values(props.chat?.users || {});
});

const addUser = async () => {
  const user = await add(props.chat, nickname.value);
  if (!user) return;

  participants.value.unshift(user);

  nickname.value = "";
};

const removeUser = async (user) => {
  await remove(props.chat, user);

  participants.value = participants.value.filter((u) => u.id !== user.id);
};

const copyLink = () => {
  navigator.clipboard.writeText(window.location.href);
  toast.success(useTranslation.__("Link copied"));
};
</script>

<template>
  <div class="w-full text-center">
    <div class="!text-accent dark:!text-dark-accent my-4">
      <div class="h2 flex justify-center">
        {{ __("Group participants") }} -
        {{ participants?.length }}
      </div>
      <div class="mt-2">
        <span v-if="chat.is_public">
          {{ __("Public") }}
        </span>
        <span v-else>
          {{ __("Private") }}
        </span>
      </div>
    </div>

    <div v-if="groupCreator">
      <div class="flex items-center gap-2">
        <x-input v-model="nickname" class="w-full" :placeholder="__('Nickname')" />
        <div>
          <x-button @click="addUser">
            {{ __("Add") }}
          </x-button>
        </div>
      </div>
    </div>
    <x-button v-if="chat.is_public" class="my-2" @click="copyLink">{{ __("copy link") }}</x-button>

    <div>
      <div v-for="user in participants" :key="user.id" class="my-3">
        <UserCard :user>
          <template #actions>
            <div v-if="groupCreator && user.id !== global.user.id">
              <x-button @click="removeUser(user)">
                {{ __("Delete") }}
              </x-button>
            </div>
          </template>
        </UserCard>
      </div>
    </div>
  </div>
</template>
