<script setup>
import { useForm } from "@inertiajs/vue3";
import { useTemplateRef } from "vue";
import { inject } from "vue";

const route = inject("route");

const modal = useTemplateRef("modal");

const groupForm = useForm({
  name: null,
  is_public: false,
});

const createGroup = () => {
  groupForm.post(route("messenger.groups.create"));
};
</script>
<template>
  <x-button @click="modal.modalOpen = true">
    {{ __("Create group") }}
  </x-button>
  <Modal :title="__('Create group')" ref="modal">
    <x-form @submit="createGroup" :form="groupForm" type="create" noClass>
      <x-input :label="__('Title')" v-model="groupForm.name" />
      <div class="mt-4">
        <x-checkbox v-model="groupForm.is_public" :label="__('Public')" />
      </div>
    </x-form>
  </Modal>
</template>
