<script setup>
import { computed } from "vue";
import { useGlobalStore } from "@s";
import { router } from "@inertiajs/vue3";
import { inject } from "vue";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PhImage, PhLock } from "@phosphor-icons/vue";
import { useChatHelpers } from "../../Composables/Chat/useChatHelpers";

const route = inject("route");

const { chatName } = useChatHelpers();

const props = defineProps({
  index: Number,
  chat: Object,
});

const global = useGlobalStore();

const emit = defineEmits(["selectChat", "destroyChat"]);

const isNotes = computed(() => props.chat.type === "notes");
const hasLastMessage = computed(() => !!props.chat.last_message?.id);
const lastMessageSenderYou = computed(
  () => props.chat.last_message.user.id === global.user.id,
);

const selectChat = () => {
  router.get(route("messenger.chat", { type: "id", key: props.chat.id }));
};

</script>

<template>
  <x-button @click="selectChat"
  class="w-full !rounded-none !px-2">
    <!-- Avatar -->
    <div class="relative shrink-0">
      <x-model-media :image="chat.avatar" :group="chat.type === 'group'" :notes="isNotes"
        class="!h-12 !w-12 rounded-full" />
      <!-- :TODO  Online indicator -->
      <div v-if="chat.isOnline"
        class="absolute right-0 bottom-0 h-3 w-3 rounded-full border-2 border-white bg-green-500 dark:border-gray-900">
      </div>
    </div>

    <!-- Chat content -->
    <div class="flex min-w-0 flex-1 flex-col justify-center">
      <!-- Chat name and timestamp row -->
      <div class="mb-1 flex items-center justify-between">
        <h3 class="truncate pr-2 text-base font-medium text-gray-900 dark:text-white">
          {{ chatName(chat) }}
        </h3>
        <time v-if="hasLastMessage" class="shrink-0 text-xs text-gray-500 dark:text-gray-400">
          {{ chat.last_message.created_at }}
        </time>
      </div>

      <!-- Last message and unread count row -->
      <div class="flex items-center justify-between">
        <div class="min-w-0 flex-1">
          <div class="truncate text-sm justify-start text-gray-600 dark:text-gray-300">
            <span v-if="hasLastMessage" class="flex gap-1">
              <!-- Message status indicators -->
              <span v-if="lastMessageSenderYou && !isNotes" class="inline-flex items-center">
                <!-- Sent/Delivered/Read status icons -->
                <div v-if="chat.last_message.status === 'seen'" class="text-secondary">
                  <PhChecks class="h-4 w-4" />
                </div>
                <div v-else-if="chat.last_message.status === 'unread'" class="text-accent dark:text-dark-accent">
                  <PhCheck class="h-4 w-3" />
                </div>
              </span>

              <!-- Sender name for group chats -->
              <span v-if="!lastMessageSenderYou && chat.type === 'group'"
                class="font-medium text-blue-600 dark:text-blue-400">
                {{ chat.last_message.user.display_name }}:
              </span>

              <!-- Message content -->
              <span v-if="chat.last_message.media?.length" class="inline-flex items-center gap-1">
                <PhImage class="h-3 w-3" />
                {{ chat.last_message.media?.length > 1 ? __("photos") : __("photo") }}
              </span>

              <span v-else-if="chat.last_message.type === 'text' || chat.last_message.type === null">
                {{ chat.last_message?.content?.slice(0, 35) }}
                <span v-if="chat.last_message?.content?.length > 35">...</span>
              </span>
              <span v-else-if="chat.last_message.type === 'notification'">
                {{ __(chat.last_message.content) }}
              </span>
            </span>

            <span v-else class="justify-start truncate flex ">
              <span>
                {{ __("No messages") }}
              </span>
            </span>

          </div>
        </div>
        <!-- {{ chat.last_message }} -->
        <!-- Unread badge and mute indicator -->
        <div class="ml-2 flex items-center space-x-1">
          <!-- Mute indicator -->
          <span v-if="chat.blocked_by" class="text-gray-400">
            <PhLock class="h-4 w-4" />
          </span>

          <!-- Unread count -->
          <div v-if="chat.new_messages"
            class="flex h-5 min-w-[20px] items-center justify-center rounded-full bg-blue-500 px-1.5 text-xs font-medium text-white">
            {{ chat.new_messages }}
          </div>

          <!-- Muted unread indicator -->
          <div v-else-if="chat.new_messages && chat.is_muted" class="h-2 w-2 rounded-full bg-gray-400"></div>
        </div>
      </div>
    </div>

    <!-- Chat menu -->

    <!-- Pinned indicator -->
    <div v-if="chat.is_pinned" class="absolute top-2 left-2">
      <svg class="h-3 w-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path
          d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V11a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM15 7a1 1 0 011 1v7.268a2 2 0 010 3.464V20a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V8a1 1 0 011-1z" />
      </svg>
    </div>
  </x-button>
</template>
