<script setup>
import { PhList, PhSmiley } from "@phosphor-icons/vue";
import EmojiPicker from "./Assets/EmojiPicker.vue";
import { useChatStore } from "../../Stores/useChatStore";

const chatStore = useChatStore();

const props = defineProps({
  chat: Object,
});

const emits = defineEmits(["addEmoji"]);

const onSelectEmoji = (emoji) => {
  emits("addEmoji", emoji);
};


</script>
<template>
  <div class="flex flex-row gap-2">
    <!-- 
    <x-button intent="rounded" class="!p-2" v-if="props.chat.company?.bot"
      @click="chatStore.isBotOpen = !chatStore.isBotOpen">
      <PhList :size="20" />
    </x-button> -->
    <slot />

    <dropdown position="top-right">
      <template #button>
        <x-button intent="rounded" class="!p-2">
          <PhSmiley :size="20" />
        </x-button>
      </template>

      <EmojiPicker @addEmoji="onSelectEmoji" />
    </dropdown>
  </div>
</template>
