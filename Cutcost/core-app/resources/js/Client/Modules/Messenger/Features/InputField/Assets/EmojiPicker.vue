<script setup>
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import { ref } from "vue";

const emits = defineEmits(["addEmoji"]);

const onSelectEmoji = (emoji) => {
  emits("addEmoji", emoji);
};
const theme = ref(localStorage.getItem("theme"));
</script>

<template>
  <EmojiPicker :native="true" @select="onSelectEmoji" hide-search :theme="theme === 'dark' ? 'dark' : 'light'" />
</template>
