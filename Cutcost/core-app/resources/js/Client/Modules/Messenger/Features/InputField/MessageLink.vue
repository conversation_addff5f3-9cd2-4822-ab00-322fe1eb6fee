<script setup>
import { PhX } from "@phosphor-icons/vue";

const emit = defineEmits(["exit"]);

const props = defineProps({
  content: String,
  type: {
    type: String,
    required: false,
  },
});

const exit = () => {
  emit("exit", props.type);
};
</script>

<template>
  <div
    class="text-text dark:text-dark-text mb-2 flex items-center justify-between rounded-lg border-l-4 border-blue-500 bg-blue-50 p-2 dark:bg-blue-900/20">
    <div class="flex min-w-0 flex-1 flex-col">
      <div class="mb-1 text-xs font-medium text-blue-600 dark:text-blue-400" v-if="type">
        {{ __(type) }}
      </div>
      <div class="truncate text-sm text-gray-700 dark:text-gray-300">
        {{ content?.length > 50 ? content?.slice(0, 50) + "..." : content }}
      </div>
    </div>
    <button v-if="type" @click="exit"
      class="text-error ml-2 flex-shrink-0 rounded-full p-1 transition-colors hover:bg-red-100 dark:hover:bg-red-900/30">
      <PhX :size="20" />
    </button>
  </div>
</template>
