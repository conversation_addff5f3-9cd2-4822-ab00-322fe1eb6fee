<script setup>
import { useGlobalStore } from "@s";
import { useChatStore } from "../../Stores/useChatStore";
import { useChatService } from "../../Services/Chat/useChatService";
import { Report } from "@s";
import { PhFlag } from "@phosphor-icons/vue";

const props = defineProps({
  chat: Object,
});

const global = useGlobalStore();
const chatStore = useChatStore();

const { handleUnBlockChat } = useChatService();

const unblock = async () => {
  await handleUnBlockChat(props.chat);
};

const reportSubject = {
  chat: props.chat,
  friend: props.chat.friend,
};
</script>
<script setup></script>
<template>
  <div class="mx-auto my-8 flex flex-col gap-2 select-none" v-if="chatStore.blockedBy">
    <h1 class="h1">{{ __("Chat blocked") }}</h1>
    <Report :subject="reportSubject" :id="chat.id + '_report_chat_blocked'">
      <x-button class="!text-error w-full flex-col">
        <PhFlag :size="32" class="fill-error" />
        {{ __("Report") }}
      </x-button>
    </Report>
    <x-button v-if="chatStore.blockedBy === global.user.id" @click="unblock()">
      {{ __("Unblock") }}
    </x-button>
  </div>
</template>
