<script setup>
  import { formatChatDate } from "../../Composables/Chat/FormatDate";
  import { useGlobalStore } from "@s";

  const { format } = formatChatDate();

  const global = useGlobalStore();

  defineProps({
    messages: Object,
    message: Object,
    index: Number,
  });
</script>
<template>
  <div
    v-if="messages[index]?.date != messages[index - 1]?.date"
    class="flex w-full"
  >
    <strong
      class="bg-surface text-text dark:bg-dark-surface dark:text-dark-primary mx-auto my-2 rounded-2xl p-2"
      >{{ format(message.date, global.currentLocale) }}</strong
    >
  </div>
</template>
