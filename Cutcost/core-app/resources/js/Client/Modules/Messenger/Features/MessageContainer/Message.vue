<script setup>
import MessageMenu from "./MessageMenu.vue";
import { inject, computed } from "vue";
import { useTranslation, Content } from "@s";
import MessageLink from "../InputField/MessageLink.vue";
import Notification from "../../Entities/Message/Notification.vue";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON> } from "@phosphor-icons/vue";

const route = inject("route");

const props = defineProps({
  chat: Object,
  message: Object,
  index: Number,
  friend: Boolean,
});

const shouldShowMenu = computed(
  () => !props.chat?.company && !props.chat.blocked_by,
);

const isNotesChat = computed(() => props.chat.type === "notes");

const messageBubbleClasses = computed(() => [
  "rounded-2xl px-3  shadow-md relative",
  props.friend
    ? "bg-primary text-bg dark:bg-dark-border dark:text-dark-text  rounded-bl-sm"
    : "bg-border dark:bg-dark-surface dark:text-dark-text rounded-br-sm",
  props.chat.type === "group" ? "  pb-2 pb-1" : " py-4",
]);

const messageStatus = computed(() => {
  if (isNotesChat.value) return "";
  const status =
    props.message.status === "unread" || !props.message.status ? "/" : "//";
  return ` ${status} `;
});

const messageIsEdited = computed(() => {
  const edited = props.message.edited
    ? useTranslation.__("edited")
    : "";
  return edited;
});

const profileUrl = computed(() =>
  route("profile", { user: props.message.user.nickname }),
);

</script>

<template>
  <div v-if="message.type !== 'notification'" class="group relative flex" :class="{ 'flex-row-reverse': !friend }">
    <!-- Avatar -->
    <div class="flex-shrink-0" :class="friend ? 'mr-2' : 'ml-2'" v-if="chat.type === 'group'">
      <Link :href="profileUrl">
      <x-model-media :name="message.user.display_name" :image="message.user.avatar" class="!h-10 !w-10" />
      </Link>
    </div>
    <div class="relative max-w-[75%] min-w-23 flex-1" :class="{ 'min-w-44': messageIsEdited }">
      <div v-bind="$attrs" :class="messageBubbleClasses" @click="handleMessageClick">
        <div class="flex w-full flex-col">
          <strong v-if="!isNotesChat && chat.type === 'group'" class="text-secondary flex py-1">
            {{ message.user.display_name }}
          </strong>
          <div class="break-before-column !font-serif break-words whitespace-pre-line">
            <MessageLink :content="message.replied_content" v-if="message.replied_content" />
            <div v-if="message?.media.length" class="mb-2">
              <x-images :media="message?.media" />
            </div>
            <Content :content="message.content" />
          </div>
        </div>
      </div>
      <!-- Message Info -->
      <div :class=" friend ? 'mr-2' : 'ml-2'" class=" text-text dark:text-dark-text mt-1 flex justify-end gap-1 text-xs
        select-none">
        <div v-if="messageIsEdited">
          {{ messageIsEdited }}
        </div>
        {{ message.created_at }}
        <span :class="messageStatus.trim() === '/'
          ? '!text-text dark:!text-dark-text'
          : '!text-secondary'
          ">
          <template v-if="message.status === 'seen'">
            <PhChecks class="h-4 w-4" />
          </template>
          <template v-else>
            <PhCheck class="h-4 w-3" />
          </template>
        </span>
        <MessageMenu v-if="shouldShowMenu" :chat="chat" :message="message" 
          :friend="friend"  class="z-0"
          :class="friend ? 'ml-8 left-0' : 'right-0'" />
      </div>
    </div>

  </div>
  <div v-else class="mx-auto">
    <Notification :message="message" />
  </div>
</template>
