<script setup>
import { PhArrowBend<PERSON>p<PERSON><PERSON><PERSON>, PhDotsThreeVertical, PhPencil, PhTrash } from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";
import { useInputStore } from "../../Stores/useInputStore";
import { ref } from "vue";
import { useMessageMenu } from "../../Composables/Chat/useMessageMenu";

const global = useGlobalStore();

// for reply and edit and input reaction
const inputStore = useInputStore();
const askDestroy = ref(false);

const { destroyMessage } = useMessageMenu();


const props = defineProps({
  chat: Object,
  message: Object,
  friend: <PERSON><PERSON><PERSON>,
});


const destroy = async () => {
  // update ui and send request to the server
  await destroyMessage(props.chat, props.message);
};

const reply = (message) => {
  // you cant reply and edit at the same time
  if (inputStore.editMessage?.content) return;
  inputStore.replyMessage = { ...message };

  if (message.media?.length) {
    inputStore.interactImages = message.media;

    if (!message.content) {
      inputStore.replyMessage.content = '🌄';
    }

  }
};

const edit = (message) => {
  // you cant reply and edit at the same time
  if (inputStore.replyMessage?.content) return;
  inputStore.editMessage = { ...message };

  if (message.media?.length) {
    inputStore.interactImages = message.media;
  }

};

</script>

<template>

  <div class="sticky  ">
    <x-confirmation :message="__('Are you sure you want to delete message?')
      " :for="chat.id + '_delete_message_' + message.id" @confirm="destroy" @cancel="askDestroy = false"
      v-if="askDestroy" />
    <Dropdown>
      <template #button>
        <div class="hover:bg p-1">
          <PhDotsThreeVertical :size="18" weight="bold" />
        </div>
      </template>
      <DropdownItem @click="reply(message)">
        <PhArrowBendUpLeft :size="16" />
        {{ __("Reply") }}
      </DropdownItem>
      <template v-if="message.user.id === global.user.id">
        <DropdownItem @click="edit(message)">
          <PhPencil :size="16" weight="bold" />
          {{ __("Edit") }}
        </DropdownItem>
        <DropdownItem @click="askDestroy = true">
          <PhTrash :size="16" weight="bold" />
          {{ __("Delete") }}
        </DropdownItem>
      </template>
    </Dropdown>
  </div>
</template>
