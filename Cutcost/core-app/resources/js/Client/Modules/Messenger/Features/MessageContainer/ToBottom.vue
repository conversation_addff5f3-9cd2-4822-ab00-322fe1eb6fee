<script setup>
import { ref, onMounted, watch } from "vue";
import { useMessagesContainer } from "../../Composables/Chat/useMessagesContainer";
import { PhArrowDown } from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";

// Reactive object to hold dynamic style for button position
const buttonPositionStyle = ref({});
const global = useGlobalStore();

// Function to scroll to the bottom of the chat
const { toBottom } = useMessagesContainer();


</script>

<template>
    <!-- Fixed position button, dynamically positioned inside the chat container -->
    <div v-motion-slide-visible-bottom
        class="sm:sticky fixed sm:top-0 max-sm:ml-[76vw] sm:left-[100vw] z-10 w-min cursor-pointer p-4 select-none"
        :style="buttonPositionStyle" @click="toBottom(true)">
        <div
            class="bg-bg dark:bg-bg-dark active:text-secondary text-text dark:text-dark-text shadow rounded-full p-4   cursor-pointer ">
            <PhArrowDown :size="15" />
        </div>
    </div>
</template>
