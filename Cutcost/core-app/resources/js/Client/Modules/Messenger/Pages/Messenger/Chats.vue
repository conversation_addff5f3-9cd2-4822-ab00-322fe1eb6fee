<script setup>
import ChatsList from "../../Widgets/ChatsList.vue";
import ChatsFilter from "../../Widgets/ChatsFilter.vue";
import { onMounted, onUnmounted } from "vue";
import { useHeaderStore } from "@m/Fixed";
// sockets initialization inside useChatsService
// cause we need to reload sockets when chat mode changes

const header = useHeaderStore();

defineProps({
  companies: Object,
});  

onMounted(() => {

  header.logo = 'CUTTALK';
  
});
onUnmounted(() => {
  header.clear();
});
</script>

<template>

  <Head :title="__('Chats') + ' -'" />

  <div class="card-position relative flex gap-2 max-md:flex-col-reverse">
    <div class="post-position max-md:w-full">
      <ChatsList :companies="companies" />
    </div>
    <div class="w-1/3 max-md:w-full">
      <ChatsFilter :companies="companies" />
    </div>
  </div>
</template>
