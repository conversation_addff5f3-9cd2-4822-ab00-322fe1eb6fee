<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, watch } from 'vue'
import MessagesContainer from '../../Widgets/MessagesContainer.vue'
import InputField from '../../Widgets/InputField.vue'
import ChatHeader from '../../Widgets/ChatHeader.vue'
import ChatAside from '../../Widgets/ChatAside.vue'
// import RenderBot from '../../Widgets/RenderBot.vue'

import { useGlobalStore } from '@s'
import { useMessengerSockets } from '../../Composables/Sockets/useMessengerSockets'
import { useChatStore } from '../../Stores/useChatStore'

const global = useGlobalStore()
const chatStore = useChatStore()

const props = defineProps({
  chat: Object,
  companies: Object,
})

const { initSockets } = useMessengerSockets('chat')

const headerRef = ref(null)
const inputRef = ref(null)
const messagesRef = ref(null)
const containerHeight = ref(window.innerHeight)

function getViewportHeight() {
  return window.visualViewport ? window.visualViewport.height : window.innerHeight
}

const updateLayout = () => {
  const totalHeight = getViewportHeight();
  const headerHeight = headerRef.value?.offsetHeight || 56
  const inputHeight = inputRef.value?.offsetHeight || 0
  const messagesHeight = totalHeight - headerHeight - inputHeight

  if (messagesRef.value) {
    messagesRef.value.style.height = `${messagesHeight}px`
  }

  containerHeight.value = totalHeight;
}

// Автоскролл вниз
const scrollToBottom = () => {
  if (messagesRef.value) {
    messagesRef.value.scrollTop = messagesRef.value.scrollHeight
  }
}

onMounted(() => {

  initSockets([props.chat.id])
  chatStore.blockedBy = props.chat.blocked_by

  nextTick(() => {
    updateLayout()
    scrollToBottom()

    window.addEventListener('resize', updateLayout)
    window.addEventListener('orientationchange', updateLayout)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', () => {
        updateLayout()
        scrollToBottom()
      })
      window.visualViewport.addEventListener('scroll', () => {
        updateLayout()
        scrollToBottom()
      })
    }
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateLayout)
  window.removeEventListener('orientationchange', updateLayout)
  if (window.visualViewport) {
    window.visualViewport.removeEventListener('resize', updateLayout)
    window.visualViewport.removeEventListener('scroll', updateLayout)
  }
})

watch(() => props.chat.messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<template>

  <Head>
    <title>{{ __(chat.name) }} </title>
  </Head>
  <div class="flex flex-col w-full" :class="global.isMobile ? '' : 'mt-7 !h-[86vh] mx-4'"
    :style="{ height: containerHeight + 'px' }">
    <!-- Шапка -->
    <div ref="headerRef"
      class="flex-shrink-0 h-14 bg-surface dark:bg-dark-surface shadow-lg px-4 flex items-center justify-between">
      <ChatHeader v-if="chat" :chat class="flex items-center justify-between w-full h-full">
        <ChatAside :chat :companies v-if="chat.type === 'group' || chat.type === 'chat'" />
      </ChatHeader>
    </div>

    <!-- Сообщения -->
    <div ref="messagesRef" class="overflow-y-auto px-3  " style="flex-grow: 1;">
      <MessagesContainer :chat />
    </div>

    <!-- Инпут и бот -->
    <div ref="inputRef"
      class="flex-shrink-0 bg-surface dark:bg-dark-surface border-t border-border dark:border-dark-border px-3 pt-1 pb-[calc(env(safe-area-inset-bottom)+6px)] shadow-lg flex flex-col">
      <!-- <RenderBot :chat="chat" v-if="chatStore.isBotOpen && chat.company?.bot" class="mb-2" /> -->
      <InputField :chat="chat" class="w-full rounded-2xl resize-none" />
    </div>

  </div>
</template>
