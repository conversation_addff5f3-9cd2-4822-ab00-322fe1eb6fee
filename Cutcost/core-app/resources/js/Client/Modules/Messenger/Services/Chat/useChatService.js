import { useGlobalStore } from "@s";
import { useChatApi } from "../../Api/useChatApi";
import { useChatsApi } from "../../Api/useChatsApi";
import { useChat } from "../../Composables/Chat/useChat";
import { useChatStore } from "../../Stores/useChatStore";
import { router } from "@inertiajs/vue3";
import { inject } from "vue";

export const useChatService = () => {
  // todo create updated and api calls

  const route = inject("route");
  const api = useChatApi();
  const { quitChat } = useChatsApi();
  const { blockFriend, unBlockFriend } = useChat();
  const chatStore = useChatStore();
  const global = useGlobalStore();

  const handleBlockChat = async (chat) => {
    // validation
    if (chatStore.blockedBy === global.user.id) {
      return;
    }

    // if chat already blocked
    if (chatStore.blockedBy) {
      return;
    }

    await api.blockFriend(chat);
    blockFriend(global.user.id);
  };

  const handleUnBlockChat = async (chat) => {
    // validation
    if (chatStore.blockedBy !== global.user.id) {
      return;
    }

    await api.unBlockFriend(chat);
    unBlockFriend();
  };

  const quit = async (chat) => {
    await quitChat(chat);

    router.get(route("messenger.index"));
  };

  return {
    handleBlockChat,
    handleUnBlockChat,
    quit,
  };
};
