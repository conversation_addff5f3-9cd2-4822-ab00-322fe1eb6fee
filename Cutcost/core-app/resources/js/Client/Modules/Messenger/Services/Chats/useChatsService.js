import { useGlobalStore } from "@s";
import { useChatsApi } from "../../Api/useChatsApi";
import { useChats } from "../../Composables/Chats/useChats";
import { useChatsStore } from "../../Stores/useChatsStore";
import { useMessengerSockets } from "../../Composables/Sockets/useMessengerSockets";

export const useChatsService = () => {
  // for chat
  const chatsStore = useChatsStore();
  // for user
  const global = useGlobalStore();
  const { sortChats } = useChats();
  const { getChats } = useChatsApi();
  const { initSockets } = useMessengerSockets("chats");

  /**
   * loads chats and sorts them
   *
   * @async
   * @param {string} [mode=null]
   * @returns {string} mode of chats
   */
  const loadChats = async (mode = null) => {
    // check if mode is same as current
    if (mode) {
      const currentMode = localStorage.getItem("messenger.chats.mode");

      // if same, do nothing
      // this prevents unnecessary api calls
      if (currentMode === mode) return currentMode;
    }

    // load chats
    if (!mode)
      mode = localStorage.getItem("messenger.chats.mode") ?? "all_chats";

    chatsStore.chats = await getChats(global.user, mode);
    chatsStore.chats = sortChats(chatsStore.chats);

    // save mode to local storage for future use
    localStorage.setItem("messenger.chats.mode", mode);

    reloadSockets();

    return mode;
  };

  const reloadSockets = () => {
    // when chats reloads we need to reload sockets
    // also we need to mannualy remove old sockets between requests
    initSockets(chatsStore.chats.map((chat) => chat.id));
  };

  return {
    loadChats,
  };
};
