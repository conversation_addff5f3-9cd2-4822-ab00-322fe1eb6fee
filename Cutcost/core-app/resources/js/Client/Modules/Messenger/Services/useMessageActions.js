import { useMessageApi } from "../Api/useMessageApi";

// unite api and composable post to server and update ui
export const useMessageActions = () => {
  // API operations
  const api = useMessageApi();

  // UI chat operations

  /**
   * from inputField, (SEND message) request to the server vie api and update ui
   * @async
   * @param {Object} chat
   * @param {Object} request
   * @returns {void}
   */
  const handleSendMessage = async (chat, request) => {
    await api.sendMessage(chat, request);
    // messages.addMessage(message);
  };

  /**
   * from inputField, (UPDATE message) request to the server vie api and update ui
   * @async
   * @param {Object} chat
   * @param {Object} message
   * @param {Object} request
   * @returns {void}
   */
  const handleUpdateMessage = async (chat, message, request) => {
    await api.updateMessage(chat, message, request);
    // messages.updateMessage(updatedMessage);
  };

  /**
   * from messageMenu, (DELETE message) request to the server vie api and update ui
   * @async
   * @param {Object} chat
   * @param {Object} message
   * @returns {void}
   */
  const handleDestroyMessage = async (chat, message) => {
    await api.deleteMessage(chat, message);
    // messages.removeMessage(message.id);
  };

  return {
    handleSendMessage,
    handleUpdateMessage,
    handleDestroyMessage,
  };
};
