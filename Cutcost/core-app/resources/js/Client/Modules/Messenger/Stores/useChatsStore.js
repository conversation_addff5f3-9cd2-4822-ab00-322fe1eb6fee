import { defineStore } from "pinia";
import { computed, ref } from "vue";

export const useChatsStore = defineStore("chats", () => {
  const chats = ref([]);

  // search filter
  const search = ref("");
  const onlyGroups = ref(false);
  const onlyUnreads = ref(false);

  const filterChats = computed(() => {
    let filtered = chats.value;

    // Apply search filter
    if (search.value) {
      filtered = filtered.filter((chat) =>
        chat.name.toLowerCase().includes(search.value.toLowerCase()),
      );
    }

    // Apply groups filter
    if (onlyGroups.value) {
      filtered = filtered.filter((chat) => chat.type === "group");
    }

    // Apply unreads filter
    if (onlyUnreads.value) {
      filtered = filtered.filter((chat) => chat.new_messages);
    }

    return filtered;
  });

  return {
    chats,
    search,
    filterChats,
    onlyGroups,
    onlyUnreads,
  };
});
