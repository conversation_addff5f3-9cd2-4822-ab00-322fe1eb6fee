import { defineStore } from "pinia";
import { computed, ref, watch } from "vue";

export const useInputStore = defineStore("input", () => {
  const textField = ref(null);
  const editMessage = ref(null);
  const replyMessage = ref(null);

  // images that used in reply or edit message
  const interactImages = ref([]);

  const previewBlockHeight = ref(0);

  const content = ref("");

  const isEdit = computed(() => {
    return editMessage.value !== null;
  });

  const exitFromReply = () => {
    replyMessage.value = null;
    interactImages.value = [];
  };

  const exitFromEdit = () => {
    editMessage.value = null;
    interactImages.value = [];
  };

  watch(
    () => editMessage.value,
    () => {
      content.value = editMessage.value?.content ?? "";
    },
  );

  return {
    textField,
    editMessage,
    replyMessage,
    interactImages,
    previewBlockHeight,
    content,
    isEdit,
    exitFromReply,
    exitFromEdit,
  };
});
