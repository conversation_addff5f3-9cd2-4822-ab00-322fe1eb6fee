<script setup>
import { computed, shallowRef, useTemplateRef } from "vue";
import { PhList } from "@phosphor-icons/vue";
import ForChat from "../Features/ChatSettings/ForChat.vue";
import ForGroup from "../Features/ChatSettings/ForGroup.vue";
import { useGlobalStore, useTranslation, Report } from "@s";
import { useGroupApi } from "../Api/useGroupApi";
import EditName from "../Features/ChatSettings/EditName.vue";
import { useChatService } from "../Services/Chat/useChatService";
import ForCompany from "../Features/ChatSettings/ForCompany.vue";
import ChatBlock from "../Entities/Chat/ChatBlock.vue";

const global = useGlobalStore();
const { updateGroupAvatar } = useGroupApi();
const { quit } = useChatService();

const props = defineProps({
  chat: Object,
  companies: Object,
});

const quitDialog = shallowRef(false);
const modal = useTemplateRef("modal");

const uploadAvatar = async (avatar) => {
  const formData = new FormData();
  formData.append("avatar", avatar);
  await updateGroupAvatar(props.chat, formData);
};

const groupCreator = computed(() => {
  return (
    global.user.id === props.chat?.created_by && props.chat.type === "group"
  );
});
const isGroup = computed(() => props.chat.type === "group");
const isNotes = computed(() => props.chat.type === "notes");
const isChatWithCompany = computed(() => !!props.chat?.company?.id);

const quitChat = async () => {
  await quit(props.chat);
  modal.modalOpen = false;
};


const message = computed(() => {
  if (groupCreator.value) return useTranslation.__("Are you sure you want to delete group?");
  if (isGroup.value) return useTranslation.__("Are you sure you want to quit group?");
  return useTranslation.__("Are you sure you want to quit chat?");
});


</script>
<template>
  <x-confirmation for="chat_quit" :message="message" v-if="quitDialog" @confirm="quitChat"
    @cancel="quitDialog = false" />

  <Modal ref="modal" :title="__('Chat info')">
    <div class="mb-4 flex flex-col items-center space-y-2">
      <x-model-media class="!h-26 !w-26" :image="chat.avatar" :upload="groupCreator && chat.type === 'group'"
        @change-avatar="uploadAvatar" />
      <EditName :chat="chat" :groupCreator />
    </div>

    <ForChat @closeModal="modal.modalOpen = false" :chat="chat" v-if="!(isNotes || isGroup) && !isChatWithCompany" />
    <ForGroup :groupCreator v-if="isGroup" :chat="chat" />
    <ForCompany v-if="isChatWithCompany" :chat="chat" :companies="companies" />
  </Modal>

  <Dropdown position="bottom-right">
    <template #button>
      <x-button intent="rounded" class="!p-2">
        <PhList :size="22" />
      </x-button>
    </template>
    <DropdownItem @click="modal.modalOpen = true">
      {{ __("Chat info") }}
    </DropdownItem>
    <Report model-type="chat" :model-id="chat.id" dropdown>
      {{ __("Report") }}
    </Report>
    <ChatBlock :chat="chat" />

    <DropdownItem v-if="!isNotes" @click="quitDialog = true" class="!text-error">
      {{ __('Quit') }}
    </DropdownItem>
  </Dropdown>

</template>
