<script setup>
import { PhArrowLeft } from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";
import { inject } from "vue";

const route = inject("route");

defineProps({
  chat: Object,
});

const global = useGlobalStore();
</script>
<template>
  <div v-bind="$attrs">
    <x-button as="Link" intent="rounded" :href="route('messenger.index')" class="!p-2">
      <PhArrowLeft :size="22" />
    </x-button>

    <!-- TODO change to chatName() from helpers-->
    <div class="text-text dark:text-dark-text flex text-xl font-bold" v-if="chat.type != 'notes'">
      <strong :class="chat.name.length > 25 && global.isMobile ? 'mask-text' : ''">
        {{ global.isMobile ? chat.name.slice(0, 25) : chat.name }}
      </strong>
    </div>
    <h2 v-else class="text-text dark:text-dark-text text-xl font-bold">
      {{ __("Notes") }}
    </h2>
    <div class="inline-flex gap-2">
      <slot />
    </div>
  </div>
</template>
