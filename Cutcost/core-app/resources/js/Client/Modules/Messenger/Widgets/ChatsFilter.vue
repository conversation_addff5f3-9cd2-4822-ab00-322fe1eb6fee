<script setup>
import { computed, ref, useTemplateRef, watch } from "vue";
import { useGlobalStore } from "@s";
import { useChatsStore } from "../Stores/useChatsStore";
import { useChatsService } from "../Services/Chats/useChatsService";
import { storeToRefs } from "pinia";
import CreateGroup from "../Features/Chats/CreateGroup.vue";

const { loadChats } = useChatsService();

const global = useGlobalStore();
const chatsStore = useChatsStore();

const props = defineProps({
  companies: Object,
});


const { search, onlyGroups, onlyUnreads } = storeToRefs(chatsStore);

const mode = ref(localStorage.getItem("messenger.chats.mode") ?? "all_chats");

const setMode = async (newMode) => {
  mode.value = await loadChats(newMode);
};

const filtersModal = useTemplateRef("filtersModal");

const handleClick = () => {
  if (global.isMobile) {
    filtersModal.value.modalOpen = true;
  }
};

const modes = computed(() => {
  return [
    { id: "all_chats", name: "Personal chats" },
    ...props.companies.map((c) => ({
      id: "company_chats_" + c.id,
      name: c.name + " chats",
    })),
  ];
});

watch(mode, (newMode) => {
  setMode(newMode);
});
</script>
<template>
  <div>
    <div class="mt-2 space-y-1">
      <x-select v-model="mode" :data="modes" class="!rounded-3xl" required v-if="companies?.length"/>

      <x-input :placeholder="__('Search chats')" @click="handleClick" v-model="search" type="search"
        class="!rounded-3xl" />
    </div>

    <component :is="global.isMobile ? 'Modal' : 'div'" ref="filtersModal" class="mt-4">
      <x-hr class="my-4" />
      <div class="w-full space-y-2">
        <div class="flex flex-col justify-start gap-2">
          <x-checkbox v-model="onlyGroups" :label="__('Groups')" />
          <x-checkbox v-model="onlyUnreads" :label="__('Unread')" />
        </div>

      </div>

      <x-hr class="my-4" />
      <CreateGroup />
    </component>
  </div>
</template>
