<script setup>
import { useHelpers } from "@s";
import Chat from "../Features/ChatsList/Chat.vue";
import { inject, onMounted } from "vue";
import { useChatsStore } from "../Stores/useChatsStore";
import { useChatsService } from "../Services/Chats/useChatsService";

const chatsStore = useChatsStore();

const { loadChats } = useChatsService();
const { debounce } = useHelpers();

const route = inject("route");

const props = defineProps({
  companies: Object,
});

onMounted(async () => {

  // check if mode is company chat and company exists
  // if not, load personal chats
  const mode = localStorage.getItem("messenger.chats.mode") ?? "all_chats";
  if (mode !== "all_chats") {
    console.log(mode);
    const companiesIds = props.companies?.map((c) => c?.id);
    if (!companiesIds.includes(parseInt(mode.split("_")[2]))) {
      await loadChats("all_chats");
    }
  } else {
    await loadChats();
  }

});

const destroyChat = debounce((chatId, index) => {
  axios
    .delete(route("messenger.chat.destroy", { chat: chatId }))
    .then((res) => {
      props.chats.splice(index, 1);
    })
    .catch(() => { });
}, 200);
</script>
<template>
  <div class="overflow-x-hidden overflow-y-auto select-none" v-if="Object.values(chatsStore.filterChats).length > 0">
    <div >
      <Chat @destroyChat="destroyChat" v-for="(chat, index) in chatsStore.filterChats" :chat :index :key="chat.id" />
    </div>
  </div>
  <div v-else>
    <h1 class="h1 mt-20 text-center">
      {{ __("Nothing") }}
    </h1>
  </div>
</template>
