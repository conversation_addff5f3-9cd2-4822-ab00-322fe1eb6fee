<script setup>
import { computed, nextTick, onMounted, onUnmounted, onUpdated, ref, useTemplateRef, watch, watchEffect } from "vue";
import { PhCheck, PhImage, PhPaperPlaneTilt, PhSpinner } from "@phosphor-icons/vue";
import Assets from "../Features/InputField/Assets.vue";
import Textarea from "../Features/InputField/Textarea.vue";
import MessageLink from "../Features/InputField/MessageLink.vue";
import { useInputStore } from "../Stores/useInputStore";
import { useChatInput } from "../Composables/Chat/useChatInput";
import { storeToRefs } from "pinia";
import { useChatStore } from "../Stores/useChatStore";
import { useGlobalStore, useTranslation } from "@s";
import isEqual from "lodash/isEqual";
import { useUploadStore } from '@s';

const inputStore = useInputStore();
const upload = useUploadStore();
const { inputSend, inputUpdate } = useChatInput();

// message content from textarea
const textareaRef = useTemplateRef(null);

const chatStore = useChatStore();
const global = useGlobalStore();

const { content, interactImages } = storeToRefs(inputStore);

const processing = ref(false);

const props = defineProps({
  chat: Object,
});

const send = async () => {
  try {
    processing.value = true;
    inputStore.textField?.focus();

    // collect data for update or create message

    if (!isValidMessage.value) return;

    const request = {
      content: content.value,
      upload: {
        temp_uuid: upload.tempUuid,
        deleted_ids: upload.deletedIds,
      },
      media_changed: !isEqual(inputStore.editMessage?.media, upload.mediaList),
      replied_content:
        inputStore.replyMessage?.content ??
        inputStore.editMessage?.replied_content,
    };

    // to clean input data immediately but save edit state
    const messageToUpdate = { ...inputStore.editMessage };

    clearInputData();
    // update
    if (messageToUpdate?.id) {
      await inputUpdate(props.chat, messageToUpdate, request);
      return;
    } else {
      await inputSend(props.chat, request);
    }
  } catch (err) {
    console.log(err);
  } finally {
    processing.value = false;
  }
};

const clearInputData = () => {
  content.value = "";
  interactImages.value = [];
  upload.mediaList = [];
  upload.tempUuid = null;
  upload.deletedIds = [];
  inputStore.replyMessage = null;
  inputStore.editMessage = null;
  inputStore.previewBlockHeight = 0;

  nextTick(() => {
    textareaRef.value?.resetHeight();
  });
};

watch(interactImages, () => {
  upload.mediaList = interactImages.value;
});

const onAddEmoji = (emoji) => {
  const textarea = inputStore.textField;
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;

  content.value =
    content.value.slice(0, start) + emoji.i + content.value.slice(end);

  nextTick(() => {
    textarea.focus();
    textarea.setSelectionRange(
      start + emoji.i.length,
      start + emoji.i.length,
    );
  });
};

onMounted(() => {
  inputStore.textField?.focus();

  const urlParams = new URLSearchParams(window.location.search);
  const message = urlParams.get('message')?.trim();
  if (message) {
    content.value = message;
  }

  measurePreviewBlock();
});

onUnmounted(() => {
  clearInputData();
});


const placeholder = computed(() => {
  if (inputStore.replyMessage) return useTranslation.__("Reply");
  if (inputStore.editMessage) return useTranslation.__("Edit");
  if (props.chat?.company?.id && props.chat?.company?.id === global.user?.company?.id)
    return useTranslation.__("Write message") + ' ' + useTranslation.__("as") + ' ' + props.chat?.company?.name;
  else
    return useTranslation.__("Write message");
});


// todo move out

const previewBlock = useTemplateRef("previewBlock");
const measurePreviewBlock = () => {
  nextTick(() => {
    if (previewBlock.value) {

      let mobileOffset = 0;

      if (global.isMobile) {
        mobileOffset = 30;
      }

      inputStore.previewBlockHeight = previewBlock.value.offsetHeight + mobileOffset;
      // Или: previewBlock.value.getBoundingClientRect().height — если нужны дробные значения
    }
  })
}

onUpdated(measurePreviewBlock)

const isValidMessage = computed(() => {
  if ((content.value.trim()
    || processing.value
    || props.chat.blocked_by)
    || upload.mediaList.length) {

    if (inputStore.editMessage &&
      inputStore.editMessage?.content !== content.value
      || !isEqual(inputStore.editMessage?.media, upload.mediaList))
      return true;
  }
  return false;
}); 
</script>

<template>
  <div v-bind="$attrs">
    <span ref="previewBlock">
      <MessageLink v-if="inputStore.editMessage" :content="inputStore.editMessage.content"
        @exit="inputStore.exitFromEdit" type="edit" />
      <MessageLink v-if="inputStore.replyMessage" :content="inputStore.replyMessage?.content"
        @exit="inputStore.exitFromReply" type="reply" />
      <MediaPreview separate />
      <div class="flex items-end gap-2">

        <Textarea ref="textareaRef" v-model="content" :disabled="!!chatStore.blockedBy" :placeholder="placeholder"
          @send="send" />

        <Assets v-if="!chatStore.blockedBy" @addEmoji="onAddEmoji" :chat>
          <x-upload :max="6" separate>
            <x-button intent="rounded" class="!p-2">
              <PhImage :size="20" />
            </x-button>
          </x-upload>
        </Assets>
        <div>
          <x-button intent="rounded" class="!p-2" @click="send" :disabled="!isValidMessage">
            <span v-if="!processing">
              <span v-if="!inputStore.isEdit">
                <PhPaperPlaneTilt :size="20" />
              </span>
              <span v-if="inputStore.isEdit">
                <PhCheck :size="20" />
              </span>
            </span>
            <span v-if="processing">
              <PhSpinner :size="20" class="animate-spin" />
            </span>
          </x-button>
        </div>
      </div>
    </span>

  </div>
</template>
