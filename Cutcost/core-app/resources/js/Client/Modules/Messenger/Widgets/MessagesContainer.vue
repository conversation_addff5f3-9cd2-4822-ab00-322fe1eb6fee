<script setup>
import {
  shallowRef,
  useTemplateRef,
  nextTick,
  onUnmounted,
} from "vue";
import { useIntersectionObserver } from "@vueuse/core";
import Message from "../Features/MessageContainer/Message.vue";
import ContainerData from "../Features/MessageContainer/ContainerData.vue";
import { useGlobalStore } from "@s";
import { useMessagesContainer } from "../Composables/Chat/useMessagesContainer";
import { useChatStore } from "../Stores/useChatStore";
import ChatBlocked from "../Features/MessageContainer/ChatBlocked.vue";
import ToBottom from "../Features/MessageContainer/ToBottom.vue";

const props = defineProps({
  chat: Object,
});

const { showToBottomButton, getMessages } = useMessagesContainer();

const chatStore = useChatStore();
const global = useGlobalStore();

chatStore.container = useTemplateRef("container");

// bool is visible
const toBottomButton = shallowRef(false);
// index of the message that has the menu open
//pagination
const loadMessagesContainer = useTemplateRef("loadMessagesContainer");

const onScroll = () => {
  toBottomButton.value = showToBottomButton();
};

const { stop } = useIntersectionObserver(
  loadMessagesContainer,
  ([entry], observerElement) => {
    if (entry?.isIntersecting) {
      loadNextMessages();
    }
  },
);

onUnmounted(() => {
  chatStore.messages = [];
});

// container offset cause eager loading of images
const loadNextMessages = async () => {
  try {
    const previousScrollHeight = chatStore.container.scrollHeight;

    const messages = await getMessages(props.chat, chatStore.messages);
    if (!messages?.length) return;
    chatStore.messages.unshift(...messages);

    //fix user view cause it will breaks
    nextTick(() => {
      chatStore.container.scrollTop +=
        chatStore.container.scrollHeight - previousScrollHeight;
    });
  } catch (err) {
    console.error(err);
  }
};

</script>

<template>
  <div
    @scroll="onScroll"
    ref="container"
    v-bind="$attrs"
    class="h-full overflow-y-auto overflow-x-hidden px-1"
  >
    <ToBottom v-if="toBottomButton" />

    <div class="flex w-full" ref="loadMessagesContainer"></div>

    <div
      class="my-2"
      v-for="(message, index) in chatStore.messages"
      :key="message.id"
      @click.stop="fieldFocus"
    >
      <ContainerData :message :messages="chatStore.messages" :index="index" />

      <div class="group flex justify-end px-2" v-if="message.user.id === global.user.id">
        <Message
          :chat="chat"
          :message="message"
          :index="index"
        />
      </div>

      <div class="flex justify-start px-2" v-else>
        <Message
          :chat="chat"
          friend
          :message="message"
          :index="index"
        />
      </div>
    </div>

    <ChatBlocked :chat="chat" />
  </div>
</template>
