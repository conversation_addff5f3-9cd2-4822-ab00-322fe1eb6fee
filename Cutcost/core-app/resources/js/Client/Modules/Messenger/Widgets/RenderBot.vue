<script setup>
import { computed, inject } from "vue";
import { useChatInput } from "../Composables/Chat/useChatInput";
import { router } from "@inertiajs/vue3";

const route = inject("route");

const { inputSend } = useChatInput();

const props = defineProps({
    chat: Object,
});

const bot = computed(() => props.chat.company?.bot);

const handleClick = async (action) => {
    if (action.url) {
        router.get(route("redirect", { url: action.url }));
    } else if (action.message_send) {

        const request = { content: action.message_send, images: [], replied_content: null };

        await inputSend(props.chat, request);
    } else {
    }
}
</script>

<template>
    <div v-if="bot.builder?.actions?.add_button?.length" class="flex flex-wrap gap-2 py-2 justify-start">
        <x-button v-for="(action, idx) in bot.builder.actions.add_button" :key="idx" @click="handleClick(action)"
            type="button">
            {{ action.message || action.message_send || 'Button' }}
        </x-button>
    </div>
</template>
