export const useBusinessHours = () => {
  const days = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];
  const defaultData = {
    enabled: false,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    days: [
      {
        name: "Monday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
      {
        name: "Tuesday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
      {
        name: "Wednesday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
      {
        name: "Thursday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
      {
        name: "Friday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
      {
        name: "Saturday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
      {
        name: "Sunday",
        diapasons: [
          {
            start: "09:00",
            end: "18:00",
          },
        ],
        workDay: true,
      },
    ],
    holidays: [],
  };

  return {
    days,
    defaultData,
  };
};
