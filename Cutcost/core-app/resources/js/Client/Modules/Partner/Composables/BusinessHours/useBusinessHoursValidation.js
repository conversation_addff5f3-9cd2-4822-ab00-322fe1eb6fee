import { computed } from "vue";
import { useTranslation } from "@s";

export function useBusinessHoursValidation(days) {
  // Валидация для одного дня
  function validateDay(day) {
    const errors = [];

    if (day.type === "work_day" && !day.open24) {
      if (!day.timeStart || !day.timeEnd) {
        errors.push(useTranslation.__("Start and end are required"));
      } else if (day.timeStart >= day.timeEnd) {
        errors.push(useTranslation.__("Time start must be before time end"));
      }
    }

    // Проверка перерывов
    day.breaks.forEach((brk, idx) => {
      if (!brk.start || !brk.end) {
        errors.push(
          useTranslation.__("Break is not valid", { number: idx + 1 }),
        );
      } else if (brk.start >= brk.end) {
        errors.push(
          useTranslation.__("Break start must be before end", {
            number: idx + 1,
          }),
        );
      }

      if (!day.open24 && day.timeStart && day.timeEnd) {
        if (brk.start < day.timeStart || brk.end > day.timeEnd) {
          errors.push(
            useTranslation.__("Break must be within working hours", {
              number: idx + 1,
            }),
          );
        }
      }
    });

    // Проверка пересечений перерывов
    for (let i = 0; i < day.breaks.length; i++) {
      for (let j = i + 1; j < day.breaks.length; j++) {
        const a = day.breaks[i];
        const b = day.breaks[j];
        if (!(a.end <= b.start || a.start >= b.end)) {
          errors.push(
            useTranslation.__("Breaks and cannot overlap", {
              number_1: i + 1,
              number_2: j + 1,
            }),
          );
        }
      }
    }

    return errors;
  }

  const validationErrors = computed(() => {
    const errs = {};
    days.forEach((day, idx) => {
      const dayErrors = validateDay(day);
      if (dayErrors.length) {
        errs[idx] = dayErrors;
      }
    });
    return errs;
  });

  function isValid() {
    return Object.keys(validationErrors.value).length === 0;
  }

  return {
    validationErrors,
    isValid,
  };
}
