import { useTranslation } from "@s";

// CY prod. 2024

export const useValidation = () => {
  // for translations
  const translatedName = (item) => " / " + useTranslation.__(item.name);

  /* available rules:
    required : the field is required.
    numeric : the field value is a number and non-negative (unsigned).
    url : the field value is a valid URL. (https://example.com)
    date : the field value is a valid date. (2024-01-01)
   */

  // :TODO move rules decklaration to composable init
  const rules = {
    valid_until: "required|date",
    user_scan_limits: "required|numeric",
    users_scan_limits: "required|numeric",
    social_media_subscription: "required|url",
    custom_condition: "required",
    product_gift: "required",
    custom_event: "required",
  };

  /**
   * validate sales builder actions, conditions, events
   *
   * @param {Object} blueprint
   * @returns {String}
   */
  const validate = (blueprint) => {
    let error = "";

    if (!blueprint.length) return "";
    // return useTranslation.__("validation.no_blueprint_data");

    if (!blueprint.some((item) => item.type === "event")) {
      return useTranslation.__("validation.no_events");
    }

    if (!blueprint.some((item) => item.type === "condition")) {
      return useTranslation.__("validation.no_conditions");
    }

    error = validateFormElements(blueprint);

    if (error) return error;

    return error;
  };

  const validateFormElements = (blueprint) => {
    for (const item of blueprint) {
      let meta = {};

      try {
        meta =
          typeof item.meta === "string"
            ? JSON.parse(item.meta)
            : item.meta || {};
      } catch {
        meta = {};
      }

      const supportedKeys = [
        "input",
        "textarea",
        "select",
        "checkbox",
        "radio",
      ];

      let foundValidatableField = false;

      for (const key of supportedKeys) {
        const field = meta[key];

        if (field && typeof field === "object" && "value" in field) {
          foundValidatableField = true;

          // тут берем правило для текущего поля, если оно есть
          const rule = rules[item.name]
            ? rules[item.name] + "|required"
            : "required";

          const error = validateByValue(field, rule);

          if (error) return error + " " + translatedName(item);
        }
      }

      // Если валидируемых полей нет — пропускаем блок без ошибки
    }

    return "";
  };

  const validateByValue = (field, rule) => {
    if (!rule) return "";

    const rulesArray = rule.split("|");

    for (const r of rulesArray) {
      if (r === "required" && (field.value === null || field.value === "")) {
        return useTranslation.__("validation.no_value_for");
      }

      if (
        r === "numeric" &&
        (isNaN(Number(field.value)) || Number(field.value) < 0)
      ) {
        return useTranslation.__("validation.invalid_number");
      }

      if (r === "url" && !/^https?:\/\/\S+$/.test(field.value)) {
        return useTranslation.__("validation.invalid_url");
      }

      if (r === "date") {
        if (isNaN(Date.parse(field.value))) {
          return useTranslation.__("validation.invalid_date");
        }

        // Проверка даты больше сегодняшнего дня
        const inputDate = new Date(field.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (inputDate <= today) {
          return useTranslation.__("validation.date_must_be_after_today");
        }
      }
    }

    return "";
  };

  return {
    validate,
  };
};
