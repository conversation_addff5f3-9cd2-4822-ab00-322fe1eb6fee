<script setup>
import { ref, computed } from 'vue';
import { useBusinessHours } from '../../Composables/BusinessHours/useBusinessHours';

const emit = defineEmits(['apply']);
const { days } = useBusinessHours();

const startIndex = ref(null);   // выбранный старт (после 1-го клика)
const endIndex = ref(null);     // выбранный конец (после 2-го клика)
const previewIndex = ref(null); // временный индекс при наведении (показать диапазон до 2-го клика)

// утилита: нормализовать (включительно)
function getRange(a, b) {
    if (a === null || b === null) return null;
    const s = Math.min(a, b);
    const e = Math.max(a, b);
    return { start: s, end: e };
}

const finalRange = computed(() => getRange(startIndex.value, endIndex.value));
const previewRange = computed(() => {
    if (startIndex.value !== null && endIndex.value === null && previewIndex.value !== null) {
        return getRange(startIndex.value, previewIndex.value);
    }
    return null;
});

function clearRange() {
    startIndex.value = null;
    endIndex.value = null;
    previewIndex.value = null;
}

// Логика клика:
// - если нет выбора -> 1-й клик: set start
// - если есть start и нет end:
//     * клик по тому же дню -> СБРОС
//     * клик по другому дню -> set end (финал)
// - если есть и start и end -> 3-й клик -> СБРОС
function onDayClick(idx) {
    if (startIndex.value !== null && endIndex.value !== null) {
        clearRange();
        return;
    }

    if (startIndex.value === null) {
        startIndex.value = idx;
        previewIndex.value = null;
        return;
    }

    if (startIndex.value !== null && endIndex.value === null) {
        if (idx === startIndex.value) {
            clearRange();
            return;
        }
        endIndex.value = idx;
        previewIndex.value = null;
        return;
    }
}

function onDayPointerEnter(idx) {
    if (startIndex.value !== null && endIndex.value === null) {
        previewIndex.value = idx;
    }
}

function onDayPointerLeave() {
    if (startIndex.value !== null && endIndex.value === null) {
        previewIndex.value = null;
    }
}

function applyAll() {
    if (!finalRange.value) {
        emit('apply', null);
        return;
    }
    emit('apply', {
        startIndex: finalRange.value.start,
        endIndex: finalRange.value.end,
        startDay: days[finalRange.value.start],
        endDay: days[finalRange.value.end],
    });
    clearRange();
}
</script>

<template>
    <div>
        <div class="flex items-center text-text dark:text-dark-text justify-between mb-3">
            <div class="text-xs">
                <span v-if="finalRange">
                    {{ __('weekdays.' + finalRange.start) }} — {{ __('weekdays.' + finalRange.end) }}
                </span>
                <span v-else-if="startIndex !== null">
                    {{ __('weekdays.' + startIndex) }}
                </span>
            </div>
        </div>

        <div class="flex gap-2 flex-wrap" @pointerleave="onDayPointerLeave">
            <template v-for="(label, idx) in days" :key="idx">
                <button type="button" class="px-3 py-2 rounded-lg text-sm font-medium transition" :class="{
                    'bg-secondary text-bg shadow': finalRange && idx >= finalRange.start && idx <= finalRange.end,
                    'bg-secondary !text-bg': !finalRange && previewRange && idx >= previewRange.start && idx <= previewRange.end,
                    'bg-bg text-secondary': !finalRange && startIndex !== null && idx === startIndex,
                    'bg-white text-gray-700 hover:bg-gray-50': !(finalRange && idx >= finalRange.start && idx <= finalRange.end) && !(previewRange && idx >= previewRange.start && idx <= previewRange.end) && !(startIndex !== null && idx === startIndex)
                }" @click="onDayClick(idx)" @pointerenter="onDayPointerEnter(idx)"
                    :aria-pressed="(finalRange && idx >= finalRange.start && idx <= finalRange.end) || (!finalRange && startIndex !== null && idx === startIndex) ? 'true' : 'false'"
                    :title="label">
                    <span class="hidden sm:inline">{{ __('weekdays.' + idx) }}</span>
                    <span class="sm:hidden">{{ __('weekdays.' + idx)?.slice(0, 2) }}</span>
                </button>
            </template>
        </div>

        <div class="mt-4 flex items-center justify-end gap-2">
            <x-button type="button" @click="applyAll"
                :disabled="!finalRange" :class="{ 'opacity-60 cursor-not-allowed': !finalRange }">
                {{ __('Paste') }}
            </x-button>
        </div>
    </div>
</template>

<style scoped>
button[disabled] {
    pointer-events: none;
}
</style>
