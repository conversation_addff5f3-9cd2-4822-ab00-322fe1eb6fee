<script setup>
import { PhPlus, PhTrash } from '@phosphor-icons/vue';
import { watch, computed, onMounted } from 'vue';

const model = defineModel({ type: Object });

// times: каждые 15 минут + '24:00'
const times = Array.from({ length: 24 * 4 + 1 }, (_, i) => {
  const totalMinutes = i * 15;
  const h = Math.floor(totalMinutes / 60).toString().padStart(2, '0');
  const m = (totalMinutes % 60).toString().padStart(2, '0');
  return `${h}:${m}`;
});

function timeToMinutes(time) {
  const [h, m] = time.split(':').map(Number);
  return h * 60 + m;
}
function minutesToTime(min) {
  const h = Math.floor(min / 60).toString().padStart(2, '0');
  const m = (min % 60).toString().padStart(2, '0');
  return `${h}:${m}`;
}

const DEFAULT_DURATION_MIN = 60; // длительность при добавлении нового диапазона

// Инициализация: если пусто — добавить один дефолтный диапазон
onMounted(() => {
  if (!Array.isArray(model.value) || model.value.length === 0) {
    // если model сам по себе не массив — подстрой под свой вариант
    model.value = [{ start: '09:00', end: '10:00' }];
  }
});

// HELPERS: проверка пересечений
function intersectsRange(startA, endA, startB, endB) {
  return timeToMinutes(startA) < timeToMinutes(endB) && timeToMinutes(endA) > timeToMinutes(startB);
}
function isTimeInsideOther(time, idx) {
  const t = timeToMinutes(time);
  return model.value.some((other, i) => i !== idx && timeToMinutes(other.start) <= t && t < timeToMinutes(other.end));
}
function rangeIntersectsOther(start, end, idx) {
  return model.value.some((other, i) => {
    if (i === idx) return false;
    return intersectsRange(start, end, other.start, other.end);
  });
}

// availableStartTimes: НЕ зависит от d.end — разрешаем выбирать старт до 24:00,
// но запрещаем старты, которые внутри других диапазонов
function availableStartTimes(idx) {
  const d = model.value[idx];
  // разрешим любой time, который не "внутри" другого диапазона (и до 24:00)
  return times.filter(t => {
    // запретить старт если он попадает внутрь чужого диапазона
    if (isTimeInsideOther(t, idx)) return false;
    // не разрешаем старт равный 24:00
    return timeToMinutes(t) < 24 * 60;
  });
}

// availableEndTimes: зависит от выбранного start — end должен быть > start
// и не создавать пересечений с другими
function availableEndTimes(idx) {
  const d = model.value[idx];
  if (!d || !d.start) return times;
  const startMin = timeToMinutes(d.start);
  return times.filter(t => {
    const tMin = timeToMinutes(t);
    if (tMin <= startMin) return false; // end должно быть строго больше start
    // не допускаем end, который делает пересечение
    return !rangeIntersectsOther(d.start, t, idx);
  });
}

// helper: найти первый допустимый end >= (start + minDuration)
function findEarliestEndForStart(start, idx, minDuration = DEFAULT_DURATION_MIN) {
  const startMin = timeToMinutes(start);
  const minEndMin = startMin + minDuration;
  for (let i = 0; i < times.length; i++) {
    const t = times[i];
    const tMin = timeToMinutes(t);
    if (tMin < minEndMin) continue;
    // end не может быть > 24*60
    if (tMin > 24 * 60) continue;
    if (!rangeIntersectsOther(start, t, idx)) {
      return t;
    }
  }
  return null;
}

// Автоподгонка — если значение перестало быть валидным
function fixIfInvalid(idx) {
  const d = model.value[idx];
  if (!d) return;
  // start: если вдруг попал внутрь другого — подвинем на ближайший допустимый
  const starts = availableStartTimes(idx);
  if (!starts.includes(d.start)) {
    // выберем ближайший <= текущего или первый допустимый
    const curMin = timeToMinutes(d.start);
    let candidate = starts.slice().reverse().find(t => timeToMinutes(t) <= curMin) || starts[0];
    if (candidate) {
      // лучше заменить объект, чтобы гарантировать реактивность
      model.value[idx] = { ...d, start: candidate };
    }
  }

  // end: должен быть >= start + minDuration и не пересекаться
  const ends = availableEndTimes(idx);
  const minCandidate = findEarliestEndForStart(model.value[idx].start, idx, DEFAULT_DURATION_MIN);
  if (!ends.includes(model.value[idx].end) || (minCandidate && timeToMinutes(model.value[idx].end) < timeToMinutes(minCandidate))) {
    const newEnd = minCandidate || ends[ends.length - 1] || model.value[idx].end;
    model.value[idx] = { ...model.value[idx], end: newEnd };
  }

  // защитный случай: если start >= end — скорректируем end
  if (timeToMinutes(model.value[idx].start) >= timeToMinutes(model.value[idx].end)) {
    const fallbackEnd = minutesToTime(timeToMinutes(model.value[idx].start) + 30);
    model.value[idx] = { ...model.value[idx], end: fallbackEnd };
  }
}

// Найти первый свободный слот (для addDiapason)
function findFirstAvailableSlot(durationMin = DEFAULT_DURATION_MIN) {
  for (let i = 0; i < times.length; i++) {
    const start = times[i];
    const endMin = timeToMinutes(start) + durationMin;
    if (endMin > 24 * 60) continue;
    const end = minutesToTime(endMin);
    if (!isTimeInsideOther(start, -1) && !rangeIntersectsOther(start, end, -1) && timeToMinutes(start) < timeToMinutes(end)) {
      return { start, end };
    }
  }
  return null;
}

const canAdd = computed(() => !!findFirstAvailableSlot());

// add / remove
function addDiapason() {
  const slot = findFirstAvailableSlot();
  if (!slot) {
    alert('Нет свободного времени — добавить диапазон невозможно.');
    return;
  }
  model.value.push({ start: slot.start, end: slot.end });
}
function removeDiapason(i) {
  if (model.value.length <= 1) {
    alert('Нельзя удалить последний диапазон — как минимум должен быть один.');
    return;
  }
  model.value.splice(i, 1);
}

// Watch: если пользователь изменил start (или список стартов), автоматически подстраиваем end.
// Отслеживаем array of starts — это позволяет обнаружить *какой именно* индекс изменился.
watch(
  () => model.value.map(d => d.start),
  (newStarts, oldStarts) => {
    for (let i = 0; i < newStarts.length; i++) {
      if (newStarts[i] !== oldStarts?.[i]) {
        // start на позиции i изменился — подберём end
        const minEnd = findEarliestEndForStart(newStarts[i], i, DEFAULT_DURATION_MIN);
        if (minEnd) {
          // если текущий end меньше минимума или пересекается — заменить
          const curEnd = model.value[i].end;
          if (!curEnd || timeToMinutes(curEnd) < timeToMinutes(minEnd) || rangeIntersectsOther(newStarts[i], curEnd, i)) {
            model.value[i] = { ...model.value[i], end: minEnd };
          }
        } else {
          // не нашли end — можно откатить start на предыдущий валидный или уведомить
          // для простоты: откат на старое значение (если есть)
          if (oldStarts?.[i]) {
            model.value[i] = { ...model.value[i], start: oldStarts[i] };
            // и можно показать уведомление
            // alert('Невозможно подобрать корректный конец для выбранного старта.');
          } else {
            // если предыдущего нет — выставим ближайший допустимый end = последний элемент times
            model.value[i] = { ...model.value[i], end: times[times.length - 1] };
          }
        }
        // после корректировки — проверим и подправим другие индексы
        // (чтобы не оставить пересечений)
        for (let j = 0; j < model.value.length; j++) fixIfInvalid(j);
      }
    }
  },
  { deep: false, immediate: false }
);

// Старайся также вызывать fixIfInvalid при mount и при любых внешних изменениях
onMounted(() => {
  for (let i = 0; i < model.value.length; i++) fixIfInvalid(i);
});

</script>


<template>
    <div class="max-w-xl mx-auto p-4">
        <div class="space-y-3 text-text dark:text-dark-text">
            <div v-for="(d, index) in model" :key="index" class="flex gap-2">
                <!-- Start -->
                <div class="flex-1 min-w-0">
                    <label class="block text-xs font-medium 600 mb-1">{{ __('From') }}</label>
                    <select v-model="d.start"
                        class="w-full text-sm rounded-md dark:bg-bg-dark border-border focus:ring-2 focus:ring-secondary  px-3 py-2 bg-surface"
                        >
                        <option v-for="time in availableStartTimes(index)" :key="time" :value="time">{{ time }}
                        </option>
                    </select>
                </div>

                <!-- End -->
                <div class="flex-1 min-w-0">
                    <label class="block text-xs font-medium  mb-1">{{ __('To') }}</label>
                    <select v-model="d.end"
                        class="w-full text-sm rounded-md dark:bg-bg-dark border-border focus:ring-2 focus:ring-secondary  px-3 py-2 bg-surface"
                        >
                        <option v-for="time in availableEndTimes(index)" :key="time" :value="time">{{ time }}
                        </option>
                    </select>
                </div>
                <div class="flex items-end">
                    <x-button intent="danger" @click="removeDiapason(index)" v-if="model.length > 1">
                        <PhTrash :size="20" />
                    </x-button>
                </div>
            </div>
        </div>

        <div class="mt-4 flex items-center justify-center">
            <div class="flex items-center gap-2">
                <x-button @click="addDiapason" v-if="canAdd">
                    <PhPlus :size="20" />
                </x-button>
                <slot />
            </div>
        </div>
    </div>
</template>

<style scoped>
select::-ms-expand {
    display: none;
}

</style>
