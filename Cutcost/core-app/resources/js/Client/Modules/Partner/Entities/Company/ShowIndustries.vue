<script setup>
const props = defineProps({
    industries: {
        type: Array,
        required: true,
    },
});
</script>

<template>
  <div class="break-before-column !font-serif break-words whitespace-pre-line text ">
    <span v-for="(industry, index) in props.industries" :key="industry.id">
      {{ __(industry.name) }}<span v-if="index < props.industries.length - 1">, </span>
    </span>
  </div>
</template>
