<script setup>
import { ref, inject } from "vue";
import { router } from "@inertiajs/vue3";
import { PhTrash } from "@phosphor-icons/vue";

const props = defineProps({
    article: Object,
    icon: {
        type: Boolean,
        default: false,
    },
});

const route = inject("route");
const showConfirmation = ref(false);

const askDelete = () => {
    showConfirmation.value = true;
};

const deleteArticle = () => {
    router.delete(route("companies.articles.destroy", {
        company: props.article.company_id,
        article: props.article.slug,
    }));
    showConfirmation.value = false;
};

const cancelDelete = () => {
    showConfirmation.value = false;
};
</script>

<template>
    <div>
        <template v-if="icon">
            <x-button intent="rounded" @click="askDelete">
                <PhTrash :size="20" class="!fill-error" />
            </x-button>
        </template>
        <template v-else>
            <x-button intent="danger" @click="askDelete">
                {{ __('Delete') }}
            </x-button>
        </template>

        <x-confirmation v-if="showConfirmation" :message="__('Are you sure')" :for="props.article.id"
            @confirm="deleteArticle" @cancel="cancelDelete" />
    </div>
</template>
