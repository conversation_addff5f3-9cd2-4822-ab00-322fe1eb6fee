<script setup>
import { ref, useTemplateRef } from 'vue';

const model = defineModel();

defineProps({
    update: {
        type: Boolean,
        required: true,
    },
})
const modal = useTemplateRef("modal");

const types = ref([
    { id: 'url', name: 'URL' },
    { id: 'message', name: 'Message' },
    { id: 'phone', name: 'Phone' },
    { id: 'question', name: 'Question' },
]);
</script>

<template>
    <div>
        <x-button @click="modal.modalOpen = true">
            {{ __("Add button") }}
        </x-button>
        <Modal ref="modal" :title="__('Add button')">
            <x-select v-model="model.type" :data="types" :label="__('Select type')" />
            <x-input v-model="model.message" type="text" :label="__('Button Text')" />
            <x-input v-model="model.url" label="URL" placeholder="https://..." />
            <x-input v-model="model.message_send" type="text" :label="__('Message')" />
        </Modal>
    </div>
</template>