<script setup>
import { onMounted, useTemplateRef, ref } from "vue";
import { useBusinessHours } from "../Composables/BusinessHours/useBusinessHours";
import DiapasonSelect from "../Entities/BusinessHours/DiapasonSelect.vue";
import ApplyAll from "../Entities/BusinessHours/ApplyAll.vue";
import { PhCopy, PhPencil, PhPower } from "@phosphor-icons/vue";
import { ShowBusinessHours } from "@m/Public";

const { days, defaultData } = useBusinessHours();
const model = defineModel({ type: Object });
const applyModal = useTemplateRef("modal");
const applyIndex = ref(null);

onMounted(() => {
    if (!model.value?.days?.length) model.value = defaultData;
});

const applyToDays = (range) => {
    if (!range) return;

    const index = JSON.parse(JSON.stringify(applyIndex.value));

    const data = model.value.days[index]?.diapasons;

    for (let i = range.startIndex; i <= range.endIndex; i++) {
        // splice for reactivity
        model.value.days.splice(i, 1, {
            ...model.value.days[i],
            diapasons: data ? JSON.parse(JSON.stringify(data)) : [],
        });
    }

    applyExit();
};

const openModal = (index) => {
    applyIndex.value = index;
    applyModal.value.modalOpen = true;
};

const applyExit = () => {
    applyIndex.value = null;
    applyModal.value.modalOpen = false;
};
</script>

<template>
    <div v-if="model?.days" class="w-full">
        <Modal ref="modal" :title="__('Copy hours',
            { day: __('weekdays.' + applyIndex) })">
            <DiapasonSelect v-if="applyIndex !== null" v-model="model.days[applyIndex].diapasons">
            </DiapasonSelect>
            <ApplyAll @apply="applyToDays" v-if="applyIndex !== null" />
        </Modal>

        <ShowBusinessHours :data="model" edit :timezone="Intl.DateTimeFormat().resolvedOptions().timeZone">
            <x-button intent="rounded" :class="model.enabled ? '!text-success' : '!text-error'"
                @click="model.enabled = !model.enabled">
                <PhPower :size="20" />
            </x-button>
            <x-button v-if="model.enabled" intent="rounded" @click="businessHoursModal.modalOpen = true">
                <PhPencil :size="20" />
            </x-button>
            <h1 class="h1 ml-2" v-else>
                {{__('business hours')}}
            </h1>

        </ShowBusinessHours>

        <Modal :title="__('Business hours')" ref="businessHoursModal">
            <div v-for="(day, index) in days" :key="index">
                <div class="flex max-sm:flex-col justify-between items-center">
                    <div>
                        <x-checkbox v-model="model.days[index].workDay" :label="__('weekdays.' + index)" />
                    </div>
                    <div v-if="model.days[index].workDay">
                        <DiapasonSelect v-model="model.days[index].diapasons">
                            <x-button @click="openModal(index)">
                                <PhCopy :size="20" />
                            </x-button>
                        </DiapasonSelect>
                    </div>
                </div>
                <x-hr v-if="index !== days.length - 1" />
            </div>
        </Modal>
    </div>
</template>
