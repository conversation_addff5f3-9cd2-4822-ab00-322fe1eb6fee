<script setup>
import { ref, inject, useTemplateRef } from 'vue';
import { useHelpers } from '@s';

const route = inject('route');
const { debounce } = useHelpers();

const props = defineProps({
    country: { type: Object, default: null },
    country_id: { type: Number, default: null },
    required: <PERSON><PERSON><PERSON>,
    message: String,
    data: {
        type: Array,
        default: () => [],
    },
});

const query = ref('')
const results = ref([]);
const select = useTemplateRef('select');
const model = defineModel();

if (props.data?.length && props.data?.[0]?.id) { // if data is array
    results.value = props.data?.[0]?.id ? props.data : []; // if data is array
}

if (props.data?.id) { // if data is object
    results.value = props.data?.id ? props.data : [];
}

const searchCities = async () => {
    if (query.value.length < 2) {
        results.value = [];
        return;
    }

    try {
        const res = await axios.post(route('search.city'), {
            query: query.value,
            country_code: props?.country?.code ?? null,
            country_id: props?.country_id ?? null,
        });
        const data = res.data ?? [];

        const normalizedQuery = query.value.trim().toLowerCase();

        const uniqueResults = [...new Map(
            data
                .filter(item => item.id && item.name)
                .map(item => [
                    item.id,
                    {
                        id: item.id,
                        name: `${item.name}, ${item.country_code}`,
                        originalName: item.name.toLowerCase()
                    }
                ])
        ).values()];

        results.value = uniqueResults.sort((a, b) => {
            const rank = (name) => {
                if (name === normalizedQuery) return 0;
                if (name.startsWith(normalizedQuery)) return 1;
                if (name.includes(normalizedQuery)) return 2;
                return 3;
            };

            return rank(a.originalName) - rank(b.originalName);
        });
        select.value.show();

    } catch (e) {
        console.error('Error fetching cities:', e);
    }
};

const debouncedSearchCities = debounce(searchCities, 300)
</script>

<template>
    <div class="relative py-2">
        <x-input type="text" v-model="query" @input="debouncedSearchCities" :placeholder="__('Search')"
            :label="__('Select city') + (required ? '*' : '')" class="w-full mb-1" autocomplete="off" />
        <div v-show="results.length">
            <x-select v-model="model" :data="results" ref="select" :message="message" />
        </div>
    </div>
</template>
