<script setup>
import {
    PhMapPinLine,
    // PhQrCode,
    PhSealPercent,
    PhHeadCircuit,
    PhGearSix,
} from "@phosphor-icons/vue";

import DeleteCompany from "./DeleteCompany.vue";

const props = defineProps({
    company: Object,
});

</script>

<template>
    <div v-bind="$attrs">
        <x-button intent="rounded" class="!px-2" @click="$refs.modal.modalOpen = true" @click.stop>
            <template v-if="$slots.default">
                <slot />
            </template>
            <template v-else>
                <PhGearSix :size="20" />
            </template>
        </x-button>
    </div>
    <Modal ref="modal" :title="__('Manage - ') + company.name">
        <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
            <x-button :href="route('partner.coupons.create', { company: company.slug })">
                <template #icon>
                    <PhSealPercent :size="20" weight="bold" />
                </template>
                <span>{{ __("Create coupon") }}</span>
            </x-button>
            <x-button :href="route('partner.locations.create', { company: company.slug })">
                <template #icon>
                    <PhMapPinLine :size="20" weight="bold" />
                </template>
                <span>{{ __("Create location") }}</span>
            </x-button>
            <x-button :href="route('partner.bot.index', { company: company.slug })">
                <template #icon>
                    <PhHeadCircuit :size="20" weight="bold" />
                </template>
                <span>{{ __("Sales Assistant") }}</span>
            </x-button>
            <DeleteCompany :company="company" />
            <!-- <x-button :href="route('partner.scan.index', { company: company.slug })">
                <template #icon>
                    <PhQrCode :size="20" weight="bold" />
                </template>
                <span>{{ __("Scan") }}</span>
            </x-button> -->
        </div>
    </Modal>
</template>
