<script setup>
import { ref, inject } from "vue";
import { router } from "@inertiajs/vue3";
import { PhTrash } from "@phosphor-icons/vue";

const props = defineProps({
    company: Object,
});

const route = inject("route");
const showConfirmation = ref(false);

const askDelete = () => {
    showConfirmation.value = true;
};

const deleteCompany = () => {
    router.delete(route("partner.companies.destroy", {
        company: props.company.slug,
    }));
    showConfirmation.value = false;
};

const cancelDelete = () => {
    showConfirmation.value = false;
};
</script>

<template>
    <x-button @click="askDelete" class="w-full">
        <template #icon>
            <PhTrash :size="20" class="!fill-error" />
        </template>
        {{ __('Delete') }}
    </x-button>
    <x-confirmation v-if="showConfirmation" strict :message="__('Are you sure')" :for="company.slug"
        @confirm="deleteCompany" @cancel="cancelDelete" />
</template>
