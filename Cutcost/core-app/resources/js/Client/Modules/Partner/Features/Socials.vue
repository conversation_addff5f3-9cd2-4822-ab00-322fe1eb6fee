<script setup>
import { ref } from "vue";
import { PhTrash } from "@phosphor-icons/vue";
import { useToastStore, useTranslation } from "@s";

const toast = useToastStore(), __ = useTranslation.__;
const model = defineModel();
const local = ref(model.value.length ? [...model.value] : [""]);

const update = (i, v) => {
    local.value[i] = v;
    model.value = local.value.map(s => s.trim()).filter(s => s !== "");
};

const add = () => {
    if (local.value.some(s => !s.trim())) {
        toast.warning(__("Please fill existing"));
        return;
    }
    local.value.push("");
};

const remove = (i) => {
    if (local.value.length === 1) {
        local.value[0] = "";
        model.value = [];
        return;
    }
    local.value.splice(i, 1);
    model.value = local.value.map(s => s.trim()).filter(s => s !== "");
};
</script>

<template>
    <div class="w-full space-y-1">
        <Label>{{ __("Links or phone numbers") }}</Label>

        <div v-for="(s, i) in local" :key="i">
            <x-input :model-value="s" @update:model-value="v => update(i, v)">
                <template #icon>
                    <div @click="remove(i)">
                        <PhTrash :size="18" class="fill-error" />
                    </div>
                </template>
            </x-input>
        </div>

        <div class="mt-2">
            <x-button intent="secondary" class="text-sm font-medium" @click="add">
                {{ __("Add") }}
            </x-button>
        </div>
    </div>
</template>
