<script setup>
import { Head } from "@inertiajs/vue3";
import ArticleForm from "../../Widgets/Articles/ArticleForm.vue";

const props = defineProps({
    article: Object,
    company: Object,
});

</script>
<template>

    <Head>
        <title>{{ __("Edit article") }} `{{ article?.name }}` </title>
    </Head>
    <div class="card-position">
        <ArticleForm :article :company />
    </div>
</template>
