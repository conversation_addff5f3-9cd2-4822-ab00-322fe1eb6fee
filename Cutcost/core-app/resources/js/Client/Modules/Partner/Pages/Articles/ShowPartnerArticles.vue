<script setup>
import { Head } from "@inertiajs/vue3";
import DeleteArticle from "../../Features/Article/DeleteArticle.vue";
import { PhPencil, PhPlus } from "@phosphor-icons/vue";

const props = defineProps({
    articles: Object,
    company: Object,
});
</script>

<template>

    <Head>
        <title>{{ __('My articles') }} </title>
    </Head>
    <div class="card-position">
        <Card :title="__('My articles')">
            <template #title>
                <x-button as="Link" intent="rounded"
                    :href="route('partner.articles.create', { company: company.slug })">
                    <PhPlus :size="20" class="!fill-secondary" weight="bold" />
                </x-button>

            </template>
            <x-table :paginator="articles" translate :hidden="['slug']">
                <template #default="{ row }">
                    <div class="flex gap-2">
                        <x-button intent="rounded"
                            :href="route('partner.articles.edit', { article: row.slug, company: company.slug })">
                            <PhPencil :size="20" class="!fill-secondary" />
                        </x-button>
                        <DeleteArticle :article="row" icon />
                    </div>
                </template>

            </x-table>
        </Card>
    </div>
</template>
