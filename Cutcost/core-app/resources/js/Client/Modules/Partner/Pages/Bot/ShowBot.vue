<script setup>
import { inject } from 'vue';
import { useHeaderStore } from '../../../Fixed/Stores/use-header-store';
import { useForm } from '@inertiajs/vue3';
import BotForm from '../../Widgets/Bot/BotForm.vue';

const route = inject('route');
const props = defineProps({
    company: Object,
    bot: Object,
});

// Form with validation rules
const botForm = useForm({
    chat_created_message: props.bot?.builder?.chat_created_message ?? '',
    reply_message: props.bot?.builder?.reply_message ?? '',
});

function submit() {
    botForm.put(route('partner.bot.update', { company: props.company.slug }));
}
</script>

<template>

    <Head :title="__('Sales Assistant') " />

    <div class="card-position">
        <x-form :form="botForm" @submit="submit" type="update" noClass>
            <Card class="space-y-6" :title="__('Sales Assistant')" :url="route('company.show', { slug: company.slug })">
                <BotForm :botForm="botForm" />
            </Card>
        </x-form>
    </div>
</template>
