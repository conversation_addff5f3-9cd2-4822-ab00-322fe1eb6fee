<script setup>
import { onMounted, onUnmounted } from "vue";
import CompanyForm from "../../Widgets/Company/CompanyForm.vue";
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

defineProps({
  industries: Object,
  currencies: Object,
});

onMounted(() => {
  header.title = "Create company";
});

onUnmounted(() => {
  header.clear();
});

</script>
<template>
  <Head :title="__('Create company') " />
  <div class="card-position">
    <CompanyForm :industries :currencies create />
  </div>
</template>
