<script setup>
import { Head } from "@inertiajs/vue3";
import CompanyForm from "../../Widgets/Company/CompanyForm.vue";
import { useHeaderStore } from "@m/Fixed";
import { onMounted, onUnmounted, inject } from "vue";

const route = inject("route");

const header = useHeaderStore();

const props = defineProps({
    company: Object,
    industries: Object,
    countries: Object,
    currencies: Object,
});

onMounted(() => {
    header.backUrl = route('company.show', { slug: props.company.slug });
    header.title = 'Edit company';
});

onUnmounted(() => {
    header.clear();
});
</script>
<template>

    <Head>
        <title>{{ __("Edit company") }} </title>
    </Head>
    <div class="card-position">
        <CompanyForm :company :industries :countries :currencies />
    </div>
</template>
