<script setup>
import { onMounted } from "vue";
import CouponForm from "../../Widgets/Coupon/CouponForm.vue";
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

const props = defineProps({
  company: Object,
});

onMounted(() => {
  header.title = 'Create coupon';
});
</script>

<template>

  <Head :title="__('Create coupon') "> </Head>
  <div class="card-position">
    <CouponForm create :company />
  </div>
</template>
