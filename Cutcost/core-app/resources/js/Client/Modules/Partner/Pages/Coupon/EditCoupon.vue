<script setup>
import CouponForm from "../../Widgets/Coupon/CouponForm.vue";
import CouponLocations from "../../Widgets/Coupon/CouponLocations.vue";
import { onMounted } from "vue";
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

const props = defineProps({
  coupon: Object,
  locations: Object,
  couponLocations: Array,
  company: Object,
});

onMounted(() => {
  header.title = props.coupon.type === 'online' ? 'Edit online coupon' : 'Edit location coupon';
});

</script>

<template>

  <Head>
    <title>{{ __("Edit coupon") + ' ' + coupon.name }} </title>
  </Head>
  <div class="card-position">
    <section>
      <div class="mb-4 grid grid-cols-1 gap-4 lg:grid-cols-3">
        <div class="flex flex-col space-y-4 lg:col-span-2">
          <CouponForm :coupon :company />
        </div>
        <div class="flex flex-col space-y-4 max-sm:mb-2 lg:col-span-1">
          <CouponLocations :locations :couponLocations :coupon :company v-if="coupon.type === 'location'" />
        </div>
      </div>
    </section>
  </div>
</template>
