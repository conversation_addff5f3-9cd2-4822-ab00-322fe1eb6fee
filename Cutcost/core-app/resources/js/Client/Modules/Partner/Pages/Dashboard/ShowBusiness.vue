<script setup>
import { Head, router } from "@inertiajs/vue3";
import Dashboard from "../../Widgets/Dashboard/Dashboard.vue";
import { Companies } from "@m/Public";
import { useGlobalStore } from "@s";
import { onMounted, onUnmounted, watch, inject } from "vue";
import { useHeaderStore } from "@m/Fixed";

const route = inject("route");

const global = useGlobalStore();

const header = useHeaderStore();

const props = defineProps({
    companies_count: Number,
});

const options = [
    { id: 'business', name: 'Business' },
    { id: 'referral', name: 'Referral' },
];

onMounted(() => {
    header.navigation = 'dashboard';
    header.options = options;
    header.chosenTab = 'business';
    header.swipeEvent();
});

watch(() => header.chosenTab, (val) => {
    if (val === 'referral')
        router.get(route('partner.referral.dashboard'));
});

onUnmounted(() => {
    header.clear();
});

</script>
<template>

    <Head>
        <title>{{ __("Business dashboard") }} </title>
    </Head>
    <div class="card-position">
        <section class="mt-4">
            <Dashboard :companies_count />
            <Companies type="company_user" :data="{ user: global.user.id }" />
        </section>
    </div>
</template>
