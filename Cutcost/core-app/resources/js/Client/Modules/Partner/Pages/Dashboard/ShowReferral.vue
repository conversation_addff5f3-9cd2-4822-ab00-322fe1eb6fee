<script setup>
import { Head, router } from "@inertiajs/vue3";
import { onMounted, onUnmounted, watch, inject, ref, computed } from "vue";
import { useHeaderStore } from "@m/Fixed";
import { Share } from "@s";
import QRCode from "qrcode";

import {
    PhCopy,
    PhUsers,
    PhShieldCheck,
    PhTrendUp,
    PhCoins,
    PhLink,
    PhQrCode,
    PhCrown,
    PhGift,
    PhUser,
    PhCheck,
    PhClock,
} from "@phosphor-icons/vue";

const route = inject("route");
const header = useHeaderStore();

// Reactive data
const showQRCode = ref(false);
const selectedTransactionType = ref('all');
const selectedTimeRange = ref('30');

const props = defineProps({
    referral_code: String,
    referral_url: String,
    stats: Object,
    recent_referrals: Array,
    recent_transactions: Array,
    monthly_earnings: Object,
    referral_chain: Array,
    user_cuts_balance: Number,
});

const options = [
    { id: 'business', name: 'Business' },
    { id: 'referral', name: 'Referral' },
];

// Computed properties
const filteredTransactions = computed(() => {
    if (!props.recent_transactions) return [];

    let filtered = props.recent_transactions;

    if (selectedTransactionType.value !== 'all') {
        filtered = filtered.filter(t => t.type === selectedTransactionType.value);
    }

    const days = parseInt(selectedTimeRange.value);
    if (days > 0) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        filtered = filtered.filter(t => new Date(t.created_at) >= cutoffDate);
    }

    return filtered;
});

const transactionTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'registration', label: 'Registration' },
    { value: 'verification', label: 'Verification' },
    { value: 'first_coupon', label: 'First Coupon' },
    { value: 'coupon_scan', label: 'Coupon Scan' },
    { value: 'purchase', label: 'Purchase' },
];

const timeRanges = [
    { value: '7', label: 'Last 7 days' },
    { value: '30', label: 'Last 30 days' },
    { value: '90', label: 'Last 90 days' },
    { value: '0', label: 'All time' },
];

const getStatusIcon = (status) => {
    if (status.first_coupon_used) return PhCheck;
    if (status.verified) return PhShieldCheck;
    return PhClock;
};

const getStatusColor = (status) => {
    if (status.first_coupon_used) return 'text-green-500';
    if (status.verified) return 'text-blue-500';
    return 'text-yellow-500';
};

const getTransactionIcon = (type) => {
    const icons = {
        registration: PhUser,
        verification: PhShieldCheck,
        first_coupon: PhGift,
        coupon_scan: PhQrCode,
        purchase: PhCoins,
    };
    return icons[type] || PhCoins;
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat().format(amount);
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
};

const qrPreview = ref('');

onMounted(async () => {
    header.navigation = 'dashboard';
    header.options = options;
    header.chosenTab = 'referral';
    header.swipeEvent();

    qrPreview.value = await getQRCode();
});

watch(() => header.chosenTab, (val) => {
    if (val === 'business')
        router.get(route('partner.business.dashboard'));
});

onUnmounted(() => {
    header.clear();
});

const url = route("partner.referral.dashboard", { referral_code: props.referral_code });

const getQRCode = async () => {
    return await QRCode.toDataURL(url, {
        errorCorrectionLevel: "H",
        margin: 1,
        width: 500,
        color: { dark: "#1a1a1a", light: "#ffffff" },
    });
};

</script>
<template>

    <Head>
        <title>{{ __("Referral Dashboard") }}</title>
    </Head>

    <div class="card-position">
        <section class="pt-2 m-2">
            <div class="grid grid-cols-2 lg:grid-cols-4  gap-4">
                <Stats :title="__('Total Referrals')" :value="stats?.direct_referrals || 0">
                    <PhUsers :size="24" />
                </Stats>
                <Stats :title="__('Verified Users')" :value="stats?.verified_referrals || 0">
                    <PhShieldCheck :size="24" />
                </Stats>
                <Stats :title="__('Active Users')" :value="stats?.active_referrals || 0">
                    <PhTrendUp :size="24" />
                </Stats>
                <Stats :title="__('Total Cuts Earned')" :value="stats?.total_cuts_earned || 0">
                    <PhCoins :size="24" />
                </Stats>
            </div>
        </section>

        <!-- Referral Code & Link Section -->
        <section class="mt-6">
            <Card :title="__('Your Referral Code')">
                <div class="p-4 space-y-4">
                    <!-- Referral Code Display -->
                    <div class="flex items-center justify-between p-4 bg-reverse rounded-lg">
                        <div class="flex items-center gap-3">
                            <PhLink :size="20" class="text-muted" />
                            <span class="font-mono text-lg text-secondary font-semibold">{{ referral_code }}</span>
                        </div>
                        <Share copy :text="referral_code" :toast="__('Referral code copied!')">
                            <x-button intent="rounded">
                                <PhCopy :size="16" />
                                {{ __('Copy code') }}
                            </x-button>
                        </Share>
                    </div>

                    <div class="flex flex-col gap-4 p-4 bg-reverse rounded-lg">
                        <div class="flex items-center gap-3 flex-1 min-w-0">
                            <PhLink :size="20" class="text-muted flex-shrink-0" />
                            <span class="text-sm text-secondary truncate">{{ referral_url }}</span>
                        </div>
                        <div class="flex gap-2 ml-auto">
                            <Share :url="referral_url" :toast="__('Referral link copied!')">
                                <x-button intent="rounded">
                                    <PhCopy :size="16" />
                                    {{ __('Copy Link') }}
                                </x-button>
                            </Share>
                            <x-button @click="$refs.qrModal.modalOpen = true" intent="rounded">
                                <PhQrCode :size="16" />
                                {{ __('QR Code') }}
                            </x-button>
                        </div>
                    </div>

                    <Modal ref="qrModal" @click="showQRCode = false" :title="__('QR Code')">
                        <div class="text-center">
                            <div class="bg-reverse p-8 rounded-lg mb-4">
                                <img :src="qrPreview" alt="QR Code" class=" object-contain w-full" />
                                <p class="text-sm text-muted mt-2">{{ __('QR Code for') }}<br>{{ referral_url
                                }}
                                </p>
                            </div>
                        </div>
                    </Modal>

                    <!-- Reward Structure Info -->
                    <div
                        class="mt-4 p-4 bg-reverse  rounded-lg">
                        <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2">
                             {{ __('Multi-Level Reward Program') }}
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div class="flex items-center gap-2 text-sm">
                                <PhCrown :size="16" class="text-yellow-500" />
                                <span><strong>{{ __('Level 1:') }}</strong> +10 {{ __('cuts') }}</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <PhGift :size="16" class="text-blue-500" />
                                <span><strong>{{ __('Level 2:') }}</strong> +3 {{ __('cuts') }}</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <PhTrendUp :size="16" class="text-green-500" />
                                <span><strong>{{ __('Level 3:') }}</strong> +1 {{ __('cut') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Level Breakdown -->
        <section class="mt-6" v-if="stats?.cuts_by_level">
            <Card :title="__('Earnings by Level')">
                <div class="p-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                            <PhCrown :size="32" class="text-yellow-500 mx-auto mb-2" />
                            <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                                {{ formatCurrency(stats.cuts_by_level[1] || 0) }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">{{ __('Level 1 Earnings') }}</div>
                        </div>
                        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <PhGift :size="32" class="text-blue-500 mx-auto mb-2" />
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                {{ formatCurrency(stats.cuts_by_level[2] || 0) }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">{{ __('Level 2 Earnings') }}</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <PhTrendUp :size="32" class="text-green-500 mx-auto mb-2" />
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                                {{ formatCurrency(stats.cuts_by_level[3] || 0) }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">{{ __('Level 3 Earnings') }}</div>
                        </div>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Recent Referrals -->
        <section class="mt-6" v-if="recent_referrals?.length">
            <Card :title="__('Recent Referrals')">
                <div class="p-4">
                    <div class="space-y-3">
                        <div v-for="referral in recent_referrals.slice(0, 5)" :key="referral.id"
                            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    {{ referral.user.name.charAt(0).toUpperCase() }}
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ referral.user.name }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ __('Joined') }} {{ formatDate(referral.registered_at) }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <component :is="getStatusIcon(referral.status)" :size="20"
                                    :class="getStatusColor(referral.status)" />
                                <span class="text-sm" :class="getStatusColor(referral.status)">
                                    {{ referral.status.first_coupon_used ? __('Active') :
                                        referral.status.verified ? __('Verified') : __('Registered') }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div v-if="recent_referrals.length > 5" class="mt-4 text-center">
                        <button class="text-blue-500 hover:text-blue-600 text-sm font-medium">
                            {{ __('View All Referrals') }} ({{ recent_referrals.length }})
                        </button>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Transaction History -->
        <section class="mt-6" v-if="recent_transactions?.length">
            <Card :title="__('Transaction History')">
                <div class="p-4">
                    <!-- Filters -->
                    <div class="flex flex-wrap gap-4 mb-4">
                        <select v-model="selectedTransactionType"
                            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm">
                            <option v-for="type in transactionTypes" :key="type.value" :value="type.value">
                                {{ type.label }}
                            </option>
                        </select>
                        <select v-model="selectedTimeRange"
                            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm">
                            <option v-for="range in timeRanges" :key="range.value" :value="range.value">
                                {{ range.label }}
                            </option>
                        </select>
                    </div>

                    <!-- Transactions List -->
                    <div class="space-y-3">
                        <div v-for="transaction in filteredTransactions.slice(0, 10)" :key="transaction.id"
                            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                    <component :is="getTransactionIcon(transaction.type)" :size="20"
                                        class="text-green-600 dark:text-green-400" />
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ transaction.description }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ formatDate(transaction.created_at) }}
                                        <span v-if="transaction.source_user" class="ml-2">
                                            {{ __('from') }} {{ transaction.source_user.name }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-semibold text-green-600 dark:text-green-400">
                                    +{{ formatCurrency(transaction.amount) }} {{ __('cuts') }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ __('Level') }} {{ transaction.level }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-if="filteredTransactions.length === 0" class="text-center py-8">
                        <PhCoins :size="48" class="text-gray-400 mx-auto mb-4" />
                        <p class="text-gray-500 dark:text-gray-400">{{ __('No transactions found') }}</p>
                    </div>

                    <!-- View More -->
                    <div v-if="filteredTransactions.length > 10" class="mt-4 text-center">
                        <button class="text-blue-500 hover:text-blue-600 text-sm font-medium">
                            {{ __('View All Transactions') }} ({{ filteredTransactions.length }})
                        </button>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Current Balance -->
        <section class="mt-6">
            <Card :title="__('Current Balance')">
                <div class="p-4">
                    <div
                        class="text-center p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg">
                        <PhCoins :size="48" class="text-yellow-500 mx-auto mb-4" />
                        <div class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                            {{ formatCurrency(user_cuts_balance || 0) }} {{ __('cuts') }}
                        </div>
                        <p class="text-gray-600 dark:text-gray-400">{{ __('Available for withdrawal') }}</p>
                        <button v-if="user_cuts_balance >= 100"
                            class="mt-4 px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition">
                            {{ __('Withdraw Cuts') }}
                        </button>
                        <p v-else class="mt-4 text-sm text-gray-500">
                            {{ __('Minimum 100 cuts required for withdrawal') }}
                        </p>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Empty State for New Users -->
        <section v-if="!stats?.direct_referrals" class="mt-6">
            <Card :title="__('Get Started')">
                <div class="p-6 text-center">
                    <PhUsers :size="64" class="text-gray-400 mx-auto mb-4" />
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        {{ __('Start Earning with Referrals') }}
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        {{ __('Share your referral link and earn cuts for every user who joins through your link!') }}
                    </p>
                    <div class="space-y-3 text-left max-w-md mx-auto">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                            </div>
                            <span class="text-sm">{{ __('Copy your referral link above') }}</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div
                                class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                <span class="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
                            </div>
                            <span class="text-sm">{{ __('Share with friends and family') }}</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div
                                class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                                <span class="text-purple-600 dark:text-purple-400 font-semibold text-sm">3</span>
                            </div>
                            <span class="text-sm">{{ __('Earn cuts when they join and use coupons') }}</span>
                        </div>
                    </div>
                </div>
            </Card>
        </section>
    </div>
</template>
