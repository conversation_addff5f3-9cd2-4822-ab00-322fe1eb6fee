<script setup>
import { onMounted, ref } from "vue";
import { Head } from "@inertiajs/vue3";
import LocationForm from "../../Widgets/Location/LocationForm.vue";
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

const props = defineProps({
  company: Object,
  locales: Object,
});

onMounted(() => {
  header.title = 'Create location';
});

</script>
<template>

  <Head :title="__('Create location') "> </Head>

  <section class="card-position">
    <LocationForm create :locales="locales" :company="company" />
  </section>
</template>
