<script setup>
import LocationForm from "../../Widgets/Location/LocationForm.vue";
import { Head } from "@inertiajs/vue3";
import { onMounted, onUnmounted } from "vue";
import { useHeaderStore } from "@m/Fixed";
import LocationCoupons from "../../Widgets/Location/LocationCoupons.vue";

const header = useHeaderStore();

const props = defineProps({
  location: Object,
  coupons: Object,
  company: Object,
  locationCoupons: Array,
});

onMounted(() => {
  header.title = 'Edit location';
});

onUnmounted(() => {
  header.clear();
});

</script>

<template>
  <div class="card-position">
    <Head> 
      <title>{{ __("Edit location") + ' ' + location.name }} </title>
    </Head>
    <div class="flex flex-col gap-2 lg:flex-row">
      <div class="flex w-full flex-col lg:w-7/10">
        {{ company.country }}
        <LocationForm :location :company />
      </div>
      <div class="flex w-full flex-col space-y-2  lg:w-3/10">
        <LocationCoupons :coupons :location :locationCoupons :company />
      </div>
    </div>
  </div>
</template>
