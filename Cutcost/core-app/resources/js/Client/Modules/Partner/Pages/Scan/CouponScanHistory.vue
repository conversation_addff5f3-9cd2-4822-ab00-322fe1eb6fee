<script setup>
import { Head } from "@inertiajs/vue3";
import { inject } from "vue";

const route = inject("route");

defineProps({
  scans: Object,
});

</script>
<template>

  <Head>
    <title>{{ __("Scan history") }} </title>
  </Head>
  <div class="card-position">
    <div class="flex">
      <x-button as="Link" :href="route('partner.coupons.index')" class="mb-2">
        {{ __("Back") }}
      </x-button>
    </div>
    <Card :title="__('Scan history')">
      <x-table :headers="[
        __('User'),
        __('Type'),
        __('Earnings'),
        __('Coupon'),
        __('Created at'),
      ]">
        <tr v-for="scan in scans.data">
          <td>
            <x-button class="hover:!text-secondary !text-sm" intent="link" as="Link"
              :href="route('profile', { user: scan.user.nickname })">{{ scan.user.display_name.slice(0, 18)
              }}</x-button>
          </td>
          <td>{{ scan.coupon_type }}</td>
          <td>{{ scan.earnings }}</td>
          <td>
            <x-button class="hover:!text-secondary !text-sm" intent="link" as="Link" :href="route('partner.coupons.edit', {
              coupon: scan.coupon.slug,
            })
              ">{{
                scan.coupon.name
              }}</x-button>
          </td>
          <td>{{ new Date(scan.created_at).toLocaleString() }}</td>
        </tr>
      </x-table>
      <Paginator :pagination="scans" />
    </Card>
  </div>
</template>
