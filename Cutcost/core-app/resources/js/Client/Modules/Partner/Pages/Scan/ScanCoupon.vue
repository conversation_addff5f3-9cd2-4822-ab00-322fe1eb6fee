<script setup>
import { onMounted, ref } from "vue";
import { QrcodeStream } from "vue-qrcode-reader";
// import { router } from "@inertiajs/vue3";
import { inject } from "vue";
import ScanResult from "../../Widgets/ScanCoupon/ScanResult.vue";
import { useHeaderStore } from "@m/Fixed";

const route = inject("route");

const header = useHeaderStore();

const template = {
    couponsInOwnership: 0,
    status: "",
    builder: {},
    coupon: {},
    user: {},
    message: "",
}

const scan = ref({ ...template });
const detectedQr = ref("");

const props = defineProps({
    company: Object,
});


onMounted(async () => {

    header.chosenCompany = props.company;

    // for testing

    // const response = await axios.post(route("partner.scan.check"), {
    //     qr: scan.qr,
    // });

    // scan.value = response?.data;
});

const onDetect = async (detectedCodes) => {
    try {
        // console.log('detect');
        detectedQr.value = detectedCodes[0].rawValue;

        const response = await axios.post(route("partner.scan.check", { company: props.company.slug }), {
            qr: detectedQr.value,
        });

        console.log(response?.data);

        scan.value = response?.data;

    } catch (err) {
        console.error(err);
    }
};

const accept = async () => {
    try {
        const response = await axios.post(route("partner.scan.accept", { company: props.company.slug }), {
            qr: detectedQr.value,
        });

        scan.value = response?.data;
        detectedQr.value = "";
    } catch (err) {
        console.error(err);
    }
};

const onError = (error) => {
    console.log(error);
};
</script>
<template>

    <Head :title="__('Scan') "> </Head>
    <div class="card-position space-y-3">
        <Card>
            <qrcode-stream @detect="onDetect" @error="onError"></qrcode-stream>
            <strong class="h2 mt-4 mb-2 flex justify-center uppercase">{{
                __('Scan')
            }}</strong>
        </Card>

        <section v-if="scan?.status">
            <ScanResult :scan="scan" />
            <x-button class="flex mt-2 ml-auto" @click="accept" v-if="scan.status === 'success' && scan?.coupon?.id">
                {{ __("Accept") }}
            </x-button>
        </section>
    </div>
</template>
