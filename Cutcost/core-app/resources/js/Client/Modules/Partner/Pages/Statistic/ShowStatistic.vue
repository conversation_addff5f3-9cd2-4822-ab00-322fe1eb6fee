<script setup>
import { Head, router } from "@inertiajs/vue3";
import { ref, computed, onMounted, inject } from "vue";
import { useGlobalStore, useToastStore } from "@s";
import {
    PhChatCircle,
    PhUsers,
    PhCoins,
    PhTrendUp,
    PhStar,
    PhDownload,
    PhActivity,
    PhCrown,
    PhTicket
} from "@phosphor-icons/vue";

const route = inject("route");
const global = useGlobalStore();
const toast = useToastStore();

// Reactive data
const selectedCompany = ref('all');
const selectedRange = ref('30');

const props = defineProps({
    companies: Array,
    selected_company_id: [String, Number],
    date_range: String,
    overview_stats: Object,
    chat_metrics: Object,
    coupon_stats: Object,
    time_analytics: Object,
    top_performers: Object,
    recent_activity: Array,
});

// Computed properties
const filteredCompanies = computed(() => {
    return [
        { id: 'all', name: 'All Companies' },
        ...props.companies
    ];
});

const chartData = computed(() => {
    if (!props.time_analytics) return [];

    const periods = Object.keys(props.time_analytics.chats_over_time || {});
    return periods.map(period => ({
        period,
        chats: props.time_analytics.chats_over_time[period] || 0,
        messages: props.time_analytics.messages_over_time[period] || 0,
        interactions: props.time_analytics.interactions_over_time[period] || 0,
        earnings: props.time_analytics.earnings_over_time[period] || 0,
    }));
});

const peakHour = computed(() => {
    if (!props.chat_metrics?.active_chat_hours) return null;

    const hours = props.chat_metrics.active_chat_hours;
    const peak = hours.reduce((max, hour) =>
        hour.count > max.count ? hour : max, hours[0] || { hour: 0, count: 0 });

    return peak.hour;
});

// Methods
const updateFilters = () => {
    const params = new URLSearchParams();

    if (selectedRange.value !== '30') {
        params.set('range', selectedRange.value);
    }

    if (selectedCompany.value !== 'all') {
        params.set('company_id', selectedCompany.value);
    }

    const queryString = params.toString();
    const url = route('partner.statistic.index') + (queryString ? '?' + queryString : '');

    router.get(url);
};

const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount || 0);
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getActivityIcon = (type) => {
    return type === 'chat_started' ? PhChatCircle : PhTicket;
};

const exportData = () => {

 const data = {
  overview_stats: props.overview_stats,
  chat_metrics: props.chat_metrics,
  coupon_stats: props.coupon_stats,
  time_analytics: props.time_analytics,
  top_performers: props.top_performers,
  recent_activity: props.recent_activity,
  companies: props.companies,
  selected_company_id: props.selected_company_id,
  date_range: props.date_range,
  exported_at: new Date().toISOString(),
};


  const fileName = 'export.json';
  const json = JSON.stringify(data, null, 2);
  const blob = new Blob([json], { type: 'application/json' });

  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  link.click();

  URL.revokeObjectURL(link.href);
  
  toast.add({
    type: 'success',
    title: 'Export complete',
    description: `File "${fileName}" downloaded successfully`
  });
};


// Initialize filters from props
onMounted(() => {
    if (props.selected_company_id) {
        selectedCompany.value = props.selected_company_id.toString();
    }
    if (props.date_range) {
        selectedRange.value = props.date_range;
    }
});
</script>

<template>
    <Head>
        <title>{{ __("Business Analytics") }}</title>
    </Head>

    <div class="card-position">
        <!-- Header with Filters -->
        <section class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ __('Business Analytics') }}
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        {{ __('Track your chat engagement and coupon performance') }}
                    </p>
                </div>

                <div class="flex flex-wrap gap-3">
                    <!-- Company Filter -->
                    <select
                        v-model="selectedCompany"
                        @change="updateFilters"
                        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm min-w-[150px]"
                    >
                        <option v-for="company in filteredCompanies" :key="company.id" :value="company.id">
                            {{ company.name }}
                        </option>
                    </select>

                    <!-- Date Range Filter -->
                    <select
                        v-model="selectedRange"
                        @change="updateFilters"
                        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm"
                    >
                        <option value="7">{{ __('Last 7 days') }}</option>
                        <option value="30">{{ __('Last 30 days') }}</option>
                        <option value="90">{{ __('Last 90 days') }}</option>
                        <option value="365">{{ __('Last year') }}</option>
                    </select>

                    <!-- Export Button -->
                    <button
                        @click="exportData"
                        class="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition text-sm"
                    >
                        <PhDownload :size="16" />
                        {{ __('Export') }}
                    </button>
                </div>
            </div>
        </section>

        <!-- Overview Statistics -->
        <section class="mb-8">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <Stats :title="__('Total Chats')" :value="overview_stats?.total_chats || 0">
                    <PhChatCircle :size="24" class="text-blue-500" />
                </Stats>
                <Stats :title="__('Active Conversations')" :value="overview_stats?.active_conversations || 0">
                    <PhUsers :size="24" class="text-green-500" />
                </Stats>
                <Stats :title="__('Coupon Interactions')" :value="overview_stats?.coupon_interactions || 0">
                    <PhTicket :size="24" class="text-purple-500" />
                </Stats>
                <Stats :title="__('Total Earnings')" :value="formatCurrency(overview_stats?.total_earnings || 0)">
                    <PhCoins :size="24" class="text-yellow-500" />
                </Stats>
            </div>
        </section>

        <!-- Key Metrics Row -->
        <section class="mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Chat Engagement -->
                <Card :title="__('Chat Engagement')">
                    <div class="p-4 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Messages per Chat') }}</span>
                            <span class="font-semibold">{{ chat_metrics?.messages_per_chat || 0 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('User Engagement Rate') }}</span>
                            <span class="font-semibold text-green-600">{{ chat_metrics?.user_engagement_rate || 0 }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Peak Hour') }}</span>
                            <span class="font-semibold">{{ peakHour !== null ? peakHour + ':00' : 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Avg Response Time') }}</span>
                            <span class="font-semibold">{{ chat_metrics?.avg_response_time || 'N/A' }}</span>
                        </div>
                    </div>
                </Card>

                <!-- Coupon Performance -->
                <Card :title="__('Coupon Performance')">
                    <div class="p-4 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Total Coupons') }}</span>
                            <span class="font-semibold">{{ coupon_stats?.total_coupons || 0 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Active Coupons') }}</span>
                            <span class="font-semibold text-green-600">{{ coupon_stats?.active_coupons || 0 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Total Views') }}</span>
                            <span class="font-semibold">{{ formatNumber(coupon_stats?.coupon_views || 0) }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Conversion Rate') }}</span>
                            <span class="font-semibold text-blue-600">{{ coupon_stats?.coupon_conversion_rate || 0 }}%</span>
                        </div>
                    </div>
                </Card>

                <!-- Growth Metrics -->
                <Card :title="__('Growth Metrics')">
                    <div class="p-4 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('New Customers') }}</span>
                            <span class="font-semibold text-green-600">{{ overview_stats?.new_customers || 0 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Total Messages') }}</span>
                            <span class="font-semibold">{{ formatNumber(overview_stats?.total_messages || 0) }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Chat to Interaction Rate') }}</span>
                            <span class="font-semibold text-purple-600">{{ overview_stats?.conversion_rate || 0 }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Revenue Growth') }}</span>
                            <span class="font-semibold text-green-600 flex items-center gap-1">
                                <PhTrendUp :size="16" />
                                {{ __('Positive') }}
                            </span>
                        </div>
                    </div>
                </Card>
            </div>
        </section>

        <!-- Time-based Analytics Charts -->
        <section class="mb-8" v-if="chartData.length > 0">
            <Card :title="__('Activity Over Time')">
                <div class="p-4">
                    <!-- Simple chart representation -->
                    <div class="space-y-6">
                        <!-- Chats Chart -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">{{ __('Chats Started') }}</h4>
                            <div class="flex items-end gap-2 h-20">
                                <div
                                    v-for="(data, index) in chartData.slice(-7)"
                                    :key="index"
                                    class="flex-1 bg-blue-200 dark:bg-blue-800 rounded-t"
                                    :style="{ height: Math.max((data.chats / Math.max(...chartData.map(d => d.chats))) * 100, 5) + '%' }"
                                    :title="`${data.period}: ${data.chats} chats`"
                                ></div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">{{ __('Messages Sent') }}</h4>
                            <div class="flex items-end gap-2 h-20">
                                <div
                                    v-for="(data, index) in chartData.slice(-7)"
                                    :key="index"
                                    class="flex-1 bg-green-200 dark:bg-green-800 rounded-t"
                                    :style="{ height: Math.max((data.messages / Math.max(...chartData.map(d => d.messages))) * 100, 5) + '%' }"
                                    :title="`${data.period}: ${data.messages} messages`"
                                ></div>
                            </div>
                        </div>

                        <!-- Interactions Chart -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">{{ __('Coupon Interactions') }}</h4>
                            <div class="flex items-end gap-2 h-20">
                                <div
                                    v-for="(data, index) in chartData.slice(-7)"
                                    :key="index"
                                    class="flex-1 bg-purple-200 dark:bg-purple-800 rounded-t"
                                    :style="{ height: Math.max((data.interactions / Math.max(...chartData.map(d => d.interactions))) * 100, 5) + '%' }"
                                    :title="`${data.period}: ${data.interactions} interactions`"
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Top Performers -->
        <section class="mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Top Companies -->
                <Card :title="__('Top Performing Companies')" v-if="top_performers?.top_companies?.length">
                    <div class="p-4">
                        <div class="space-y-3">
                            <div
                                v-for="(company, index) in top_performers.top_companies"
                                :key="company.id"
                                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                            >
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                        {{ index + 1 }}
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ company.name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ company.interactions }} {{ __('interactions') }}
                                        </div>
                                    </div>
                                </div>
                                <PhCrown v-if="index === 0" :size="20" class="text-yellow-500" />
                            </div>
                        </div>
                    </div>
                </Card>

                <!-- Most Active Users -->
                <Card :title="__('Most Active Users')" v-if="top_performers?.most_active_users?.length">
                    <div class="p-4">
                        <div class="space-y-3">
                            <div
                                v-for="(record, index) in top_performers.most_active_users"
                                :key="record.user.id"
                                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                            >
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                        {{ record.user.name.charAt(0).toUpperCase() }}
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ record.user.name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ record.message_count }} {{ __('messages') }}
                                        </div>
                                    </div>
                                </div>
                                <PhStar v-if="index === 0" :size="20" class="text-yellow-500" />
                            </div>
                        </div>
                    </div>
                </Card>

                <!-- Top Coupons -->
                <Card :title="__('Top Performing Coupons')" v-if="coupon_stats?.top_coupons?.length">
                    <div class="p-4">
                        <div class="space-y-3">
                            <div
                                v-for="(coupon, index) in coupon_stats.top_coupons"
                                :key="coupon.id"
                                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                            >
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                        {{ index + 1 }}
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                            {{ coupon.name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ coupon.views }} {{ __('views') }} • {{ coupon.interactions }} {{ __('interactions') }}
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-green-600">{{ coupon.discount }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </section>

        <!-- Recent Activity -->
        <section class="mb-8" v-if="recent_activity?.length">
            <Card :title="__('Recent Activity')">
                <div class="p-4">
                    <div class="space-y-3">
                        <div
                            v-for="activity in recent_activity.slice(0, 10)"
                            :key="activity.created_at"
                            class="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                        >
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                <component :is="getActivityIcon(activity.type)" :size="20" class="text-blue-600 dark:text-blue-400" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ activity.description }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <span v-if="activity.user">{{ activity.user.name }}</span>
                                    <span v-if="activity.user && activity.company"> • </span>
                                    <span v-if="activity.company">{{ activity.company.name }}</span>
                                    <span v-if="activity.coupon_name"> • {{ activity.coupon_name }}</span>
                                </div>
                            </div>
                            <div class="text-right">
                                <div v-if="activity.earnings" class="text-sm font-semibold text-green-600">
                                    {{ formatCurrency(activity.earnings) }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ formatDate(activity.created_at) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Empty State -->
        <section v-if="!overview_stats?.total_chats" class="text-center py-12">
            <PhActivity :size="64" class="text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {{ __('No Activity Yet') }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ __('Start engaging with customers through chat to see your analytics here.') }}
            </p>
            <button
                @click="router.get(route('partner.companies.create'))"
                class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
            >
                {{ __('Create Your First Company') }}
            </button>
        </section>
    </div>
</template>
