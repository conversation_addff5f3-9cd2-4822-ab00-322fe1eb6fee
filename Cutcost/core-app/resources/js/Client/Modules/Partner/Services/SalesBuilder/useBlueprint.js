import { useEditorStore } from "../../Stores/useEditorStore";

// model is event, condition, action

export const useBlueprint = () => {
  const editorStore = useEditorStore();

  const addModelToBlueprint = (model) => {
    model.id = Date.now().toString();
    editorStore.blueprint.push(model);
  };

  const removeModelFromBlueprint = (model) => {
    editorStore.blueprint = editorStore.blueprint.filter(
      (item) => item.id !== model.id,
    );
  };

  return {
    addModelToBlueprint,
    removeModelFromBlueprint,
  };
};
