import { defineStore } from "pinia";
import { ref, watch } from "vue";

export const useOpenHoursStore = defineStore("openHours", () => {
  const modal = ref(null);
  const editIndex = ref(null);

  const groups = ref([]);

  const resetGroupState = () => {
    groupState.value = {
      type: "work_days",
      weekdays: [],
      timeStart: null,
      timeEnd: null,
      breaks: [],
      open24: false,
    };
  };

  const groupState = ref({
    type: "work_days",
    weekdays: [],
    timeStart: null,
    timeEnd: null,
    breaks: [],
    open24: false,
  });

  const weekOrder = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  const availableDays = ref([
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ]);

  const sortList = () => {
    groups.value.sort((a, b) => {
      const aIndex = weekOrder.indexOf(a.weekdays[0]);
      const bIndex = weekOrder.indexOf(b.weekdays[0]);
      return aIndex - bIndex;
    });
  };

  // check if day or days are taken by other group
  const takenByOther = (days) => {
    const dayList = Array.isArray(days) ? days : [days];

    const checkGroup = (group) =>
      dayList.some((day) => group.weekdays.includes(day));

    if (editIndex.value === false) {
      // Check all groups
      return groups.value.some(checkGroup);
    } else {
      // Check all except the one being edited
      return groups.value.some((group, index) => {
        if (index === editIndex.value) return false;
        return checkGroup(group);
      });
    }
  };

  watch(
    () => groupState.value.open24,
    (val) => {
      if (val) {
        groupState.value.type = "work_days";
        groupState.value.weekdays = [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday",
        ];
        groupState.value.timeStart = null;
        groupState.value.timeEnd = null;
        groupState.value.breaks = [];
      } else {
        groupState.value.weekdays = [];
      }
    },
  );

  return {
    sortList,
    editIndex,
    groups,
    modal,
    groupState,
    availableDays,
    weekOrder,
    takenByOther,
    resetGroupState,
  };
});
