<script setup>
import { useForm } from "@inertiajs/vue3";
import { inject } from "vue";

const route = inject("route");


const props = defineProps({
    article: Object,
    company: Object,
});

const articleForm = useForm({
    name: props.article?.name ?? null,
    content: props.article?.content ?? null,
    upload: {
        temp_uuid: null,
        deleted_ids: [],
    },
});

const submit = () => {
    if (props.article?.slug) {
        articleForm.put(
            route("partner.articles.update", {
                article: props.article.slug,
            }),
        );
    } else {
        articleForm.post(route("partner.articles.store", { company: props.company.slug }));
    }
};

</script>
<template>
    <x-form noClass @submit="submit" :form="articleForm" :type="article ? 'update' : 'create'">
        <Card :title="article
            ? __('Edit Article')
            : __('Create Article')
            " :url="route('partner.articles.index', { company: company.slug })">

            <x-input class="mb-2" v-model="articleForm.name" :label="__('Headline') + '*'" :max="64"
                :message="articleForm.errors.name" required />
            <Label class="my-1">{{ __('Content') }}</Label>
            <QuillEditor class="z-20 text-text dark:bg-bg-dark  dark:text-dark-text border-border rounded-2xl border-1"
                theme="bubble" toolbar="essential" v-model:content="articleForm.content" contentType="html" />
            <div class="my-4">
                <x-upload v-model="articleForm.upload" :max="6" :media="article?.media" />
            </div>
        </Card>

    </x-form>
</template>
