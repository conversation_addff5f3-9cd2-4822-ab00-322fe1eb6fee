<script setup>
import { PhCheckCircle } from "@phosphor-icons/vue";
import { useGlobalStore, useTranslation } from "@s";
import { useTemplateRef } from "vue";
import PartnershipForm from "../../../Seller/Widgets/Partner/PartnershipForm.vue";

defineProps({
    application_status: String,
});

const global = useGlobalStore();

const modal = useTemplateRef("modal");
const plansData = useTranslation.__('subscribe_plans');

const planKeys = Object.keys(plansData);

const currentPlan = (key) => {
    return global.isPartner && key === 'start';
}

const openModal = (key) => {
    if (key === 'scale' || key === 'grow' || global.isPartner) return;
    modal.value.modalOpen = true;
}

</script>
<template>
    <section>
        <Modal ref="modal" :title="__('Become a partner')">
            <PartnershipForm :application_status />
        </Modal>
        <div class="text-center mb-8 sm:mb-12 max-w-4xl mx-auto">
            <h1
                class="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-accent dark:text-dark-accent mb-4 tracking-tight leading-tight">
                {{ __("choose_plan_heading") }}
            </h1>
            <p class="text-base sm:text-lg lg:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                {{ __("compare_and_start") }}
            </p>
        </div>

        <div class="mb-12 grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            <div v-for="key in planKeys" :key="key" :class="[
                'flex flex-col rounded-2xl p-4 transition-all duration-300 bg-white dark:bg-gray-800 relative',
                plansData[key].highlight
                    ? 'shadow-2xl border-4 border-indigo-300 dark:border-indigo-600 transform scale-105 z-10'
                    : 'shadow-lg hover:shadow-xl hover:scale-[1.02]'
            ]">
                <div v-if="plansData[key].highlight" class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span
                        class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-bold px-4 py-2 rounded-full shadow-lg">🔥</span>
                </div>

                <div class="text-center mb-6" :class="{ 'mt-2': !plansData[key].highlight }">
                    <h2 :class="[
                        'font-bold mb-3',
                        {
                            'text-xl sm:text-2xl lg:text-2xl': true,
                            'text-indigo-700 dark:text-indigo-300': plansData[key].highlight,
                            [plansData[key].accentColor]: !plansData[key].highlight,
                        },
                    ]">
                        {{ __("subscribe_plans." + key + ".title", plansData[key].title) }}
                    </h2>
                    <div :class="[
                        'font-extrabold mb-3',
                        {
                            'text-2xl sm:text-4xl lg:text-4xl ': true,
                            [plansData[key].accentColor]: true,
                        },
                    ]">
                        {{ __("subscribe_plans." + key + ".price", plansData[key].price) }}
                    </div>
                    <p class="text-sm sm:text-base italic text-gray-500 dark:text-gray-400">
                        {{ __("subscribe_plans." + key + ".description", plansData[key].description) }}
                    </p>
                </div>

                <ul class="space-y-3 mb-8 text-sm flex-grow">
                    <li v-for="feature in plansData[key].features" :key="feature"
                        class="flex items-start text-gray-700 dark:text-gray-300">
                        <span class="text-green-500 mr-3 mt-0.5 flex-shrink-0">
                            <PhCheckCircle :size="20" />
                        </span>
                        <span>{{ feature }}</span>
                    </li>
                </ul>

                <x-button @click="openModal(key)">
                    <span v-if="currentPlan(key)">
                        {{ __("current_plan") }}
                    </span>

                    <span v-else-if="key === 'scale'">
                        SOON 🔥
                    </span>
                    <span v-else-if="key === 'grow'">
                        SOON 🔥
                    </span>
                    <span v-else>
                        {{ __("subscribe_plans." + key + ".button.text", plansData[key]?.button?.text) }}
                    </span>
                </x-button>
            </div>
        </div>
    </section>
</template>
