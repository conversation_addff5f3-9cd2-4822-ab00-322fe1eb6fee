<script setup>

const props = defineProps({
    botForm: {
        type: Object,
        required: true,
    },
});

</script>

<template>
    <div class="w-full">
        <div class="!space-y-2">
            <x-textarea v-model="botForm.chat_created_message" type="text" :label="__('Welcome message')" class="w-full"
                :error="botForm.errors['chat_created_message']" :max="512" :min="1" />
            <x-textarea v-model="botForm.reply_message" type="text" :label="__('Reply first client message')"
                class="w-full" :error="botForm.errors['reply_message']" :max="512" :min="1" />
        </div>
    </div>
</template>
