<script setup>
import { useForm } from "@inertiajs/vue3";
import Socials from "../../Features/Socials.vue";
import { inject } from "vue";
import { useHelpers, useGlobalStore, PaginatedList, useCache } from "@s";
import BusinessHours from "../../Features/BusinessHours.vue";

const route = inject("route");
const global = useGlobalStore();
const { flush } = useCache();

const { getIdsFromObject, countryCodeToEmoji } = useHelpers();

const props = defineProps({
  company: Object,
  industries: {
    type: Array,
    default: () => [],
  },
  currencies: {
    type: Array,
    default: () => [],
  },
  create: Boolean,
});

const companyForm = useForm({
  name: props.company?.name,
  type: props.company?.type ?? "individual",
  description: props.company?.description,
  business_hours: props.company?.business_hours ?? [],
  socials: props.company?.socials ?? [],
  industries: getIdsFromObject(props.company?.industries),
  country_id: props.company?.country_id ?? null,
  currency_id: props.company?.currency_id ?? null,
});

const submitCompany = () => {
  if (props.create) {
    companyForm.post(route("partner.companies.store"), {
      preserveScroll: true,
    });
    flush();
    return;
  }

  companyForm.put(
    route("partner.companies.update", {
      company: props.company.slug,
    }),
    {
      preserveScroll: true,
    },
  );

  flush();
  global.isReload = true;
};

const types = [
  { id: "individual", name: "Individual" },
  { id: "organization", name: "Organization" },
];

</script>
<template>
  <x-form noClass :form="companyForm" @submit="submitCompany" :type="create ? 'create' : 'update'">
    <Card back class="mb-2">
      <div class="space-y-2">
        <x-input :max="64" :label="__('Title') + '*'" v-model="companyForm.name" :message="companyForm.errors.name"
          required />
        <x-select v-model="companyForm.type" :data="types" :label="__('Business type') + '*'"
          :message="companyForm.errors.type" required :disabled="!create" />
        <div class="sm:grid sm:grid-cols-2 space-y-2 sm:space-y-0">
          <div>
            <PaginatedList :label="__('Country')" :help="{ text: __('Choose worldwide if your business is global.') }"
              lang v-model="companyForm.country_id" type="country_search" required>
              <template #default="{ item }">
                <div class="p-3">
                  {{ countryCodeToEmoji(item.code) }} {{ __(item.name) }}
                </div>
              </template>
            </PaginatedList>
          </div>
          <div>
            <x-select :label="__('Currency') + '*'" :data="currencies" v-model="companyForm.currency_id"
              :message="companyForm.errors.currency_id" search lang />
          </div>
        </div>
      </div>
    </Card>
    <Card>
      <div class="mb-4">
        <x-textarea :label="__('Description')" v-model="companyForm.description"
          :message="companyForm.errors.description" :max="400" />
      </div>
      <div class="flex my-2">
        <BusinessHours v-model="companyForm.business_hours" />
      </div>
      <div>
        <x-select :label="__('Category') + '*'" :data="industries" v-model="companyForm.industries"
          :message="companyForm.errors.industries" search lang />
      </div>

      <div class="mt-4">
        <Socials v-model="companyForm.socials" />
      </div>
    </Card>
  </x-form>
</template>
