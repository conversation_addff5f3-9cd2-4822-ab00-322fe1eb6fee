<script setup>

import { useForm } from "@inertiajs/vue3";
import { inject } from "vue";

const route = inject("route");

const props = defineProps({
    locations: Object,
    coupon: Object,
    couponLocations: Array,
    company: Object,
});

const locationForm = useForm({
    locations: props.couponLocations ?? [],
});

const saveLocations = () => {
    locationForm.patch(
        route("partner.coupon.update.locations", {
            coupon: props.coupon.slug,
        }),
        { preserveScroll: true },
    );
};

const isLocationExists = () => {
    return Object.values(props.locations).length;
};
</script>
<template>
    <Card :title="__('Locations')" class="w-full">
        <div v-if="isLocationExists">
            <x-select :placeholder="__('Select locations')" multiple v-model="locationForm.locations" :data="locations"
                :message="locationForm.errors.locations" />
            <div class="flex justify-end mt-2">
                <x-button @click="saveLocations" type="button">{{
                    __("Update")
                    }}</x-button>
            </div>
        </div>
        <div class="flex flex-col  " v-if="!isLocationExists">
            <div class="text-center">
                {{ __("Nothing") }}
            </div>
            <div class="justify-end flex mt-4">
                <x-button as="Link" :href="route('partner.locations.create', { company: company.slug })">
                    {{ __("Create") }}
                </x-button>
            </div>


        </div>
    </Card>
</template>
