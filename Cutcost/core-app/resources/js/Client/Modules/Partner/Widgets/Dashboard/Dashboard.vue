<script setup>

import { Ph<PERSON>hartLineUp } from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";

defineProps({
    companies_count: Number,
});

const global = useGlobalStore();

</script>

<template>
    <div class="flex items-center p-2 bg-secondary justify-between ">
        <div class="uppercase">
            {{ global.userRoles[0] }}
        </div>
        <div class="flex gap-1">
            <x-button intent="rounded" :href="route('partner.statistic.index')">
                <PhChartLineUp :size="20" />
                {{ __("Analytics") }}
            </x-button>
            <x-button intent="rounded" :href="route('plans')">
                {{ __("Upgrade now") }}
            </x-button>
        </div>
    </div>
    <div class="flex text justify-evenly my-4">
        <div class="text-center">
            <div class="font-semibold">{{ companies_count }}</div>
            <div>{{ __("Companies") }}</div>
        </div>
    </div>
</template>
