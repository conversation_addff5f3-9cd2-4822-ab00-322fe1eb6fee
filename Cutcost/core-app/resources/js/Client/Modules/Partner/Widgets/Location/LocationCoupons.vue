<script setup>
import { inject } from "vue";
import { useForm } from "@inertiajs/vue3";

const route = inject("route");

const props = defineProps({
    coupons: Object,
    location: Object,
    locationCoupons: Array,
    company: Object,
});


const locationForm = useForm({
    coupons: props.locationCoupons ?? [],
});

const submit = () => {
    locationForm.patch(route("partner.location.update.coupons",
        { location: props.location.slug }));
};

</script>
<template>

    <Card :title="__('Coupons')">
        <div>
            <x-select :data="coupons" v-model="locationForm.coupons" multiple :label="__('Select coupons')" >
                <template #nothing>
                    <x-button class="w-full" as="Link" :href="route('partner.coupons.create', { company: company.slug })">
                        {{ __("Create coupon") }}
                    </x-button>
                </template>
            </x-select>
            <div class="flex justify-end mt-2">
                <x-button @click="submit">{{ __("Update") }}</x-button>
            </div>
        </div>
    </Card>
</template>
