<script setup>
import { ref, inject } from "vue";
import { useTranslation, FormMap, PaginatedList, useHelpers, useCache } from "@s";
import { useForm } from "@inertiajs/vue3";
import BusinessHours from "../../Features/BusinessHours.vue";

const { countryCodeToEmoji } = useHelpers();

const route = inject("route");
const { flush } = useCache();

const props = defineProps({
  create: Boolean,
  location: {
    type: Object,
    default: {},
  },
  company: Object,
});

const locationForm = useForm({
  type: props.location?.type ?? "online",
  is_open: props.location?.is_open ?? true,
  name: props.location?.name ?? "",
  description: props.location?.description ?? "",
  address: props.location?.address ?? "",
  lnglat: props.location?.lnglat ?? null,
  business_hours: props.location?.business_hours ?? [],
  city_id: props.location?.city_id ?? null,
  rent_expires_at: props.location?.rent_expires_at ?? null,
  upload: {
    temp_uuid: null,
    deleted_ids: [],
  },
});

const types = ref([
  { id: "online", name: useTranslation.__("Online") },
  { id: "offline", name: useTranslation.__("Offline location") },
]);

const submitForm = () => {

  if (locationForm.type === "online") {
    locationForm.address = null;
    locationForm.lnglat = null;
  }

  if (props.create) {
    locationForm.post(route("partner.locations.store", { company: props.company.slug }), {
    });
  } else {
    locationForm.put(route("partner.locations.update", { location: props.location.slug }), {
      preserveScroll: true
    });
  }
  flush();
};

</script>

<template>
  <x-form noClass @submit="submitForm" class="space-y-4" :form="locationForm">
    <Card v-if="create" :url="route('company.show', { slug: company.slug })" class="w-full mb-2">
      <x-select v-model="locationForm.type" :data="types" :label="__('Select type') + '*'"
        :message="locationForm.errors.type" required />
    </Card>
    <Card back>
      <x-upload :max="6" v-model="locationForm.upload" :media="props.location.media"
        :message="locationForm.errors.images" class="mb-2" />
      <div class="space-y-4">
        <div>
          <x-input :max="64" :label="__('Title')" v-model="locationForm.name" :message="locationForm.errors.name" />
        </div>
        <div>
          <x-textarea :max="400" :label="__('Description')" v-model="locationForm.description"
            :message="locationForm.errors.description" />
        </div>
      </div>

      <div class="mt-4">
        <BusinessHours v-model="locationForm.business_hours" />
      </div>
      <div v-if="locationForm.type === 'offline'" class="mt-2">
        <x-checkbox v-model="locationForm.is_open" :label="__('Is open for visiting')" />
      </div>
    </Card>

    <Card>
      <PaginatedList v-model="locationForm.city_id" type="city_search" required :filters="{
        country_id: props.company?.country_id,
      }">
        <template #default="{ item }">
          {{ countryCodeToEmoji(item.country_code) }} {{ item.name }}
        </template>
      </PaginatedList>
      <div class="mt-4 space-y-4" v-if="locationForm.type !== 'online'">
        <FormMap v-model:lnglat="locationForm.lnglat" v-model:address="locationForm.address"
          :lnglat="locationForm.lnglat" :address="locationForm.address" />
      </div>
    </Card>
  </x-form>
</template>
