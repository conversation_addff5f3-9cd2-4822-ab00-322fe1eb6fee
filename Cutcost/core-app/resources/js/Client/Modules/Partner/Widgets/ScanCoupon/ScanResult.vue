<script setup>
import { CouponMiniCard, UserCard } from '@s'
import { computed } from 'vue'

const props = defineProps({
    scan: Object,
})

const coupon = computed(() => props.scan?.coupon)
const blueprint = computed(() => props.scan?.builder || [])
const user = computed(() => props.scan?.user)
const message = computed(() => props.scan?.message)
const status = computed(() => props.scan?.status)

</script>

<template>
    <Card :title="__('Scan results')">
        <div class="space-y-4">
            <div class="  font-medium rounded-lg px-4 py-2"
                :class="status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ message }}
            </div>

            <CouponMiniCard :coupon v-if="coupon?.id" />

            <UserCard :user v-if="user?.id" />
        </div>
    </Card>
</template>
