import L from 'leaflet';

const entityIcon = (imageUrl, name) => {
  const hasImage = imageUrl && imageUrl !== "404";
  const text = name ? name.charAt(0).toUpperCase() : "•";

  return L.divIcon({
    html: `
      <div class="location-icon">
        <div class="location-icon__circle">
          ${hasImage
            ? `<img src="${imageUrl}" alt="${name || ''}" />`
            : text
          }
        </div>
        <div class="location-icon__label">
          ${name || ""}
        </div>
      </div>
    `,
    className: "", 
    iconSize: [44, 44],
    iconAnchor: [22, 44],
  });
};

export default entityIcon;
