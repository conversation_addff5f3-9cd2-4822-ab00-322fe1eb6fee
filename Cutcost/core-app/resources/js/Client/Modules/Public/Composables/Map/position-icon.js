import L from 'leaflet';

const positionIcon = (color) => {
  return L.divIcon({
    html: `
      <div class="marker-container">
        <div class="marker-wave" style="border-color: ${color}"></div>
        <div class="marker-pin" style="background-color: ${color}"></div>
      </div>
    `,
    className: "", 
    iconSize: [44, 44],
    iconAnchor: [22, 20],
  });
};

export default positionIcon;
