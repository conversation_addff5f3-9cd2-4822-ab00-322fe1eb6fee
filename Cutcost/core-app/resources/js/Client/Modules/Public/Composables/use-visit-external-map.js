const isAppleDevice = /iPhone|iPad|iPod|Macintosh/.test(navigator.userAgent);

const visitMap = (lnglat) => {
    const currentPosition = "My+Location"; //google maps property My+Location
    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${currentPosition}&destination=${lnglat.lat},${lnglat.lng}`;
    const appleMapsUrl = `https://maps.apple.com/?saddr=${currentPosition}&daddr=${lnglat.lat},${lnglat.lng}`;

    window.open(isAppleDevice ? appleMapsUrl : googleMapsUrl, "_blank");
};

export default visitMap;
