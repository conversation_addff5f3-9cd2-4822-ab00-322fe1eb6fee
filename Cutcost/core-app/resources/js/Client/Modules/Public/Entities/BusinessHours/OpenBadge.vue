<script setup>
import { PhCheckCircle, PhCircle } from '@phosphor-icons/vue'

const props = defineProps({
    isTodayOpen: { type: Boolean, default: false },
})

</script>

<template>
    <div >
        <div :class="['inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium', isTodayOpen ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800']"
            aria-live="polite">
            <PhCheckCircle v-if="isTodayOpen" :size="18" weight="fill" class="fill-success" />
            <PhCircle v-else :size="18" weight="fill" class="fill-error" />
            <span>{{ isTodayOpen ? __('Open') : __('Closed') }}</span>
        </div>
    </div>
</template>