<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import OpenBadge from './OpenBadge.vue'

const props = defineProps({
  data: { type: Object, required: true },
  short: { type: Boolean, default: false },
  timezone: { type: String, default: null }, // business/location timezone passed from template
  edit: { type: Boolean, default: false },
  noBadge: { type: Boolean, default: false },
})

// small alias for template clarity
const propsShort = computed(() => props.short)

// helpers
function toMinutes(t) {
  const [hh, mm] = String(t).split(':').map(Number)
  const h = hh === 24 ? 0 : hh
  return (hh === 24 ? 1440 : h * 60) + (mm || 0)
}

function pad(n) { return String(n).padStart(2, '0') }

// Determine timezone: prop (business/location) > Intl-resolved (user) > UTC
// props.timezone is the business/location timezone passed from template (USER_TIMEZONE)
const businessTz = computed(() => props.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC')

// also expose user timezone if you want to compare/show both
const userTz = computed(() => Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC')

// reactive "now" so everything updates automatically
const now = ref(new Date())
let timer = null
onMounted(() => {
  // update every 15 seconds to keep UI responsive around date transitions
  timer = setInterval(() => { now.value = new Date() }, 15_000)
})
onUnmounted(() => {
  if (timer) clearInterval(timer)
})

// get "now" parts in the target (business) timezone (hours, minutes, weekday index)
// NOTE: weekdayIndex uses the same ordering as your `days` array (Monday=0 ... Sunday=6)
const tzNow = computed(() => {
  const d = now.value

  // Get hour and minute in business timezone
  const parts = new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: businessTz.value
  }).formatToParts(d)

  const hour = Number(parts.find(p => p.type === 'hour')?.value ?? '0')
  const minute = Number(parts.find(p => p.type === 'minute')?.value ?? '0')

  // weekday in English short/long form — we compare to an EN map below (keep stable)
  const weekdayStr = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    timeZone: businessTz.value
  }).format(d)

  // Map must match your days array ordering (useBusinessHours uses Monday..Sunday)
  const weekdayMap = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
  const weekdayIndex = weekdayMap.indexOf(weekdayStr) // 0..6 (Monday..Sunday)

  return { hour, minute, weekdayIndex: weekdayIndex === -1 ? 0 : weekdayIndex, utcNow: d }
})


function formatWithTZ(date, timeZone, options) {
  return new Intl.DateTimeFormat(undefined, { timeZone, ...options }).format(date)
}

// use Intl.RelativeTimeFormat for human-friendly relative strings
const rtf = new Intl.RelativeTimeFormat(undefined, { numeric: 'auto' })
function formatRelativeMinutes(diffMin) {
  if (diffMin <= 0) return rtf.format(0, 'second') // "now"
  const days = Math.floor(diffMin / 1440)
  const hours = Math.floor((diffMin % 1440) / 60)
  const mins = Math.floor(diffMin % 60)
  if (days > 0) return rtf.format(days, 'day')
  if (hours > 0) return rtf.format(hours, 'hour')
  return rtf.format(mins, 'minute')
}

// reactive values derived from tzNow
const todayIndex = computed(() => tzNow.value.weekdayIndex)
const currentMin = computed(() => tzNow.value.hour * 60 + tzNow.value.minute)
const currentTime = computed(() => `${pad(tzNow.value.hour)}:${pad(tzNow.value.minute)}`)

function inRange(startMin, endMin, currentMinLocal) {
  // full day (open 00:00-24:00)
  if (startMin === 0 && endMin === 1440) return true

  // invalid zero-length range => closed
  if (startMin === endMin) return false

  // normal same-day range
  if (endMin > startMin) {
    // if endMin === 1440, treat it as open until midnight (exclusive of next day's 00:00)
    if (endMin === 1440) return currentMinLocal >= startMin
    return currentMinLocal >= startMin && currentMinLocal < endMin
  }

  // spans over midnight (e.g., 22:00 - 02:00)
  return currentMinLocal >= startMin || currentMinLocal < endMin
}

// Processed days for full list (reactive)
const processedDays = computed(() => {
  const days = (props.data && props.data.days) || []
  return days.map((d, idx) => {
    const ranges = (d.diapasons || []).map(r => ({
      start: r.start,
      end: r.end,
      startMin: toMinutes(r.start),
      endMin: toMinutes(r.end)
    }))

    const isOpen = ranges.some(r => inRange(r.startMin, r.endMin, currentMin.value))

    return {
      ...d,
      ranges,
      isOpen
    }
  })
})

const holidays = computed(() => (props.data && props.data.holidays) || [])
const isTodayOpen = computed(() => (processedDays.value[todayIndex.value] ? processedDays.value[todayIndex.value].isOpen : false))

// Build event timeline for the next 14 days (reactive).
// Events store absolute minutes offset relative to business-tz "today"'s midnight.
const events = computed(() => {
  const days = (props.data && props.data.days) || []
  const ev = []
  for (let dayOffset = 0; dayOffset < 14; dayOffset++) {
    const di = (todayIndex.value + dayOffset) % 7
    const day = days[di] || { diapasons: [] }
    const base = dayOffset * 1440
      ; (day.diapasons || []).forEach(r => {
        const sMin = toMinutes(r.start)
        const eMin = toMinutes(r.end)
        let s = sMin + base
        let e = eMin + base

        const isFullDay = sMin === 0 && eMin === 1440

        // if event closes before it opens (spans midnight) and not full day, extend close to next day
        if (e <= s && !isFullDay) e += 1440

        ev.push({ type: 'open', timeMin: s, dayOffset, di, rawStart: r.start })
        if (!isFullDay) ev.push({ type: 'close', timeMin: e, dayOffset, di, rawEnd: r.end })
      })
  }
  ev.sort((a, b) => a.timeMin - b.timeMin)
  return ev
})

function computeNext(type) {
  const evs = events.value
  const todayMin = currentMin.value // minutes since business-tz midnight for "now"

  for (const ev of evs) {
    if (ev.type !== type) continue

    // skip events earlier today
    if (ev.timeMin <= todayMin && ev.dayOffset === 0) continue

    // minutes until event from now (business-tz perspective)
    const minutesToAdd = ev.timeMin - todayMin // <-- fixed: ev.timeMin already includes dayOffset*1440

    // compute the absolute Date for that future moment by adding minutesToAdd to real now
    const eventDate = new Date(tzNow.value.utcNow)
    eventDate.setMinutes(eventDate.getMinutes() + minutesToAdd)

    // format in business timezone
    const label = formatWithTZ(eventDate, businessTz.value, { weekday: 'short', hour: '2-digit', minute: '2-digit', hour12: false })
    return { raw: ev, label, relative: formatRelativeMinutes(minutesToAdd) }
  }
  return null
}

const nextOpen = computed(() => computeNext('open'))
const nextClose = computed(() => computeNext('close'))

// when external data changes, nudge 'now' so computed properties refresh immediately
watch(() => props.data, () => { now.value = new Date() }, { deep: true })
watch(() => props.timezone, () => { now.value = new Date() })

</script>

<template>
  <div v-if="propsShort">
    <OpenBadge v-if="data.enabled" :isTodayOpen="isTodayOpen" />
    <div v-else class="text-sm font-bold text-error">{{ __('Business hours not set') }}</div>
  </div>

  <div v-else-if="data.enabled || edit"
    class="max-w-2xl mx-auto w-full px-4 py-2 bg-surface text-text dark:text-dark-text dark:bg-bg-dark rounded-2xl shadow-lg ring-1 ring-border dark:ring-dark-border">

    <div class="flex w-full items-center flex-col">
      <div class="flex gap-2 w-full justify-between items-center ">
        <div class="gap-1 flex items-center">
          <slot />
        </div>
        <OpenBadge v-if="data.enabled && !noBadge" :isTodayOpen="isTodayOpen" class="justify-left " />
      </div>
    </div>

    <div class="flex justify-center gap-4 mt-1" v-if="data.enabled">
      <div v-if="isTodayOpen && nextClose">
        <span class="text-sm font-bold">
          {{ __('Closes in') }}
        </span>
        {{ nextClose.label }} ({{ nextClose.relative
        }})
      </div>

      <div v-if="nextOpen && !isTodayOpen">
        <span class="text-sm font-bold">
          {{ __('Opens in') }}
        </span>
        {{ nextOpen.label }} ({{ nextOpen.relative
        }})
      </div>
    </div>

    <!-- Full week list -->
    <ul v-if="data.enabled" class="divide-y divide-slate-100 dark:divide-slate-800 rounded-lg overflow-hidden mt-4">
      <li v-for="(day, i) in processedDays" :key="day.name" class="flex items-center justify-between px-2 py-1">
        <div class="flex items-center gap-3">
          <div>
            <div class="text-sm font-medium"
              :class="['w-10 text-sm font-medium', i === todayIndex ? 'text-secondary' : '']"> {{ __('weekdays.' + i) }}
            </div>
          </div>
        </div>

        <div class="text-right">
          <div class="text-sm font-medium">
            <template v-if="day.workDay && day.ranges.length">
              <div v-for="(r, idxR) in day.ranges" :key="idxR">{{ r.start }} — {{ r.end }}</div>
            </template>
            <div v-else class="text-slate-400">{{ __('Closed') }}</div>
          </div>
        </div>
      </li>
    </ul>

    <div v-if="holidays && holidays.length && !propsShort" class="mt-4 text-sm text-slate-500">
      <strong>Holidays:</strong>
      <ul class="list-disc list-inside mt-2">
        <li v-for="(h, idx) in holidays" :key="idx">{{ h }}</li>
      </ul>
    </div>

  </div>
</template>
