<script setup>
import { computed } from "vue";
import { Content } from "@s";
import ShowConditions from "../../Features/ShowCoupon/ShowConditions.vue";
import { PhCity, PhGlobe } from "@phosphor-icons/vue";
import CouponDiscount from "./CouponDiscount.vue";

const props = defineProps({
  coupon: Object,
  full: {
    type: Boolean,
    default: false,
  },
});
const distance = computed(() => {
  const meters = props.coupon?.distance;
  if (!meters) return null;
  return meters < 1000 ? `${meters.toFixed(0)} m.` : `${(meters / 1000).toFixed(0)} km.`;
});
</script>

<template>
  <div>
    <h2 class="text-xl text font-semibold" :class="{
      'underline': !full
    }">{{ coupon.name }}</h2>
    <div class="flex mb-2 mt-1">
      <CouponDiscount :coupon="coupon" mini :currency="coupon.currency" />
    </div>
    <div class="text-muted dark:text-dark-muted">
      <Content :content="coupon.description" />
    </div>

    <div class="mt-2">
      <div class="flex mx-auto text gap-1 text-sm" v-if="coupon.locations?.length">
        <PhCity :size="19" class="fill-secondary" />
        <span class="mt-0.5">
          {{coupon?.locations?.map((location) => location.address).join(", ")}}
        </span>
      </div>
      <div v-else-if="coupon?.type === 'online'" class="flex gap-1 text items-center">
        <PhGlobe :size="19" class="fill-secondary" /> {{ __("Online") }}
      </div>
      <div class="text-md  justify-center text-success flex  flex-col rounded-2xl">
        <div class="mx-auto" v-if="distance">
          ≈ {{ distance }} {{ __("away") }}
        </div>
      </div>
    </div>
    <ShowConditions :coupon :full class="my-2" />
  </div>
</template>
