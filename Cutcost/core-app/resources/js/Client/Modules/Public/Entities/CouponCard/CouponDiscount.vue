<script setup>
import { computed } from "vue";

const props = defineProps({
    coupon: Object,
    mini: Boolean,
    inline: Boolean,
    currency: Object,
});

const totalPrice = computed(() => {
    let percent = 0;
    if (props.coupon.discount.includes("%")) {
        percent = props.coupon.discount.slice(0, -1);
        return (
            props.coupon.price_in_store -
            (props.coupon.price_in_store * percent) / 100
        ).toFixed(2);
    } else {
        return (
            props.coupon.price_in_store - props.coupon.discount
        ).toFixed(2);
    }
});

// const discountInFiat = computed(() => {
//     const discount = props.coupon.discount;

//     if (typeof discount === "string" && discount.includes("%")) {
//         const percent = parseFloat(discount.replace("%", ""));
//         return ((percent / 100) * props.coupon.price_in_store).toFixed(2);
//     }

//     return Number(discount).toFixed(2);
// });

const discountPercent = computed(() => {
    if (props.coupon.discount.includes("%")) return props.coupon.discount;

    return (
        (
            (props.coupon.discount / props.coupon.price_in_store) *
            100
        ).toFixed(0) + "%"
    );
});

const isProductFree = computed(() => totalPrice.value == 0);
</script>
<template>
    <div class="flex dark:bg-white bg-bg-dark rounded-lg p-2 items-center">
        <!-- Discount -->
        <div class="text-secondary flex items-center gap-2 rounded-lg font-bold" >
            <div class="flex gap-1 font-bold">
                <span class="line-through-color text-muted dark:text-dark-muted after:bg-error">{{ coupon.price_in_store
                }}{{ currency?.symbol || currency?.name }}
                </span>
            </div>
            <div class="text-error">-{{ discountPercent }}</div>
        </div>

        <div class="flex w-max text-muted gap-2 dark:text-dark-muted ml-2"
           >
            <span v-if="!isProductFree" :class="isProductFree ? 'line-through-color after:bg-error' : ''"
                class="text-success font-bold">{{
                    totalPrice }}{{ currency?.symbol || currency?.name }}</span>
            <span class="format-accent text-success" v-if="isProductFree">
                {{ __("Free") }}
            </span>
        </div>
    </div>
</template>
