<script setup>
import Rating from "../../Features/Rating.vue";
import { Share } from "@s";
import { PhShareFat, PhEye, PhChatsTeardrop } from "@phosphor-icons/vue";
import Bookmarkable from "../../Features/Bookmarkable.vue";

const props = defineProps({
  coupon: Object,
});

const goToCoupon = () => {
  const comments_container = document.getElementById("comments_container");

  if (comments_container) {
    comments_container.scrollIntoView({ behavior: "smooth", block: "start" });
    return;
  }
}

</script>

<template>
  <div>
    <div class="flex items-center justify-between gap-2 pb-2 text-lg">
      <div class="flex w-full flex-col">
      </div>
    </div>
    <div class="flex justify-between">
      <Rating modelName="Coupon" :model="coupon" @click.stop />
      <div class="flex gap-2 items-center">
        <div class="flex cursor-pointer items-center gap-1">
          <x-button intent="rounded" @click="goToCoupon">
            <template #icon>
              <PhChatsTeardrop :size="20" />
              {{ coupon.comments_count }}
            </template>
          </x-button>

          <x-button intent="rounded">
            <PhEye :size="20" />
            {{ coupon.views }}
          </x-button>

          <Bookmarkable :model="coupon" modelName="Coupon" :modelId="coupon?.id" @click.stop />

          <Share :url="route('coupon.view', { coupon: coupon.slug })">
            <x-button intent="rounded">
              <PhShareFat :size="20" />
            </x-button>
          </Share>
        </div>
      </div>
    </div>
  </div>
</template>
