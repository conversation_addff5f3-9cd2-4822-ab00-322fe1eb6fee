<script setup>
import { PhBookmarkSimple } from "@phosphor-icons/vue";
import { useHelpers, useGlobalStore } from "@s";
import { computed, inject } from "vue";

const { debounce } = useHelpers();
const global = useGlobalStore();

const props = defineProps({
    model: Object,
    modelName: String,
    modelId: Number,
});

const route = inject("route");

const bookmarked = computed({
    get: () => props.model.is_bookmarked,
    set: (value) => props.model.is_bookmarked = value
});


const toggleBookmark = () => {
  bookmarked.value = !bookmarked.value;
};

const bookmark = debounce(async () => {
  try {

    if (!global.auth()) return;

    await axios.patch(route("bookmark", { model: props.modelName, id: props.modelId }));
  toggleBookmark();
  } catch (err) {
    console.error(err);
  }
}, 200);

</script>

<template>
    <x-button @click="bookmark" intent="rounded">
        <PhBookmarkSimple :size="20" :weight="bookmarked ? 'fill' : 'regular'" />
    </x-button>
</template>