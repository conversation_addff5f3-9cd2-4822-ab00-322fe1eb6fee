<script setup>
import { router } from "@inertiajs/vue3";
import { CompanyControls } from "@m/Seller";
import { inject } from "vue";
import {NavigationButton} from "@m/Fixed";

const route = inject("route");

const props = defineProps({
  company: Object,
  short: {
    type: Boolean,
    default: false,
  },
});

const visitCompany = () => {
  router.visit(route("company.show", { slug: props.company.slug }));
};
</script>

<template>
  <NavigationButton @click="visitCompany" class="w-full !justify-between !items-center flex gap-3 !px-2 !rounded-0">
    <div class="flex gap-2 !text-start w-full min-w-0">
      <x-model-media :url="company.avatar" class="!h-14 !w-14 shrink-0" />

      <div class="flex flex-col flex-grow min-w-0 text-sm">
        <span class="font-medium truncate block w-full">{{ company.name }}</span>
        <span class="text-xs text-muted dark:text-dark-muted truncate block w-full">
          {{ __(company.country?.name) }} · {{ company.currency?.code }}
        </span>
      </div>
    </div>

    <div v-if="!short" class="shrink-0 flex gap-1">
      <CompanyControls :company="company" short />
    </div>
  </NavigationButton>
</template>
