<script setup>
import ShowSocials from './ShowSocials.vue';
import { UserCard, useHelpers } from "@s";

const { countryCodeToEmoji } = useHelpers();

defineProps({
    company: Object,
})

</script>

<template>
    <x-button intent="rounded" v-bind="$attrs" @click="$refs.modal.modalOpen = true" class="w-full">
        {{ __("More info") }}
    </x-button>
    <Modal ref="modal" :title="__('More info')" :short="true">
        <div class="flex flex-col space-y-1 text">
            <div>
                <h3 class=" format-accent">
                    {{ __('currency') }}
                </h3>
                {{ company.currency?.code }}
            </div>
            <div>
                <h3 class=" format-accent">
                    {{ __('location') }}
                </h3>
                {{ countryCodeToEmoji(company.country?.code) }}
                
                {{ __(company.country?.name) }} 
            </div>
            <ShowSocials :socials="company.socials" />

            <div >
                <h3 class=" mb-1 format-accent">
                    {{ __('Owner') }}:
                </h3>
                <UserCard :user="company.user" />
            </div>
            <x-hr class="my-3"/>
            <x-button 
                :href="route('seller.users.list', { type: 'user_companySubscribers', model: 'company', id: company.id })">
                <div class="flex-col flex">
                    <div>
                        {{ company?.subscribers_count }}
                    </div>
                    <div>
                        {{ __("Subscribers") }}
                    </div>
                </div>
            </x-button>
        </div>

    </Modal>

</template>