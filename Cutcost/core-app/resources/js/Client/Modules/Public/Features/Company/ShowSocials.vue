<script setup>
defineProps({
    socials: Array,
});

const normalizeUrl = (url) => {
    if (/^\+?[1-9]\d{1,14}$/.test(url)) {
        return `tel:${url}`;
    }
    return url.startsWith("http") ? url : `https://${url}`;
};

const isPhone = (url) => /^\+?[1-9]\d{1,14}$/.test(url);

const getLabel = (url) => {
    if (isPhone(url)) return url;
    const normalized = normalizeUrl(url);
    return (normalized.match(/https?:\/\/([^/]+)/) || ['', ''])[1];
};
</script>

<template>
    <div v-if="socials?.length" class="space-y-2">
        <div v-if="socials.some(isPhone)" class="flex flex-col space-y-1">
            <h3 class="format-accent text">
                {{ __("Phones") }}
            </h3>
            <div class="flex flex-wrap gap-2">
                <x-button v-for="social in socials.filter(isPhone)" :key="social" as="a" :href="normalizeUrl(social)"
                    intent="link">
                    📞 {{ getLabel(social) }}
                </x-button>
                <!-- hidden raw number for copy-paste or SEO -->
                <span v-for="social in socials.filter(isPhone)" :key="social + '-hidden'" class="sr-only">
                    {{ social }}
                </span>
            </div>
        </div>

        <!-- Links -->
        <div v-if="socials.some(s => !isPhone(s))" class="flex flex-col space-y-1">
            <h3 class="format-accent text">
                {{ __("Links") }}
            </h3>
            <div class="flex flex-wrap gap-2">
                <x-button v-for="social in socials.filter(s => !isPhone(s))" :key="social" as="a"
                    :href="normalizeUrl(social)" intent="link">
                    🔗 {{ getLabel(social) }}
                </x-button>
                <!-- hidden raw link for SEO/screen readers -->
                <span v-for="social in socials.filter(s => !isPhone(s))" :key="social + '-hidden'" class="sr-only">
                    {{ social }}
                </span>
            </div>
        </div>
    </div>
</template>
