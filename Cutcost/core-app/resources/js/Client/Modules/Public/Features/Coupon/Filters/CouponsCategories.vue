<script setup>
import { PhX } from '@phosphor-icons/vue';
import { ref } from 'vue';
import { NavigationButton } from "@m/Fixed";

defineProps({
    categories: Object,
    category: Object,
})

const emits = defineEmits(['apply']);

const modal = ref(null);

const clear = () => {
    emits('apply', null);
};

const chooseCategory = (category) => {
    modal.value.modalOpen = false;
    emits('apply', category);
};

</script>

<template>
    <NavigationButton intent="secondary" class="!px-2" @click="$refs.modal.modalOpen = true">
        {{ category?.id ? __(category.name) : __('Categories') }}
        <PhX :size="20" class="fill-error rounded-full" @click.stop="clear" v-if="category?.id" />
    </NavigationButton>

    <Modal ref="modal" :title="__('Categories')">
        <div class="grid grid-cols-2">
            <NavigationButton v-for="item in categories" :key="item.id" @click="chooseCategory(item)">
                {{ __(item.name) }}
            </NavigationButton>
        </div>
    </Modal>

</template>