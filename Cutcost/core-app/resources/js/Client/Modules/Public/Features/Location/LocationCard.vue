<script setup>
import { computed, inject } from 'vue';
import { useGlobalStore, DeleteModel, Report, Share, ErrorWidget, useHelpers, Content, Translate } from "@s";
import ShowBusinessHours from "../../Entities/BusinessHours/ShowBusinessHours.vue";
import { PhCity, PhMapPinLine, PhDotsThreeVertical, PhEye, PhShareFat } from '@phosphor-icons/vue';
import Bookmarkable from '../Bookmarkable.vue';
import visitMap from "../../Composables/use-visit-external-map";
import { router } from "@inertiajs/vue3";

const { diffForHumans } = useHelpers();

const route = inject("route");

const global = useGlobalStore();

const props = defineProps({
    location: {
        type: Object,
        required: true
    },
    full: {
        type: Boolean,
        default: false,
    },
    withBusinessHours: {
        type: Boolean,
        default: false,
    },
})

const isOwner = computed(() => global.user?.id === props.location.user_id);

const goToLocation = () => {
    if (props.full) return;
    router.get(route('location.show', { slug: props.location.slug }));
}
</script>

<template>
    <div>
        <div :class="{ 'hover:opacity-85 cursor-pointer': !full }" class="relative bg" @click="goToLocation">
            <x-gallery :media="location?.media" def="assets/images/location.png" h="30" />
            <div class="flex w-full max-sm:flex-col py-2 px-3 gap-2 justify-between items-start">
                <div class="w-full">
                    <div class="text">
                        <div>
                            <h2 class="text-xl font-semibold " :class="{
                                'underline': !full
                            }">{{ location.name }}</h2>
                            <div class="text-muted dark:text-dark-muted">
                                <Content :content="location?.description" />
                                <Translate @click.stop :model="location" field="description" />
                            </div>
                        </div>
                        <x-hr />
                        <div class=" py-2 ">
                            <div class=" flex gap-1 ">
                                <div>
                                    <PhMapPinLine :size="20" class="fill-secondary" />
                                </div>
                                <span class="py-auto">
                                    {{ location.address }}
                                </span>
                            </div>
                            <div class="flex gap-1" v-if="location.city?.name">
                                <div>
                                    <PhCity :size="20" class="fill-secondary" />
                                </div>
                                <span class="py-auto">
                                    {{ location.city?.name }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div v-if="withBusinessHours || full">
                        <ShowBusinessHours :data="location.business_hours" noBadge />
                    </div>
                </div>
                <div class="flex w-full items-center justify-between">
                    <div class="flex w-full justify-between gap-2">
                        <div class="flex" @click.stop>
                            <x-button intent="rounded" @click="visitMap(location.lnglat)">
                                {{ __('Get Directions') }}
                            </x-button>
                        </div>
                        <div class="flex gap-2">
                            <x-button intent="rounded">
                                <template #icon>
                                    <PhEye :size="20" />
                                </template>
                                {{ location?.views ?? 0 }}
                            </x-button>
                            <Bookmarkable :model="location" modelName="Location" :modelId="location?.id" @click.stop />
                            <Share :url="route('location.show', { slug: location.slug })">
                                <x-button intent="rounded">
                                    <PhShareFat :size="20" />
                                </x-button>
                            </Share>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute top-0 flex gap-1 right-0 p-2" @click.stop>
                <Dropdown>
                    <template #button>
                        <x-button intent="rounded" class="!p-2">
                            <PhDotsThreeVertical :size="20" />
                        </x-button>
                    </template>
                    <div v-if="isOwner">
                        <DropdownItem as="Link"
                            :href="route('partner.locations.edit', { location: location.slug, company: location.company.slug })">
                            {{ __("Edit") }}
                        </DropdownItem>
                        <DeleteModel :model="location" modelName="Location"
                            :url="route('partner.locations.destroy', { location: location.slug })" dropdown
                            :redirect="$page.component === 'Discount/ShowLocation'" />
                    </div>
                    <Report model-type="location" :model-id="location.id" dropdown>
                        {{ __("Report") }}
                    </Report>
                    <DropdownItem>
                        {{ diffForHumans(location.created_at) }}
                    </DropdownItem>
                </Dropdown>
            </div>
        </div>
    </div>
</template>
