<script setup>
import { ref, watch, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { <PERSON><PERSON>pinner } from '@phosphor-icons/vue'
import { onClickOutside } from '@vueuse/core'
import { Geolocate } from "@s";

const STORAGE_KEY = 'search-select-history'
const MAX_HISTORY = 10
const MIN_CHARS = 2
const RESULTS_LIMIT = 6
const NAVIGATION_THROTTLE = 80 // milliseconds

const emit = defineEmits(['lnglat', 'geolocate', 'geolocate-error'])

const query = ref('')
const results = ref([])
const history = ref([])
const isSearching = ref(false)
const openDropdown = ref(false)
const highlightedIndex = ref(-1)
const dropdownRef = ref(null)
const mapControls = ref(null)
const searchInput = ref(null)
const lastNavigationTime = ref(0)
const keysPressed = ref(new Set())

// Custom keyboard event handler
function handleKeyDown(event) {
    // Prevent key repeat by tracking pressed keys
    if (keysPressed.value.has(event.code)) {
        return
    }
    keysPressed.value.add(event.code)

    // Only handle navigation if dropdown is open
    if (!openDropdown.value) return

    // Throttle navigation
    const now = Date.now()
    if (now - lastNavigationTime.value < NAVIGATION_THROTTLE) {
        return
    }

    switch (event.code) {
        case 'ArrowDown':
            event.preventDefault()
            lastNavigationTime.value = now
            highlightNext()
            break
        case 'ArrowUp':
            event.preventDefault()
            lastNavigationTime.value = now
            highlightPrev()
            break
        case 'Enter':
            event.preventDefault()
            selectHighlighted()
            break
        case 'Escape':
            event.preventDefault()
            openDropdown.value = false
            highlightedIndex.value = -1
            break
    }
}

function handleKeyUp(event) {
    keysPressed.value.delete(event.code)
}

// Mount/unmount event listeners
onMounted(() => {
    // Add global keyboard event listeners
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)
})

onUnmounted(() => {
    // Clean up event listeners
    document.removeEventListener('keydown', handleKeyDown)
    document.removeEventListener('keyup', handleKeyUp)
})

// Close dropdown when clicking outside
onClickOutside(mapControls, () => {
    openDropdown.value = false
    highlightedIndex.value = -1
})

// Load history from localStorage
function loadHistory() {
    try {
        history.value = JSON.parse(localStorage.getItem(STORAGE_KEY)) || []
    } catch {
        history.value = []
    }
}

// Save item to history
function saveHistory(item) {
    history.value = history.value.filter(h => h.place_id !== item.place_id)
    history.value.unshift(item)
    if (history.value.length > MAX_HISTORY) history.value.length = MAX_HISTORY
    localStorage.setItem(STORAGE_KEY, JSON.stringify(history.value))
}

// Clear history
function clearHistory() {
    history.value = []
    localStorage.removeItem(STORAGE_KEY)
    highlightedIndex.value = -1
}

// Compose unified items array: results first, then history
const items = computed(() => {
    return [
        ...(results.value || []).map(r => ({ ...r, _fromHistory: false })),
        ...(history.value || []).map(h => ({ ...h, _fromHistory: true }))
    ]
})

// Search places
async function searchPlaces(q) {
    highlightedIndex.value = 0
    if (!q || q.length < MIN_CHARS) {
        results.value = []
        return
    }
    isSearching.value = true
    try {
        const { data } = await axios.get('https://nominatim.openstreetmap.org/search', {
            params: { q, format: 'json', limit: RESULTS_LIMIT }
        })
        results.value = Array.isArray(data) ? data : []
    } catch {
        results.value = []
    } finally {
        isSearching.value = false
    }
}

// Debounced search
let searchTimeout
watch(query, (val) => {
    clearTimeout(searchTimeout)
    results.value = []
    highlightedIndex.value = -1
    if (val) {
        searchTimeout = setTimeout(() => searchPlaces(val), 600)
    }
})

// Select item
function selectItem(item) {
    saveHistory(item)
    query.value = item.display_name
    emit('lnglat', { lat: item.lat, lng: item.lon })
    openDropdown.value = false
    highlightedIndex.value = -1
}

function highlightPrev() {
    const length = items.value.length
    if (!length) {
        highlightedIndex.value = -1
        return
    }

    // Move up (decrease index)
    if (highlightedIndex.value <= 0) {
        highlightedIndex.value = length - 1 // Wrap to last item
    } else {
        highlightedIndex.value--
    }

    console.log('highlightPrev:', highlightedIndex.value)
    scrollToHighlighted()
}

function highlightNext() {
    const length = items.value.length
    if (!length) {
        highlightedIndex.value = -1
        return
    }

    // Move down (increase index)
    if (highlightedIndex.value >= length - 1) {
        highlightedIndex.value = 0 // Wrap to first item
    } else {
        highlightedIndex.value++
    }

    console.log('highlightNext:', highlightedIndex.value)
    scrollToHighlighted()
}

// Scroll to highlighted item
function scrollToHighlighted() {
    nextTick(() => {
        const dropdown = dropdownRef.value
        if (!dropdown) return
        const itemEl = dropdown.querySelectorAll('ul > li')[highlightedIndex.value]
        if (itemEl) itemEl.scrollIntoView({ block: 'nearest' })
    })
}

// Select highlighted item
function selectHighlighted() {
    if (highlightedIndex.value >= 0 && items.value[highlightedIndex.value]) {
        selectItem(items.value[highlightedIndex.value])
    }
}



// Initial load
loadHistory()
</script>

<template>
    <div class="w-full fixed z-1000000" ref="mapControls">
        <div class="m-3 right-0 sm:!w-[50%] absolute left-auto">
            <div class="flex gap-2">
                <!-- keydown event sends multiple events !!!!! -->
                <x-input v-model="query" @focus="openDropdown = true" ref="searchInput" :placeholder="__('Search')">
                    <template #icon>
                        <div class="animate-spin text" v-if="isSearching">
                            <PhSpinner :size="20" />
                        </div>
                    </template>
                </x-input>
                <Geolocate @click="openDropdown = false"
                button
                @geolocate="emit('geolocate', $event)" 
                @geolocate-error="emit('geolocate-error', $event)" />

            </div>
            <div v-show="openDropdown" ref="dropdownRef" class="absolute w-full mt-2 bg text rounded-lg shadow-lg">
                <div class="p-2">
                    <div class="flex justify-between items-center mb-2">
                        <div class="text-xs text">{{ __('Results & History') }}</div>
                        <button type="button" v-if="history.length" @click="clearHistory"
                            class="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                            {{ __('Clear History') }}
                        </button>
                    </div>
                    <ul class="divide-y divide-border dark:divide-dark-border max-h-[50vh] overflow-auto">
                        <li v-for="(item, idx) in items" :key="item.place_id + (item._fromHistory ? '-h' : '-r')"
                            :class="[
                                'py-2 px-3 cursor-pointer',
                                highlightedIndex === idx ? 'bg-surface/80 dark:bg-bg-dark/80' : 'hover:bg-surface dark:hover:bg-bg-dark'
                            ]" @click="selectItem(item)">
                            <div class="text-sm truncate">{{ item.display_name }}</div>
                            <div class="text-xs text truncate">
                                {{ item.type }} · {{ item.class }}
                                <span v-if="item._fromHistory" class="ml-2 text-[10px] text-gray-400">(history)</span>
                            </div>
                        </li>
                    </ul>
                    <div v-if="!items.length && query && !isSearching" class="py-4 text-center text-sm text-gray-400">
                        {{ __('Nothing found') }}
                    </div>
                    <div v-if="!items.length && !query" class="py-4 text-center text-sm text-gray-400">
                        {{ __('Search for places') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
