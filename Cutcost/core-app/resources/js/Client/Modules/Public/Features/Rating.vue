<script setup>
import { router } from "@inertiajs/vue3";
import { PhHeart } from "@phosphor-icons/vue";
import { useHelpers, useGlobalStore } from "@s";
import { computed, inject } from "vue";

const route = inject("route");
const { debounce } = useHelpers();
const global = useGlobalStore();

const props = defineProps({
    modelName: String,
    model: Object,
});

const isLiked = computed({
    get: () => props.model.is_liked,
    set: (value) => props.model.is_liked = value
});

const likesCount = computed({
    get: () => props.model.likes_count,
    set: (value) => props.model.likes_count = value
});

const toggle = debounce(async () => {
    try {
        if (!global.auth()) return;

        await axios.patch(
            route("rate", { model: props.modelName, id: props.model.id })
        );

        isLiked.value = !isLiked.value;
        likesCount.value = likesCount.value + (isLiked.value ? 1 : -1);
    } catch (err) {
        if (err?.response?.status === 401) {
            router.visit(route("auth"));
        }
    }
}, 200);
</script>

<template>
    <div class="flex gap-1 items-center">
        <x-button intent="rounded"
            class="flex items-center group gap-2 cursor-pointer text-text dark:text-dark-text  p-2" @click="toggle">
            <PhHeart :size="20" :weight="isLiked ? 'fill' : 'bold'" :class="{ 'text-error': isLiked }"
                class="group-hover:text-error" />
            {{ likesCount }}
        </x-button>
    </div>
</template>
