<script setup>
import { inject, computed } from "vue";
import { useTranslation } from "@s";
import { PhChatCircleDots, PhEyes } from "@phosphor-icons/vue";

const { __ } = useTranslation;
const route = inject("route");

const props = defineProps({
    coupon: Object,
});

const chatHref = computed(() =>
    route('messenger.chat', {
        key: props.coupon.company.slug,
        type: 'slug',
    })
);

const productHref = computed(() =>
    props.coupon?.link_to_product ?? null
);

</script>

<template>
    <div class=" flex flex-col gap-1 mx-1 justify-center">
        <x-button :href="chatHref" class="w-full">
            <template #icon>
                <PhChatCircleDots :size="20" />
            </template>

            {{ __("Contact seller") }}
        </x-button>

        <x-button class="w-full" v-if="productHref" :href="productHref">
            <template #icon>
                <PhEyes :size="20" />
            </template>
            {{ __("View product") }}
        </x-button>
    </div>
</template>
