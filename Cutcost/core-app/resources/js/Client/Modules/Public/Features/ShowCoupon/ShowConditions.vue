<script setup>
import { useTemplateRef, computed } from 'vue'
import NavigationButton from '../../../Fixed/Entities/NavigationButton.vue'

const props = defineProps({
    coupon: Object,
    full: {
        type: Boolean,
        default: false,
    },
})

const modal = useTemplateRef('modal')

const conditions = computed(() => {
    const list = []

    if (props.coupon?.valid_until) {
        list.push({
            label: 'Valid Until',
            value: new Date(props.coupon.valid_until).toLocaleDateString(),
        })
    }
    if (props.coupon?.users_scan_limits) {
        list.push({
            label: 'Scan Limit',
            value: props.coupon.users_scan_limits,
        })
    }
    if (props.coupon?.first_time_only) {
        list.push({
            label: 'First Time Only',
            value: 'Yes',
        })
    }
    if (props.coupon?.min_order_amount) {
        list.push({
            label: 'Minimum Order Amount',
            value: props.coupon.min_order_amount,
        })
    }
    if (props.coupon?.one_use_per_cart) {
        list.push({
            label: 'One Use per Cart',
            value: 'Yes',
        })
    }
    if (props.coupon?.min_age) {
        list.push({
            label: 'Minimum Age',
            value: props.coupon.min_age,
        })
    }
    if (props.coupon?.max_age) {
        list.push({
            label: 'Maximum Age',
            value: props.coupon.max_age,
        })
    }
    if (props.coupon?.custom_conditions) {
        list.push({
            label: 'Custom Conditions',
            value: props.coupon.custom_conditions,
        })
    }

    return list
})

const count = () => conditions.value.length
</script>

<template>
    <div>
        <template v-if="!full">
            <x-button intent="secondary" class="w-full" v-if="count() > 0" @click.stop="modal.modalOpen = true">
                {{ __("Conditions")?.toUpperCase() }} -
                {{ count() }}
            </x-button>
            <Modal ref="modal" :title="__('Conditions')" short>
                <NavigationButton v-for="(item, index) in conditions" :key="index"
                    class="flex justify-start items-center py-3 text-sm hover:bg-surface dark:hover:bg-bg-dark ">
                    <span class="font-medium text-warning ">{{ __(item.label) }}</span>
                    <span class="text" v-if="item.value !== 'Yes'">{{ item.value }}</span>
                </NavigationButton>
            </Modal>
        </template>
        <template v-else>
            <NavigationButton v-for="(item, index) in conditions" :key="index"
                class="flex justify-start items-center py-3 text-sm hover:bg-surface dark:hover:bg-bg-dark ">
                <span class="font-medium text-warning">{{ __(item.label) }}</span>
                <span class="text" v-if="item.value !== 'Yes'">{{ item.value }}</span>
            </NavigationButton>
        </template>
    </div>
</template>
