<script setup>
import { useForm } from "@inertiajs/vue3";
import { inject } from "vue";

const route = inject("route");

const loginForm = useForm({
  email: null,
  password: null,
  remember: false,
});

const submitLogin = () => {
  loginForm.post(route("login"), {
    onError: () => loginForm.reset("password"),
  });
};
</script>

<template>

  <Head :title="__('Login')">
  </Head>
  <form @submit.prevent="submitLogin">
    <fieldset class="mt-4 px-6 pb-4">
      <legend class="h2 select-none">{{ __('Login') }}</legend>
      <div class="my-2">
        <x-input required type="email" autocomplete="email" label="Enter your email:" :message="loginForm.errors.email"
          v-model="loginForm.email" />
      </div>
      <div class="my-2">
        <x-input type="password" required name="password" autocomplete="current-password" label="Enter your password"
          :message="loginForm.errors.password" v-model="loginForm.password" />
      </div>
      <div class="my-2 mt-4 flex justify-between">
        <x-checkbox id="remember" label="Remember me" v-model="loginForm.remember" />
        <button type="submit">{{ __('Login') }}</button>
      </div>
    </fieldset>
  </form>
</template>
