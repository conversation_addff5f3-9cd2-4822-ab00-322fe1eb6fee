<script setup>
import { Head } from "@inertiajs/vue3";
import ShowCompanyContent from "../../Widgets/ShowCompany/ShowCompanyContent.vue";
import { Recommendations } from "@m/Seller";
import { computed, onMounted, onUnmounted } from "vue";
import { useGlobalStore, ErrorWidget } from "@s";
import { Coupons } from "@m/Public";
import Locations from "../../Widgets/Feeds/Locations.vue";
import { useHeaderStore } from "@m/Fixed";

const global = useGlobalStore();
const header = useHeaderStore();

const props = defineProps({
  company: [Object, null],
  status: [Number, null],
});

const isOwner = computed(() => global.user?.id === props.company.user_id);

const options = [
  { id: 'coupons', name: 'coupons' },
  { id: 'locations', name: 'locations' },
];

onMounted(() => {
  header.title = 'view company' + (props.company ? (' | ' + props.company.name) : '');
  if (!props.status) {
    header.navigation = 'company' + props.company?.id;
    header.options = options;
    header.chosenTab = localStorage.getItem(header.navigation + '.selected') ?? 'coupons';
    header.swipeEvent();
  }
});

onUnmounted(() => {
  header.clear();
});
</script>

<template>

  <Head :title="header.title" />
  <div class="card-position relative flex gap-2 max-sm:flex-col">
    <div class="post-position space-y-2" v-if="company">
      <ShowCompanyContent :company :subscribersCount="company.subscribers_count" :isOwner />
      <div v-show="header.chosenTab === 'coupons'">
        <Coupons type="coupon_company" :data="{ company: company.slug }" small />
      </div>
      <div v-show="header.chosenTab === 'locations'">
        <Locations type="location_company" :data="{ company: company.slug }" />
      </div>
    </div>
    <div v-else>
      <ErrorWidget
        :message="status === 403 ? __('Resource is not available') : __('Business not found...')" back />
    </div>
    <Recommendations />
  </div>
</template>
