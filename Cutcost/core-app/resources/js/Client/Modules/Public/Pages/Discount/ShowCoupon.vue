<script setup>
import Comments from "../../../Seller/Widgets/User/Comments.vue";
import { CouponCard } from "@m/Public";
import { useHeaderStore } from "@m/Fixed";
import CompanyCard from "../../Features/Company/CompanyCard.vue";
import LocationCard from "../../Features/Location/LocationCard.vue";
import { onMounted, onUnmounted } from "vue";
import { ErrorWidget } from "@s";
import ShowBusinessHours from "../../Entities/BusinessHours/ShowBusinessHours.vue";

const props = defineProps({
    coupon: [Object, null],
    company: [Object, null],
    locations: [Object, null],
    status: [Number, null],
});

const header = useHeaderStore();

onMounted(() => {
    header.title = 'View coupon' + (props.coupon ? (' | ' + props.coupon.name) : '');
});

onUnmounted(() => {
    header.clear();
});

</script>
<template>

    <Head :title="header.title" />
    <div class="card-position flex flex-col gap-4 md:flex-row">
        <div class="w-full space-y-2 md:w-6/10 flex flex-col md:order-none" v-if="coupon">
            <CouponCard :coupon full v-motion-slide-visible-once-left />
            <h1 class="h1 mx-4 mb-2">{{ __("Owner") }}</h1>
            <CompanyCard :company class="!bg-bg dark:!bg-dark-surface" />
            <template v-if="company?.business_hours">
                <h1 class="h1 mx-4 mb-2">{{ __("Better to write") }}</h1>
                <ShowBusinessHours :data="company.business_hours" />
            </template>
            <div v-if="locations.length">
                <h1 class="h1 mx-4 mb-2">{{ __("Where to use") }}</h1>
                <LocationCard v-for="location in locations" :location withBusinessHours />
            </div>
            <Comments :modelClass="'Coupon'" :modelId="coupon.id" />
        </div>
        <div v-else>
            <ErrorWidget :message="status === 403 ? __('Resource is not available') : __('Coupon not found...')" back />
        </div>
    </div>
</template>
