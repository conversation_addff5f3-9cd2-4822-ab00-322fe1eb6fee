<script setup>
import { onMounted, onUnmounted } from "vue";
import CouponCard from "../../Features/Coupon/CouponCard.vue";
import { useHeaderStore } from "@m/Fixed";
import LocationCard from "../../Features/Location/LocationCard.vue";
import CompanyCard from "../../Features/Company/CompanyCard.vue";
import { ErrorWidget } from "@s";

const props = defineProps({
    location: [Object, null],
    coupons: [Object, null],
    company: [Object, null],
    status: [Number, null],
})

const header = useHeaderStore();

onMounted(() => {
    header.title = 'View location' + (props.location ? (' | ' + props.location?.name) : '');
})

onUnmounted(() => {
    header.clear();
});

</script>

<template>

    <Head :title="__(header.title)" />
    <div class="card-position flex gap-2 max-sm:flex-col">
        <template v-if="location">
            <LocationCard :location full v-motion-slide-visible-once-left />
            <h1 class="h1 mx-4 my-2" v-if="company?.id">{{ __("Owner") }}</h1>
            <CompanyCard :company v-if="company?.id" class="!bg-bg dark:!bg-dark-surface" />
            <div v-if="coupons?.length">
                <h1 class="h1 mx-4 my-2">{{ __("Coupons") }}</h1>
                <CouponCard v-for="coupon in coupons" :coupon="coupon" />
            </div>
        </template>
        <template v-else>
            <ErrorWidget :message="status === 403 ? __('Resource is not available') : __('Location not found...')" back />
        </template>
    </div>
</template>
