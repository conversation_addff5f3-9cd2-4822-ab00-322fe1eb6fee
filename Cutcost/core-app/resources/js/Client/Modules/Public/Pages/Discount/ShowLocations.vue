<script setup>
import { onMounted, onUnmounted } from "vue";
import Map from "../../Widgets/Map/Map.vue";
import Locations from "../../Widgets/Feeds/Locations.vue";
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

defineProps({
    locations: Object,
})

const options = [
    { id: 'locations', name: 'locations' },
    { id: 'map', name: 'map' },
];

onMounted(() => {
    header.navigation = 'locations';
    header.options = options;
    header.chosenTab = localStorage.getItem(header.navigation + '.selected') ?? 'locations';
    header.swipeEvent();
});

onUnmounted(() => {
    header.clear();
});

</script>

<template>

    <Head :title="__('Locations')" />
    <div class="card-position">
        <div v-show="header.chosenTab === 'locations'" >
            <Locations type="location_feed" />
        </div>
    </div>
    <div v-if="header.chosenTab === 'map'">
        <Map :locations class="h-[calc(100vh-128px)] sm:h-[calc(100vh-120px)] w-full z-10" />
    </div>
</template>
