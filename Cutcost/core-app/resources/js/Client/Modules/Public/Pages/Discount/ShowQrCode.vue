<script setup>
import QrcodeVue from 'qrcode.vue'
import { onMounted, ref } from "vue";
import { CouponMiniSearchResult } from "@s";
import { Head } from "@inertiajs/vue3";

const props = defineProps({
  coupon: Object,
});

const qr = ref("");
const size = ref(400);

onMounted(() => {

  // encode first
  qr.value = btoa(JSON.stringify({
    coupon: {
      coupon_id: props.coupon.id,
      user_id: global.user.id,
    },
  }));
});
</script>
<template>

  <Head :title="`${__('QR Code')} ${coupon.name} - `">
  </Head>
  <div class="card-position">
    <Card>
      <div class="my-12 flex items-center justify-center" v-motion-slide-visible-top>
        <div class="relative rounded-3xl bg-white/70 dark:bg-white/10 backdrop-blur-xl shadow-lg
           border border-gray-200 dark:border-white/10 px-8 py-10 max-w-sm w-full">
          <div class="absolute inset-0 rounded-3xl pointer-events-none">
          </div>

          <h2 class="relative mb-6 text-center text-3xl font-semibold text-gray-900 dark:text-gray-100 tracking-tight">
            {{ __("Scan in store") }}
          </h2>

          <div class="relative mx-auto flex items-center justify-center rounded-2xl border border-gray-300/60 
             dark:border-white/20 bg-white p-6 shadow-inner">
            <qrcode-vue :value="qr" :size="size" level="H" class="!w-48 !h-48" />
          </div>

          <p class="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
            {{ __('Show code at checkout') }}
          </p>
        </div>
      </div>
      <div class="my-3 flex items-center mx-auto sm:w-[25vw]">
        <CouponMiniSearchResult :coupon class="w-full" />
      </div>

    </Card>
  </div>
</template>
