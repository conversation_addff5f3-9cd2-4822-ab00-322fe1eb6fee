          {{ __("rewards remain") }}
<script setup>
import { computed } from "vue";
import { useHelpers } from "@s";

const props = defineProps({
    status: Number,
});

const helpers = useHelpers();

const title = computed(() => {
    switch (props.status) {
        case 503:
            return "Paslauga neprieinama / Service Unavailable";
        case 500:
            return "Vidinė serverio klaida / Internal Server Error";
        case 404:
            return "Puslapis nerastas / Page Not Found";
        case 403:
            return "Prieigos draudimas / Access Denied";
        default:
            return "";
    }
});

const description = computed(() => {
    switch (props.status) {
        case 503:
            return "Šiuo metu paslauga negalima. Bandykite dar kartą vėliau. / The service is currently unavailable. Please try again later.";
        case 500:
            return "Įvyko serverio klaida. Prašome bandyti vėliau. / An internal server error occurred. Please try again later.";
        case 404:
            return "Ieškomas puslapis nerastas. Patikrinkite URL ir bandykite dar kartą. / The requested page was not found. Check the URL and try again.";
        case 403:
            return "Neturite prieigos teisės šiai puslapiui. / You do not have permission to access this page.";
        default:
            return "";
    }
});
</script>

<template>

    <Head>
        <title>
            {{ title }} - Cutcost
        </title>
    </Head>
    <div class="card-position sm:!mx-30 text-center text-text dark:text-dark-accent">
        <h1 class="mb-6 text-4xl font-extrabold">{{ title }}</h1>
        <p class="mb-8 text-lg leading-relaxed">{{ description }}</p>
        <div class="flex justify-center mb-10">
            <x-button v-if="status !== 503" @click="helpers.back()">
                Atgal / Back / Назад
            </x-button>
        </div>
        <div class="relative w-full max-w-md mx-auto">
            <!-- <img src="/public/assets/images/error.jpg" alt="Error illustration"
                class="w-full opacity-0 animate-fadeIn" /> -->
            <div class="absolute left-0 right-2 text-center  font-bold italic text-xl max-sm:top-[48%] top-[50%] ">
                {{ status }}
            </div>
        </div>
    </div>
</template>

<style scoped>
@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 1s forwards ease-in-out;
}
</style>
