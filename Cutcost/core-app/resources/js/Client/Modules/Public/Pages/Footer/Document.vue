<script setup>
import { computed } from 'vue';


const props = defineProps({
    document: Object,
})

const updatedDate = computed(() => {
    return new Date(props.document.updated_at).toLocaleDateString();
})

</script>

<template>

    <Head>
        <title>{{ document.title }} </title>
    </Head>
    <div class="card-position">
        <Card :title="document.title">
            <div class="text-accent dark:text-dark-accent">
                {{ __('Last updated') }}: {{ updatedDate }}
            </div>
            <div v-html="document.content" class="!prose !prose-stone dark:!prose-invert">

            </div>
        </Card>
    </div>
</template>
