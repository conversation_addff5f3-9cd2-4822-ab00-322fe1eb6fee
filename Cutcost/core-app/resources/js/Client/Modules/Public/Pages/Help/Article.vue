<script setup>
import { Link } from '@inertiajs/vue3'
import { PhArrowLeft, PhClock, PhTag } from '@phosphor-icons/vue'

const props = defineProps({
  article: Object
})

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<template>
  <Head>
    <title>{{ __(article.title) }} - {{ __("Help") }} - CutCost</title>
  </Head>

  <div class="card-position">
    <Card noStyle>
      <!-- Breadcrumb -->
      <div class="flex items-center mb-6 text-sm">
        <Link 
          :href="route('help.index')"
          class="text-text-secondary dark:text-dark-text-secondary hover:text-primary transition-colors"
        >
          {{ __("Help Center") }}
        </Link>
        <span class="mx-2 text-text-secondary dark:text-dark-text-secondary">/</span>
        <span class="text-primary">{{ __(article.category) }}</span>
      </div>

      <!-- Back Button -->
      <div class="mb-6">
        <Link 
          :href="route('help.index')"
          class="inline-flex items-center text-text-secondary dark:text-dark-text-secondary hover:text-primary transition-colors"
        >
          <PhArrowLeft :size="20" class="mr-1" />
          {{ __("Back to Help Center") }}
        </Link>
      </div>

      <!-- Article Header -->
      <div class="mb-8">
        <div class="flex items-center gap-4 mb-4">
          <div class="flex items-center text-sm text-text-secondary dark:text-dark-text-secondary">
            <PhTag :size="16" class="mr-1" />
            <span>{{ __(article.category) }}</span>
          </div>
          <div class="flex items-center text-sm text-text-secondary dark:text-dark-text-secondary">
            <PhClock :size="16" class="mr-1" />
            <span>{{ __("Updated") }} {{ formatDate(article.updated_at) }}</span>
          </div>
        </div>
        
        <h1 class="text-3xl font-bold text-text dark:text-dark-text mb-4">
          {{ __(article.title) }}
        </h1>
        
        <p v-if="article.description" class="text-lg text-text-secondary dark:text-dark-text-secondary">
          {{ __(article.description) }}
        </p>
      </div>

      <!-- Article Content -->
      <div class="bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border p-8">
        <div class="prose prose-lg max-w-none dark:prose-invert">
          <!-- This would typically render markdown or rich text content -->
          <div v-html="article.content"></div>
          
          <!-- Fallback for plain text content -->
          <div v-if="!article.content" class="space-y-4">
            <p class="text-text dark:text-dark-text leading-relaxed">
              {{ __("This help article is currently being updated. Please check back soon or contact our support team for assistance.") }}
            </p>
          </div>
        </div>
      </div>

      <!-- Was this helpful? -->
      <div class="mt-8 p-6 bg-gray-50 dark:bg-dark-card/50 rounded-xl text-center">
        <h3 class="text-lg font-semibold text-text dark:text-dark-text mb-4">
          {{ __("Was this article helpful?") }}
        </h3>
        
        <div class="flex justify-center gap-4 mb-6">
          <button class="px-6 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors">
            👍 {{ __("Yes") }}
          </button>
          <button class="px-6 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors">
            👎 {{ __("No") }}
          </button>
        </div>

        <p class="text-text-secondary dark:text-dark-text-secondary mb-4">
          {{ __("Still need help? Our support team is here for you.") }}
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Link 
            :href="route('help.contact')"
            class="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            {{ __("Contact Support") }}
          </Link>
          <Link 
            :href="route('help.faq')"
            class="inline-flex items-center justify-center px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
          >
            {{ __("Browse FAQ") }}
          </Link>
        </div>
      </div>

      <!-- Back to Help Center -->
      <div class="mt-12 text-center">
        <Link
          :href="route('help.index')"
          class="inline-flex items-center justify-center px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
        >
          {{ __("Back to Help Center") }}
        </Link>
      </div>
    </Card>
  </div>
</template>
