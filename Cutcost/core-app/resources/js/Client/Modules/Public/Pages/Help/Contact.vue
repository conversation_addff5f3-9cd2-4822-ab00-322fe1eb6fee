<script setup>
import { ref } from 'vue'
import { Link, useForm } from '@inertiajs/vue3'
import { 
  PhArrowLeft, 
  PhChatCircle, 
  PhEnvelope, 
  PhPhone,
  PhClock,
  PhCheckCircle,
  PhWarning
} from '@phosphor-icons/vue'

const props = defineProps({
  supportChannels: Array
})

const form = useForm({
  name: '',
  email: '',
  subject: '',
  message: '',
  category: 'general'
})

const categories = [
  { value: 'general', label: 'General Question' },
  { value: 'technical', label: 'Technical Issue' },
  { value: 'account', label: 'Account Problem' },
  { value: 'billing', label: 'Billing Question' },
  { value: 'business', label: 'Business Inquiry' },
  { value: 'bug', label: 'Bug Report' },
  { value: 'feature', label: 'Feature Request' }
]

const submitForm = () => {
  form.post(route('help.contact'), {
    onSuccess: () => {
      // Handle success
    }
  })
}

const getChannelIcon = (type) => {
  switch (type) {
    case 'chat': return PhChatCircle
    case 'email': return PhEnvelope
    case 'phone': return PhPhone
    default: return PhChatCircle
  }
}
</script>

<template>
  <Head>
    <title>{{ __("Contact Support") }} - CutCost</title>
  </Head>

  <div class="card-position">
    <Card noStyle>
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <Link 
            :href="route('help.index')"
            class="flex items-center text-text-secondary dark:text-dark-text-secondary hover:text-primary transition-colors mr-4"
          >
            <PhArrowLeft :size="20" class="mr-1" />
            {{ __("Back to Help") }}
          </Link>
        </div>
        
        <h1 class="text-3xl font-bold text-text dark:text-dark-text mb-4">
          {{ __("Contact Support") }}
        </h1>
        <p class="text-lg text-text-secondary dark:text-dark-text-secondary">
          {{ __("Get in touch with our support team for help with your account or technical issues") }}
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Support Channels -->
        <div class="lg:col-span-1">
          <h2 class="text-xl font-semibold text-text dark:text-dark-text mb-6">
            {{ __("Support Channels") }}
          </h2>
          
          <div class="space-y-4">
            <div
              v-for="channel in supportChannels"
              :key="channel.type"
              class="p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border"
              :class="{ 'opacity-50': !channel.available }"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                  <component :is="getChannelIcon(channel.type)" :size="20" class="text-primary" />
                </div>
                <div class="flex-1">
                  <h3 class="font-medium text-text dark:text-dark-text mb-1">
                    {{ __(channel.title) }}
                  </h3>
                  <p class="text-sm text-text-secondary dark:text-dark-text-secondary mb-2">
                    {{ __(channel.description) }}
                  </p>
                  
                  <div v-if="channel.available" class="flex items-center text-xs text-green-600 dark:text-green-400">
                    <PhCheckCircle :size="16" class="mr-1" />
                    <span v-if="channel.hours">{{ __(channel.hours) }}</span>
                    <span v-else-if="channel.response_time">{{ __(channel.response_time) }}</span>
                  </div>
                  
                  <div v-else class="flex items-center text-xs text-orange-600 dark:text-orange-400">
                    <PhWarning :size="16" class="mr-1" />
                    <span>{{ __(channel.note) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Tips -->
          <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 class="font-medium text-blue-900 dark:text-blue-100 mb-2">
              {{ __("Before contacting support") }}
            </h3>
            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• {{ __("Check our FAQ for quick answers") }}</li>
              <li>• {{ __("Try refreshing the page or app") }}</li>
              <li>• {{ __("Include screenshots if reporting a bug") }}</li>
              <li>• {{ __("Provide your account email") }}</li>
            </ul>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="lg:col-span-2">
          <div class="bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border p-6">
            <h2 class="text-xl font-semibold text-text dark:text-dark-text mb-6">
              {{ __("Send us a message") }}
            </h2>

            <form @submit.prevent="submitForm" class="space-y-6">
              <!-- Name and Email -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-text dark:text-dark-text mb-2">
                    {{ __("Your Name") }} *
                  </label>
                  <input
                    v-model="form.name"
                    type="text"
                    required
                    class="w-full px-4 py-3 border border-border dark:border-dark-border rounded-lg bg-white dark:bg-dark-input focus:outline-none focus:ring-2 focus:ring-primary"
                    :class="{ 'border-red-500': form.errors.name }"
                  />
                  <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">
                    {{ form.errors.name }}
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-text dark:text-dark-text mb-2">
                    {{ __("Email Address") }} *
                  </label>
                  <input
                    v-model="form.email"
                    type="email"
                    required
                    class="w-full px-4 py-3 border border-border dark:border-dark-border rounded-lg bg-white dark:bg-dark-input focus:outline-none focus:ring-2 focus:ring-primary"
                    :class="{ 'border-red-500': form.errors.email }"
                  />
                  <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">
                    {{ form.errors.email }}
                  </div>
                </div>
              </div>

              <!-- Category -->
              <div>
                <label class="block text-sm font-medium text-text dark:text-dark-text mb-2">
                  {{ __("Category") }}
                </label>
                <select
                  v-model="form.category"
                  class="w-full px-4 py-3 border border-border dark:border-dark-border rounded-lg bg-white dark:bg-dark-input focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option v-for="category in categories" :key="category.value" :value="category.value">
                    {{ __(category.label) }}
                  </option>
                </select>
              </div>

              <!-- Subject -->
              <div>
                <label class="block text-sm font-medium text-text dark:text-dark-text mb-2">
                  {{ __("Subject") }} *
                </label>
                <input
                  v-model="form.subject"
                  type="text"
                  required
                  class="w-full px-4 py-3 border border-border dark:border-dark-border rounded-lg bg-white dark:bg-dark-input focus:outline-none focus:ring-2 focus:ring-primary"
                  :class="{ 'border-red-500': form.errors.subject }"
                />
                <div v-if="form.errors.subject" class="text-red-500 text-sm mt-1">
                  {{ form.errors.subject }}
                </div>
              </div>

              <!-- Message -->
              <div>
                <label class="block text-sm font-medium text-text dark:text-dark-text mb-2">
                  {{ __("Message") }} *
                </label>
                <textarea
                  v-model="form.message"
                  rows="6"
                  required
                  :placeholder="__('Please describe your issue or question in detail...')"
                  class="w-full px-4 py-3 border border-border dark:border-dark-border rounded-lg bg-white dark:bg-dark-input focus:outline-none focus:ring-2 focus:ring-primary resize-vertical"
                  :class="{ 'border-red-500': form.errors.message }"
                ></textarea>
                <div v-if="form.errors.message" class="text-red-500 text-sm mt-1">
                  {{ form.errors.message }}
                </div>
              </div>

              <!-- Submit Button -->
              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="form.processing"
                  class="px-8 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="form.processing">{{ __("Sending...") }}</span>
                  <span v-else>{{ __("Send Message") }}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Alternative Contact Methods -->
      <div class="mt-12 p-6 bg-gray-50 dark:bg-dark-card/50 rounded-xl">
        <h3 class="text-lg font-semibold text-text dark:text-dark-text mb-4 text-center">
          {{ __("Other ways to get help") }}
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link 
            :href="route('help.faq')"
            class="flex items-center justify-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-primary/20 transition-colors">
                <PhChatCircle :size="24" class="text-primary" />
              </div>
              <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("Browse FAQ") }}</span>
            </div>
          </Link>

          <Link 
            :href="route('help.search')"
            class="flex items-center justify-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-primary/20 transition-colors">
                <PhChatCircle :size="24" class="text-primary" />
              </div>
              <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("Search Help") }}</span>
            </div>
          </Link>

          <Link 
            :href="route('contact.us')"
            class="flex items-center justify-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-primary/20 transition-colors">
                <PhEnvelope :size="24" class="text-primary" />
              </div>
              <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("General Contact") }}</span>
            </div>
          </Link>
        </div>
      </div>
    </Card>
  </div>
</template>
