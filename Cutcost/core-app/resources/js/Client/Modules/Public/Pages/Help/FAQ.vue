<script setup>
import { ref, computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { PhCaretDown, PhArrowLeft, PhMagnifyingGlass } from '@phosphor-icons/vue'

const props = defineProps({
  faqs: Array
})

const openItems = ref(new Set())
const searchQuery = ref('')

const toggleItem = (categoryIndex, questionIndex) => {
  const key = `${categoryIndex}-${questionIndex}`
  if (openItems.value.has(key)) {
    openItems.value.delete(key)
  } else {
    openItems.value.add(key)
  }
}

const isOpen = (categoryIndex, questionIndex) => {
  return openItems.value.has(`${categoryIndex}-${questionIndex}`)
}

const filteredFaqs = computed(() => {
  if (!searchQuery.value || !props.faqs) return props.faqs || []

  const query = searchQuery.value.toLowerCase()
  return props.faqs.map(category => ({
    ...category,
    questions: category.questions.filter(q =>
      q.question.toLowerCase().includes(query) ||
      q.answer.toLowerCase().includes(query)
    )
  })).filter(category => category.questions.length > 0)
})
</script>

<template>
  <Head>
    <title>{{ __("Frequently Asked Questions") }} - CutCost</title>
  </Head>

  <div class="card-position">
    <Card noStyle>
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <Link 
            :href="route('help.index')"
            class="flex items-center text-text-secondary dark:text-dark-text-secondary hover:text-primary transition-colors mr-4"
          >
            <PhArrowLeft :size="20" class="mr-1" />
            {{ __("Back to Help") }}
          </Link>
        </div>
        
        <h1 class="text-3xl font-bold text-text dark:text-dark-text mb-4">
          {{ __("Frequently Asked Questions") }}
        </h1>
        <p class="text-lg text-text-secondary dark:text-dark-text-secondary">
          {{ __("Find quick answers to the most common questions about CutCost") }}
        </p>
      </div>

      <!-- Search -->
      <div class="mb-8">
        <div class="relative max-w-md">
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="__('Search FAQ...')"
            class="w-full px-4 py-3 pr-12 border border-border dark:border-dark-border rounded-lg bg-white dark:bg-dark-card focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <PhMagnifyingGlass 
            :size="20" 
            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-text-secondary dark:text-dark-text-secondary"
          />
        </div>
      </div>

      <!-- FAQ Categories -->
      <div class="space-y-8">
        <div
          v-for="(category, categoryIndex) in filteredFaqs"
          :key="category.category"
          class="bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border overflow-hidden"
        >
          <div class="bg-gray-50 dark:bg-dark-card/50 px-6 py-4 border-b border-border dark:border-dark-border">
            <h2 class="text-xl font-semibold text-text dark:text-dark-text">
              {{ __(category.category) }}
            </h2>
          </div>

          <!-- Questions -->
          <div class="divide-y divide-border dark:divide-dark-border">
            <div
              v-for="(faq, questionIndex) in category.questions"
              :key="questionIndex"
              class="transition-all duration-200"
            >
              <!-- Question -->
              <button
                @click="toggleItem(categoryIndex, questionIndex)"
                class="w-full px-6 py-4 text-left hover:bg-gray-50 dark:hover:bg-dark-card/50 transition-colors focus:outline-none focus:bg-gray-50 dark:focus:bg-dark-card/50"
              >
                <div class="flex items-center justify-between">
                  <h3 class="font-medium text-text dark:text-dark-text pr-4">
                    {{ __(faq.question) }}
                  </h3>
                  <PhCaretDown 
                    :size="20" 
                    class="text-text-secondary dark:text-dark-text-secondary transition-transform duration-200 flex-shrink-0"
                    :class="{ 'rotate-180': isOpen(categoryIndex, questionIndex) }"
                  />
                </div>
              </button>

              <!-- Answer -->
              <div
                v-show="isOpen(categoryIndex, questionIndex)"
                class="px-6 pb-4 text-text-secondary dark:text-dark-text-secondary leading-relaxed"
              >
                <div class="prose prose-sm max-w-none dark:prose-invert">
                  <p>{{ __(faq.answer) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div 
        v-if="searchQuery && filteredFaqs.length === 0"
        class="text-center py-12"
      >
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-2">
          {{ __("No results found") }}
        </h3>
        <p class="text-text-secondary dark:text-dark-text-secondary mb-6">
          {{ __("Try searching with different keywords or browse our help categories") }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            @click="searchQuery = ''"
            class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            {{ __("Clear Search") }}
          </button>
          <Link 
            :href="route('help.index')"
            class="px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
          >
            {{ __("Browse Help Center") }}
          </Link>
        </div>
      </div>

      <!-- Still Need Help -->
      <div class="mt-12 p-8 bg-primary/5 rounded-xl text-center">
        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-2">
          {{ __("Didn't find what you're looking for?") }}
        </h3>
        <p class="text-text-secondary dark:text-dark-text-secondary mb-6">
          {{ __("Our support team is ready to help you with any questions") }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Link 
            :href="route('help.contact')"
            class="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            {{ __("Contact Support") }}
          </Link>
          <Link 
            :href="route('help.search')"
            class="inline-flex items-center justify-center px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
          >
            <PhMagnifyingGlass :size="20" class="mr-2" />
            {{ __("Search Help Articles") }}
          </Link>
        </div>
      </div>
    </Card>
  </div>
</template>
