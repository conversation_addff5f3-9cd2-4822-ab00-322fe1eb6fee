<script setup>
import { Link } from '@inertiajs/vue3'
import {
  Ph<PERSON>ocket,
  PhUser,
  Ph<PERSON><PERSON>ing,
  PhMagnifyingGlass,
  PhQuestion,
  PhChatCircle,
  PhHeart,
  PhTarget,
  PhUsers
} from '@phosphor-icons/vue'

const props = defineProps({
  aboutContent: Object,
  categories: Array,
  popularTopics: Array
})

const iconMap = {
  rocket: PhRocket,
  user: PhUser,
  building: PhBuilding
}

const getIcon = (iconName) => iconMap[iconName] || PhQuestion
</script>

<template>
  <Head>
    <title>{{ __("Help Center") }} - CutCost</title>
  </Head>

  <div class="card-position">
    <Card noStyle>
      <!-- Hero Section -->
      <div class="text-center mb-16">
        <h1 class="text-5xl font-bold text-text dark:text-dark-text mb-6">
          {{ __(aboutContent.title) }}
        </h1>
        <p class="text-xl text-text-secondary dark:text-dark-text-secondary mb-8 max-w-3xl mx-auto">
          {{ __(aboutContent.description) }}
        </p>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto relative mb-8">
          <form :action="route('help.search')" method="GET" class="relative">
            <input
              type="text"
              name="q"
              :placeholder="__('Search help articles...')"
              class="w-full px-6 py-4 pr-14 text-lg border border-border dark:border-dark-border rounded-xl bg-white dark:bg-dark-card focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <button
              type="submit"
              class="absolute right-4 top-1/2 transform -translate-y-1/2 text-text-secondary hover:text-primary transition-colors"
            >
              <PhMagnifyingGlass :size="24" />
            </button>
          </form>
        </div>
      </div>

      <!-- About Us Section -->
      <div class="mb-16">
        <div class="max-w-4xl mx-auto">
          <div class="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8 mb-12">
            <div class="text-center mb-8">
              <h2 class="text-3xl font-bold text-text dark:text-dark-text mb-4">
                {{ __("About CutCost") }}
              </h2>
              <p class="text-lg text-text-secondary dark:text-dark-text-secondary leading-relaxed">
                {{ __(aboutContent.content) }}
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                  <PhHeart :size="32" class="text-primary" />
                </div>
                <h3 class="font-semibold text-text dark:text-dark-text mb-2">{{ __("Our Mission") }}</h3>
                <p class="text-sm text-text-secondary dark:text-dark-text-secondary">
                  {{ __(aboutContent.mission) }}
                </p>
              </div>

              <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                  <PhTarget :size="32" class="text-primary" />
                </div>
                <h3 class="font-semibold text-text dark:text-dark-text mb-2">{{ __("What We Do") }}</h3>
                <p class="text-sm text-text-secondary dark:text-dark-text-secondary">
                  {{ __("Connect customers with local businesses through exclusive deals and promotions") }}
                </p>
              </div>

              <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                  <PhUsers :size="32" class="text-primary" />
                </div>
                <h3 class="font-semibold text-text dark:text-dark-text mb-2">{{ __("Community") }}</h3>
                <p class="text-sm text-text-secondary dark:text-dark-text-secondary">
                  {{ __("Building stronger local communities through savings and support") }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12 max-w-2xl mx-auto">
        <Link
          :href="route('help.faq')"
          class="flex items-center p-6 bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border hover:border-primary dark:hover:border-primary transition-all hover:shadow-lg group"
        >
          <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-primary/20 transition-colors">
            <PhQuestion :size="24" class="text-primary" />
          </div>
          <div>
            <h3 class="font-semibold text-text dark:text-dark-text mb-1">{{ __("FAQ") }}</h3>
            <p class="text-sm text-text-secondary dark:text-dark-text-secondary">{{ __("Frequently asked questions") }}</p>
          </div>
        </Link>

        <Link
          :href="route('help.contact')"
          class="flex items-center p-6 bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border hover:border-primary dark:hover:border-primary transition-all hover:shadow-lg group"
        >
          <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-primary/20 transition-colors">
            <PhChatCircle :size="24" class="text-primary" />
          </div>
          <div>
            <h3 class="font-semibold text-text dark:text-dark-text mb-1">{{ __("Contact Support") }}</h3>
            <p class="text-sm text-text-secondary dark:text-dark-text-secondary">{{ __("Get in touch with our team") }}</p>
          </div>
        </Link>
      </div>

      <!-- Help Categories -->
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-text dark:text-dark-text mb-8 text-center">
          {{ __("How can we help?") }}
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div
            v-for="category in categories"
            :key="category.id"
            class="bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border p-6 hover:shadow-lg transition-all text-center group cursor-pointer"
          >
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
              <component :is="getIcon(category.icon)" :size="32" class="text-primary" />
            </div>
            <h3 class="font-semibold text-text dark:text-dark-text mb-2">{{ __(category.title) }}</h3>
            <p class="text-sm text-text-secondary dark:text-dark-text-secondary">{{ __(category.description) }}</p>
          </div>
        </div>
      </div>

      <!-- Popular Topics -->
      <div class="bg-gray-50 dark:bg-dark-card/50 rounded-xl p-8">
        <h2 class="text-xl font-bold text-text dark:text-dark-text mb-6 text-center">
          {{ __("Popular Help Topics") }}
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
          <Link
            v-for="topic in popularTopics"
            :key="topic.slug"
            :href="route('help.article', topic.slug)"
            class="flex items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary dark:hover:border-primary transition-all group"
          >
            <div class="w-2 h-2 bg-primary rounded-full mr-3 group-hover:scale-125 transition-transform"></div>
            <span class="text-text dark:text-dark-text group-hover:text-primary transition-colors">
              {{ __(topic.title) }}
            </span>
          </Link>
        </div>
      </div>

      <!-- Still Need Help -->
      <div class="text-center mt-16 p-8 bg-primary/5 rounded-xl">
        <h3 class="text-2xl font-semibold text-text dark:text-dark-text mb-4">
          {{ __("Still need help?") }}
        </h3>
        <p class="text-text-secondary dark:text-dark-text-secondary mb-8 text-lg">
          {{ __("Our support team is here to help you with any questions or issues") }}
        </p>
        <Link
          :href="route('help.contact')"
          class="inline-flex items-center justify-center px-8 py-4 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-lg font-medium"
        >
          <PhChatCircle :size="24" class="mr-3" />
          {{ __("Contact Support") }}
        </Link>
      </div>
    </Card>
  </div>
</template>
