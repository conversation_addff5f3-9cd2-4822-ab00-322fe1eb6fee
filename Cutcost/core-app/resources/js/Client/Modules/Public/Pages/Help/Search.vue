<script setup>
import { ref, computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { PhArrowLeft, PhMagnifyingGlass, PhArticle, PhTag } from '@phosphor-icons/vue'

const props = defineProps({
  query: String,
  results: Array,
  popularSearches: {
    type: Array,
    default: () => [
      'how to redeem coupon',
      'create account',
      'reset password',
      'business registration',
      'payment issues',
      'delete account'
    ]
  }
})

const searchQuery = ref(props.query)

const hasResults = computed(() => props.results && props.results.length > 0)
</script>

<template>
  <Head>
    <title>{{ __("Search Results") }} - {{ __("Help") }} - CutCost</title>
  </Head>

  <div class="card-position">
    <Card noStyle>
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <Link 
            :href="route('help.index')"
            class="flex items-center text-text-secondary dark:text-dark-text-secondary hover:text-primary transition-colors mr-4"
          >
            <PhArrowLeft :size="20" class="mr-1" />
            {{ __("Back to Help") }}
          </Link>
        </div>
        
        <h1 class="text-3xl font-bold text-text dark:text-dark-text mb-4">
          {{ __("Search Help Articles") }}
        </h1>
        <p v-if="query" class="text-lg text-text-secondary dark:text-dark-text-secondary">
          {{ __("Results for") }} "<span class="font-medium">{{ query }}</span>"
        </p>
      </div>

      <!-- Search Bar -->
      <div class="mb-8">
        <form :action="route('help.search')" method="GET" class="relative max-w-2xl">
          <input
            v-model="searchQuery"
            type="text"
            name="q"
            :placeholder="__('Search help articles...')"
            class="w-full px-6 py-4 pr-14 text-lg border border-border dark:border-dark-border rounded-xl bg-white dark:bg-dark-card focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <button
            type="submit"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-text-secondary hover:text-primary transition-colors"
          >
            <PhMagnifyingGlass :size="24" />
          </button>
        </form>
      </div>

      <!-- Search Results -->
      <div v-if="hasResults" class="mb-12">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-text dark:text-dark-text">
            {{ __("Search Results") }}
          </h2>
          <span class="text-sm text-text-secondary dark:text-dark-text-secondary">
            {{ results.length }} {{ __("results found") }}
          </span>
        </div>

        <div class="space-y-4">
          <Link
            v-for="result in results"
            :key="result.slug"
            :href="route('help.article', result.slug)"
            class="block p-6 bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border hover:border-primary hover:shadow-lg transition-all group"
          >
            <div class="flex items-start">
              <div class="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-primary/20 transition-colors">
                <PhArticle :size="20" class="text-primary" />
              </div>
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <PhTag :size="16" class="text-text-secondary dark:text-dark-text-secondary" />
                  <span class="text-sm text-text-secondary dark:text-dark-text-secondary">
                    {{ __(result.category) }}
                  </span>
                </div>
                <h3 class="text-lg font-semibold text-text dark:text-dark-text group-hover:text-primary transition-colors mb-2">
                  {{ __(result.title) }}
                </h3>
                <p class="text-text-secondary dark:text-dark-text-secondary">
                  {{ __(result.description) }}
                </p>
              </div>
            </div>
          </Link>
        </div>
      </div>

      <!-- No Results -->
      <div v-else-if="query" class="text-center py-12 mb-12">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-2">
          {{ __("No results found") }}
        </h3>
        <p class="text-text-secondary dark:text-dark-text-secondary mb-6">
          {{ __("We couldn't find any articles matching your search. Try different keywords or browse our help categories.") }}
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <Link 
            :href="route('help.index')"
            class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            {{ __("Browse Help Center") }}
          </Link>
          <Link 
            :href="route('help.faq')"
            class="px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
          >
            {{ __("View FAQ") }}
          </Link>
        </div>

        <!-- Search Tips -->
        <div class="max-w-md mx-auto p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">
            {{ __("Search Tips") }}
          </h4>
          <ul class="text-sm text-blue-800 dark:text-blue-200 text-left space-y-1">
            <li>• {{ __("Try using different keywords") }}</li>
            <li>• {{ __("Check your spelling") }}</li>
            <li>• {{ __("Use more general terms") }}</li>
            <li>• {{ __("Try searching for related topics") }}</li>
          </ul>
        </div>
      </div>

      <!-- Popular Searches -->
      <div class="mb-12">
        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-6">
          {{ query ? __("Popular Searches") : __("Try searching for") }}
        </h3>
        
        <div class="flex flex-wrap gap-3">
          <Link
            v-for="search in props.popularSearches"
            :key="search"
            :href="route('help.search', { q: search })"
            class="px-4 py-2 bg-white dark:bg-dark-card border border-border dark:border-dark-border rounded-full text-sm text-text-secondary dark:text-dark-text-secondary hover:border-primary hover:text-primary transition-colors"
          >
            {{ __(search) }}
          </Link>
        </div>
      </div>

      <!-- Help Categories -->
      <div class="bg-gray-50 dark:bg-dark-card/50 rounded-xl p-8">
        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-6 text-center">
          {{ __("Browse by Category") }}
        </h3>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Link 
            :href="route('help.index')"
            class="flex flex-col items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group text-center"
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
              <PhArticle :size="24" class="text-primary" />
            </div>
            <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("Getting Started") }}</span>
          </Link>

          <Link 
            :href="route('help.index')"
            class="flex flex-col items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group text-center"
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
              <PhArticle :size="24" class="text-primary" />
            </div>
            <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("For Users") }}</span>
          </Link>

          <Link 
            :href="route('help.index')"
            class="flex flex-col items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group text-center"
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
              <PhArticle :size="24" class="text-primary" />
            </div>
            <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("For Businesses") }}</span>
          </Link>

          <Link 
            :href="route('help.index')"
            class="flex flex-col items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group text-center"
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
              <PhArticle :size="24" class="text-primary" />
            </div>
            <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("Account") }}</span>
          </Link>

          <Link 
            :href="route('help.index')"
            class="flex flex-col items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group text-center"
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
              <PhArticle :size="24" class="text-primary" />
            </div>
            <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("Troubleshooting") }}</span>
          </Link>

          <Link 
            :href="route('help.index')"
            class="flex flex-col items-center p-4 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary transition-colors group text-center"
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
              <PhArticle :size="24" class="text-primary" />
            </div>
            <span class="text-sm font-medium text-text dark:text-dark-text">{{ __("Policies") }}</span>
          </Link>
        </div>
      </div>

      <!-- Still Need Help -->
      <div class="mt-12 text-center p-8 bg-primary/5 rounded-xl">
        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-2">
          {{ __("Still can't find what you're looking for?") }}
        </h3>
        <p class="text-text-secondary dark:text-dark-text-secondary mb-6">
          {{ __("Our support team is ready to help you with any questions") }}
        </p>
        <Link 
          :href="route('help.contact')"
          class="inline-flex items-center justify-center px-8 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        >
          {{ __("Contact Support") }}
        </Link>
      </div>
    </Card>
  </div>
</template>
