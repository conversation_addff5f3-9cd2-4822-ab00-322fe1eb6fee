<script setup>
import { Head } from "@inertiajs/vue3";
import ArticleCard from "../../Widgets/Information/ArticleCard.vue";

defineProps({
    articles: Object,
});
</script>

<template>

    <Head :title="__('Blog') + ' -'" />

    <div class="card-position">
        <h1 class="h1 format-accent mb-4 content-position">
            {{ __("Latest news") }}
        </h1>

        <div v-if="articles.data.length" class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
            <ArticleCard v-for="article in articles.data" :key="article.id" :article class="card p-4">
            </ArticleCard>
        </div>

        <div v-else class="mt-4 flex h-28 w-full justify-center">
            <h1 class="h1 text-text m-auto text-center">
                {{ __("Nothing") }}
            </h1>
        </div>

        <Paginator :pagination="articles" />
    </div>
</template>
