<script setup>
import { Head } from "@inertiajs/vue3";
import { PhDotsThree } from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";
import { computed } from "vue";
import { inject } from "vue";

const route = inject("route");

const global = useGlobalStore();

const props = defineProps({
  article: Object,
});

const ownerName = computed(() => {
  return (
    props.article.company.name
  );
});
</script>
<template>

  <Head>
    <title>{{ article.name }} </title>
  </Head>
  <div class="card-position !mx-4">
    <!-- Article Header -->
    <div class="post-position">
      <div class="mb-8 flex">
        <div class="w-full">
          <div class="flex">
            <h1 class="h1 format-accent">{{ article.name }}</h1>

            <div class="ml-auto" v-if="global.user?.company?.id === article?.company_id">
              <Dropdown>
                <template #button>
                  <x-button>
                    <template #icon>
                      <PhDotsThree :size="24" weight="bold" />
                    </template>
                  </x-button>
                </template>
                <DropdownItem as="Link" :href="route('partner.articles.edit', {
                  article: article.slug,
                })
                  ">
                  {{ __("Edit") }}
                </DropdownItem>
              </Dropdown>
            </div>
          </div>
          <div class="flex my-2">
            <x-button as="Link" intent="link" :href="route('company.show', { slug: article.company.slug })">
              {{ ownerName }}
            </x-button>
          </div>
          <div class="flex">
            <p class="text-muted text-sm">{{ article.created_at }}</p>
            <p class="text-muted ml-auto text-sm">
              {{ __("Views") }}: {{ article.views }}
            </p>
          </div>
        </div>
      </div>
      <!-- Article Image -->
      <div v-if="article.media && article.media.length" class="mb-8">
        <x-images :media="article.media" />
      </div>
      <!-- Article Content -->
      <div class="prose prose-stone dark:prose-invert">
        <article v-html="article.content"></article>
      </div>
      <div class="mt-8 flex justify-between">
        <x-button as="Link" :href="route('news')">
          {{ __("Back") }}
        </x-button>
      </div>
    </div>
  </div>
</template>
