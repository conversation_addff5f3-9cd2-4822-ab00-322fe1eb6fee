<script setup>
import { Head } from "@inertiajs/vue3";
// import Recommendations from "../../../Seller/Widgets/Recommendations.vue";
import { onMounted, onUnmounted, inject } from "vue";
import SearchFeed from "../../Widgets/Search/SearchFeed.vue";
import { useHeaderStore } from "@m/Fixed";
import CouponCard from "../../Features/Coupon/CouponCard.vue";
import { UserCard } from "@s";
import LocationCard from "../../Features/Location/LocationCard.vue";
import CompanyCard from "../../Features/Company/CompanyCard.vue";
import BottomText from "../../../../Shared/Features/BottomText.vue";

const route = inject("route");

const header = useHeaderStore();

defineProps({
  coupons: Object,
  users: Object,
  locations: Object,
  companies: Object,
})

onMounted(() => {
  header.title = 'Advanced search';
  header.navigation = 'advanced_search';
  header.backUrl = route('feed');

});

onUnmounted(() => {
  header.clear();
});

</script>

<template>

  <Head :title="header.title" />
  <SearchFeed />
  <template v-if="coupons.length || users.length || locations.length || companies.length">
    <div>
      <h1 class="h1 mx-4 my-2">{{ __("Coupons") }}</h1>
      <CouponCard v-for="coupon in coupons" :coupon="coupon" />
      <BottomText v-if="!coupons.length" :message="__('No coupons')" />
    </div>
    <div>
      <h1 class="h1 mx-4 my-2">{{ __("Users") }}</h1>
      <UserCard v-for="user in users" :user="user" />
      <BottomText v-if="!users.length" :message="__('No users')" />
    </div>
    <div>
      <h1 class="h1 mx-4 my-2">{{ __("Locations") }}</h1>
      <LocationCard v-for="location in locations" :location="location" />
      <BottomText v-if="!locations.length" :message="__('No locations')" />
    </div>
    <div>
      <h1 class="h1 mx-4 my-2">{{ __("Companies") }}</h1>
      <CompanyCard v-for="company in companies" :company="company" />
      <BottomText v-if="!companies.length" :message="__('No companies')" />
    </div>

  </template>
  <template v-else>
    <BottomText :message="__('Nothing')" />
  </template>
</template>
