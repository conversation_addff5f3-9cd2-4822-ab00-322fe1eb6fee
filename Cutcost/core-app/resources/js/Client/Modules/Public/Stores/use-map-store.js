// Stores/use-map-store.js
import { defineStore } from "pinia";
import L from "leaflet";
import axios from "axios";
import { ref, toRaw } from "vue";
import { useTranslation } from "@s";
import "leaflet.markercluster";
import "leaflet.markercluster/dist/MarkerCluster.css";
import "leaflet.markercluster/dist/MarkerCluster.Default.css";
import { icon } from "../Composables/Map/LocationIcon";

// Глобально отключаем анимацию зума маркеров и тултипов
L.Marker.prototype.options.zoomAnimation = false;
L.Tooltip.prototype.options.fade = false; // отключаем анимацию тултипов

const _origUpdatePosition = L.Tooltip.prototype._updatePosition;
L.Tooltip.prototype._updatePosition = function () {
  if (!this._map) return;
  _origUpdatePosition.call(this, ...arguments);
};

export const useMapStore = defineStore("map", () => {
  const { __ } = useTranslation;
  const map = ref(null);
  const coordinates = ref(null);
  const searchAnswer = ref("");
  const currentTileLayer = ref(null);
  const startMarker = ref(null);
  const clickMarker = ref(null);
  const address = ref("");

  const isDark = ref(localStorage.getItem("theme") === "dark");

  function initMap(targetId = "map", center, zoom, readOnly = false) {
    const initial =
      center && center.lat != null && center.lng != null
        ? center
        : { lat: 46.9631, lng: 8.9402 };

    map.value = L.map(targetId, {
      center: [initial.lat, initial.lng],
      zoom,
      doubleClickZoom: readOnly,
      zoomAnimation: false,
      markerZoomAnimation: false,
    });

    switchTileLayer(isDark.value);
    L.control.scale().addTo(toRaw(map.value));

    if (!readOnly) {
      map.value.on("click", handleMapClick);
    }
  }

  function toggleTheme() {
    isDark.value = !isDark.value;
    localStorage.setItem("theme", isDark.value ? "dark" : "light");
    switchTileLayer(isDark.value);
  }

  function setView(lat, lng, zoom = 13) {
    if (!map.value) return;
    map.value.setView([lat, lng], zoom);
  }

  async function searchLocation(query) {
    try {
      const { data } = await axios.get(
        "https://nominatim.openstreetmap.org/search",
        { params: { q: query, format: "json" } },
      );
      if (!data.length || data[0].display_name.includes("undefined")) {
        searchAnswer.value = __("Nothing found");
        return;
      }
      const place = data[0];
      const levels = { country: 7, city: 11, town: 11, village: 15 };
      setView(place.lat, place.lon, levels[place.addresstype] || 20);
      searchAnswer.value = place.display_name;
    } catch (err) {
      console.error("searchLocation error", err);
      searchAnswer.value = __("Error occurred");
    }
  }

  function placeStartMarker(position, customIcon = null) {
    if (!map.value) return;
    removeLayer(startMarker.value);
    if (position?.lat != null && position?.lng != null) {
      startMarker.value = L.marker([position.lat, position.lng], {
        icon: customIcon ? customIcon : icon,
        interactive: false,
        zoomAnimation: false,
      }).addTo(toRaw(map.value));
    }
  }

  function handleMapClick(e) {
    removeLayer(clickMarker.value);
    removeLayer(startMarker.value);

    coordinates.value = e.latlng;

    clickMarker.value = L.marker(e.latlng, {
      icon,
      draggable: true,
      zoomAnimation: false,
    }).addTo(toRaw(map.value));

    // При клике сразу получаем адрес
    updateAddress(e.latlng.lat, e.latlng.lng);

    // При перемещении маркера обновляем координаты и адрес
    clickMarker.value.on("moveend", (ev) => {
      const pos = ev.target.getLatLng();
      coordinates.value = pos;
      updateAddress(pos.lat, pos.lng);
    });
  }

  async function updateAddress(lat, lng) {
    address.value = __("Loading");
    try {
      const { data } = await axios.get(
        "https://nominatim.openstreetmap.org/reverse",
        {
          params: {
            lat,
            lon: lng,
            format: "json",
            addressdetails: 0,
            zoom: 18,
          },
        },
      );
      address.value = data.display_name || __("Address not found");
    } catch (err) {
      console.error("getAddressFromCoords error", err);
      address.value = __("Error occurred");
    }
  }

  function removeLayer(layer) {
    if (layer && map.value?.hasLayer(layer)) {
      map.value.removeLayer(layer);
    }
  }

  return {
    map,
    coordinates,
    searchAnswer,
    isDark,
    initMap,
    switchTileLayer,
    toggleTheme,
    setView,
    searchLocation,
    address,
    placeStartMarker,
  };
});
