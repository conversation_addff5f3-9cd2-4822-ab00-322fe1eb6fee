<script setup>
import { EndlessPagination, useHelpers } from "@s";
import { computed, reactive, useTemplateRef, watch } from "vue";
import NavigationButton from "../../../Fixed/Entities/NavigationButton.vue";

const { debounce, countryCodeToEmoji } = useHelpers();

const emits = defineEmits(['apply']);

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    cityId: [Number, String, null],
    countries: {
        type: Array,
        default: null,
    },
    country_id: Number,
    countryRequired: Boolean,
});

const pagination = useTemplateRef('pagination');

// Вычисляем код страны по переданному country_id
const countryCodeById = computed(() => {
    return props.countries?.find(c => c.id === props.country_id)?.code ?? null;
});

// Реактивные фильтры
const filters = reactive({
    search_query: null,
    country_code: countryCodeById.value,
});

watch(
    () => [filters.country_code, filters.search_query],
    () => reload(),
    { deep: true }
);

const reload = debounce(() => {
    if (pagination.value) {
        pagination.value.replace();
    }
}, 400);


const combinedFilters = computed(() => ({
    ...filters,
    ...(props.data?.filters ?? {}),
    city_id: filters.search_query ? null : props.cityId ?? null,
}));


</script>

<template>
    <div class="space-y-2">
        <x-select v-model="filters.country_code" :placeholder="__('Country')" :data="countries" 
             search v-if="countries" />

        <x-input v-model="filters.search_query" v-if="(countryRequired && filters.country_code) || !countryRequired"
            :placeholder="__('Search city')" />

        <EndlessPagination :noBottomText="cityId && !filters.search_query" v-if="filters.country_code || filters.search_query || cityId"
            ref="pagination" :url="route('load.paginated')"
            :params="{ type: 'city_search', filters: combinedFilters, ...data }">
            <template #default="{ item }">
                <NavigationButton @click="emits('apply', item)">
                    {{ countryCodeToEmoji(item.country_code) }} {{ item.name }}
                </NavigationButton>
            </template>
        </EndlessPagination>
    </div>
</template>
