<script setup>
import { EndlessPagination } from "@s";
import CompanyCard from "../../Features/Company/CompanyCard.vue";

const props = defineProps({
  filters: Object,
  type: String,
  data: {
    type: Object,
    default: () => { },
  },
});


</script>

<template>
  <EndlessPagination :url="route('load.paginated')" :params="{ type: type, ...data }">
    <template #default="{ item }">
      <CompanyCard :company="item" />
    </template>
  </EndlessPagination>
</template>
