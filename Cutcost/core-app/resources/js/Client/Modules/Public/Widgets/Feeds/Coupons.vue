<script setup>
import { nextTick, onMounted, onUnmounted, reactive, ref, useTemplateRef } from "vue";
import { EndlessPagination, useCache } from "@s";
import CouponCard from "../../Features/Coupon/CouponCard.vue";
import CouponsFilters from "../Filters/CouponsFilters.vue";
import { useHeaderStore } from "@m/Fixed";

const props = defineProps({
  categories: Object,
  countries: Object,
  type: String,
  small: Boolean,
  data: {
    type: Object,
    default: () => { },
  },
});

const header = useHeaderStore();

// 
const pagination = useTemplateRef('pagination');

const filtersObj = ref(JSON.parse(localStorage.getItem('coupons_filters_data')) ?? {});

const onApply = (payload) => {
  console.log(payload);
  filtersObj.value = payload;

  nextTick(() => {
    pagination.value.replace();
  });
};

const { flush } = useCache();

const deleteIds = reactive([]);

const handleDelete = (event) => {
  deleteIds.push(event.detail?.id);
  flush(); // cache
};

onMounted(() => {
  document.addEventListener('coupon-deleted', handleDelete);
});

onUnmounted(() => {
  document.removeEventListener('coupon-deleted', handleDelete);
});

</script>

<template>
  <EndlessPagination ref="pagination" class="grid gap-2"
    :class="small ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'"
    :url="route('load.paginated')" :params="{ type: type, filters: filtersObj, ...data }">
    <template #filters v-if="(categories || countries) && header.chosenTab === 'coupons'">
      <CouponsFilters :categories :countries @apply="onApply" />
    </template>
    <template #default="{ item }">
      <CouponCard :coupon="item" v-if="!deleteIds.includes(item.id)" />
    </template>
  </EndlessPagination>
</template>
