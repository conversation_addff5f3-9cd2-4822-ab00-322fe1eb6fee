<script setup>
import { EndlessPagination, useCache } from "@s";
import LocationCard from "../../Features/Location/LocationCard.vue";
import { onMounted, onUnmounted, reactive } from "vue";

const props = defineProps({
  filters: Object,
  type: String,
  data: {
    type: Object,
    default: () => null,
  },
});

const { flush } = useCache();

const deleteIds = reactive([]);

const handleDelete = (event) => {
  deleteIds.push(event.detail?.id);
  flush(); // cache
};

onMounted(() => {
  document.addEventListener('location-deleted', handleDelete);
});

onUnmounted(() => {
  document.removeEventListener('location-deleted', handleDelete);
});

</script>

<template>
  <EndlessPagination :url="route('load.paginated')" class="space-y-1" :params="{ type: type, ...data }">
    <template #default="{ item }">
      <LocationCard :location="item" v-if="!deleteIds.includes(item.id)"/>
    </template>
  </EndlessPagination>
</template>
