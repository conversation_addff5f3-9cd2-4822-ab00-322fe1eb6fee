<script setup>
import { EndlessPagination, useCache } from "@s";
import { Post } from "@m/Seller";
import { onMounted, onUnmounted, reactive } from "vue";



const props = defineProps({
    type: String,
    data: {
        type: Object,
        default: () => { },
    },
});

const { flush } = useCache();

const deleteIds = reactive([]);

const handleDelete = (event) => {
    deleteIds.push(event.detail?.id);
    flush(); // cache
};

onMounted(() => {
    document.addEventListener('post-deleted', handleDelete);
});

onUnmounted(() => {
    document.removeEventListener('post-deleted', handleDelete);
});

</script>

<template>
    <EndlessPagination :url="route('load.paginated')" :params="{ type: type, ...data }" class="space-y-1">
        <template #default="{ item }">
            <Post v-if="!deleteIds.includes(item.id)" :post="item" :show="false" />
        </template>
    </EndlessPagination>
</template>