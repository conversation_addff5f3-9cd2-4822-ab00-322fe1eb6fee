<script setup>
import CouponsCategories from "../../Features/Coupon/Filters/CouponsCategories.vue";
import { Geolocate } from "@s";
import { computed, onMounted, reactive, ref, Teleport, useTemplateRef } from "vue";
import Cities from "../Feeds/Cities.vue";
import { PhX } from "@phosphor-icons/vue";
import NavigationButton from "../../../Fixed/Entities/NavigationButton.vue";

const emit = defineEmits(['apply']);

const props = defineProps({
    countries: Object,
    categories: Object,
});

const cityModal = useTemplateRef('cityModal');

const savedData = reactive({
    city: {},
    category: {},
    lng: null,
    lat: null,
});

const filters = reactive({
    category: null,
    country: null,
    city: null,
    near: null,
});

const countriesWithoutWW = computed(() => {
    return props.countries.filter(c => c.code !== 'ss');
});

const targetReady = ref(false);

onMounted(() => {
    const saved = localStorage.getItem('coupons_filters_saved');
    const data = localStorage.getItem('coupons_filters_data');

    if (saved) {
        Object.assign(savedData, JSON.parse(saved));
    }

    if (data) {
        Object.assign(filters, JSON.parse(data));
    }

    if (document.querySelector('#upperbar')) {
        targetReady.value = true
    }
});

const setCity = (city) => {
    filters.city = city?.id ?? null;
    savedData.city = city;
    cityModal.value.modalOpen = false;
    remember();
};

const setNear = (lnglat) => {
    if (!lnglat) {
        filters.near = null;
        savedData.lng = null;
        savedData.lat = null;
        remember();
        return;
    }
    filters.near = [lnglat.lng, lnglat.lat];
    savedData.lng = lnglat.lng;
    savedData.lat = lnglat.lat;
    remember();
};

const setCategory = (category) => {
    filters.category = category?.id ?? null;
    savedData.category = category ?? null;
    remember();
};

const remember = () => {
    localStorage.setItem('coupons_filters_saved', JSON.stringify(savedData));
    localStorage.setItem('coupons_filters_data', JSON.stringify(filters));
    emit('apply', filters);
}

</script>

<template>
    <Teleport to="#upperbar" v-if="targetReady">
        <div class="flex flex-nowrap items-center overflow-x-auto">
            <div class="shrink-0">
                <CouponsCategories :categories :category="savedData.category" @apply="setCategory" />
            </div>
            <div v-if="countriesWithoutWW?.length" class="shrink-0">
                <NavigationButton intent="secondary"  @click="cityModal.modalOpen = true">
                    {{ savedData.city?.name ?? __('City') }}
                    <PhX :size="20" class="fill-error rounded-full" @click.stop="setCity(null)"
                        v-if="savedData.city?.name" />
                </NavigationButton>
                <Modal ref="cityModal" :title="__('City & Country')">
                    <Cities :countries @apply="setCity" :cityId="filters.city" />
                </Modal>
            </div>
            <div class="shrink-0">
                <Geolocate class="!p-2" @geolocate="setNear" :active="!!savedData.lng">
                    {{ __('Near me') }}
                    <PhX :size="20" class="fill-error rounded-full" @click.stop="setNear(null)" v-if="savedData.lng" />
                </Geolocate>
            </div>
        </div>
    </Teleport>
</template>
