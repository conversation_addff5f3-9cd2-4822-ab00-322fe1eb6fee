<script setup>
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PhShareNetwork, PhQrCode, PhInstagramLogo, PhYoutubeLogo, PhTiktokLogo } from "@phosphor-icons/vue";
</script>

<template>
    <!-- Hero Section -->
    <section v-motion-slide-visible-top
        class="sm:min-h-screen flex flex-col rounded-t-xl items-center justify-center px-6 max-sm:py-16 bg-white text-gray-900 dark:bg-gray-900 dark:text-white">
        <h1 class="text-4xl sm:text-5xl font-light mb-4 text-center">
            {{ __('about_us.hero.heading') }}
        </h1>
        <h2 class="text-2xl sm:text-3xl font-semibold mb-2 text-secondary dark:text-blue-400 text-center">
            {{ __('about_us.hero.highlight') }}
        </h2>
        <p class="text-base sm:text-lg max-w-lg text-center text-gray-600 dark:text-gray-400">
            {{ __('about_us.hero.subheading') }}
        </p>
    </section>

    <!-- Features Section -->
    <section v-motion-slide-visible-left
        class="py-12 px-4 sm:px-8 bg-gray-50 text-gray-900 dark:bg-gray-800 dark:text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h3 class="text-2xl sm:text-3xl font-medium mb-8">
                {{ __('about_us.features.heading') }}
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                <!-- Feature 1 -->
                <div
                    class="flex flex-col items-center p-6 sm:p-8 bg-white rounded-2xl shadow backdrop-blur dark:bg-gray-700">
                    <PhSealPercent size="36" class="text-secondary mb-3 dark:text-blue-400" />
                    <h4 class="text-lg sm:text-xl font-semibold mb-1">
                        {{ __('about_us.features.generator.title') }}
                    </h4>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.features.generator.text') }}
                    </p>
                </div>
                <!-- Feature 2 -->
                <div
                    class="flex flex-col items-center p-6 sm:p-8 bg-white rounded-2xl shadow backdrop-blur dark:bg-gray-700">
                    <PhGift size="36" class="text-secondary mb-3 dark:text-blue-400" />
                    <h4 class="text-lg sm:text-xl font-semibold mb-1">
                        {{ __('about_us.features.messenger.title') }}
                    </h4>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.features.messenger.text') }}
                    </p>
                </div>
                <!-- Feature 3 -->
                <div
                    class="flex flex-col items-center p-6 sm:p-8 bg-white rounded-2xl shadow backdrop-blur dark:bg-gray-700">
                    <PhShareNetwork size="36" class="text-secondary mb-3 dark:text-blue-400" />
                    <h4 class="text-lg sm:text-xl font-semibold mb-1">
                        {{ __('about_us.features.analytics.title') }}
                    </h4>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.features.analytics.text') }}
                    </p>
                </div>
                <!-- Feature 4 -->
                <div
                    class="flex flex-col items-center p-6 sm:p-8 bg-white rounded-2xl shadow backdrop-blur dark:bg-gray-700">
                    <PhQrCode size="36" class="text-secondary mb-3 dark:text-blue-400" />
                    <h4 class="text-lg sm:text-xl font-semibold mb-1">
                        {{ __('about_us.features.gamification.title') }}
                    </h4>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.features.gamification.text') }}
                    </p>
                </div>
                <!-- Feature 5 -->
                <div
                    class="flex flex-col items-center p-6 sm:p-8 bg-white rounded-2xl shadow backdrop-blur dark:bg-gray-700">
                    <PhShareNetwork size="36" class="text-secondary mb-3 dark:text-blue-400" />
                    <h4 class="text-lg sm:text-xl font-semibold mb-1">
                        {{ __('about_us.features.shorts.title') }}
                    </h4>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.features.shorts.text') }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How it works Section -->
    <section v-motion-slide-visible-bottom
        class="py-12 px-4 sm:px-8 bg-white text-gray-900 dark:bg-gray-900 dark:text-white">
        <div class="max-w-3xl mx-auto">
            <h3 class="text-2xl sm:text-3xl font-medium mb-6 text-center">
                {{ __('about_us.how_it_works.title') }}
            </h3>
            <div class="space-y-6">
                <!-- Step 1 -->
                <div class="flex items-start">
                    <div class="p-3 bg-gray-100 rounded-full backdrop-blur dark:bg-gray-700">
                        <PhSealPercent size="28" class="text-secondary dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <h5 class="text-lg font-semibold mb-1">
                            {{ __('about_us.how_it_works.steps.step_1.title') }}
                        </h5>
                        <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base">
                            {{ __('about_us.how_it_works.steps.step_1.text') }}
                        </p>
                    </div>
                </div>
                <!-- Step 2 -->
                <div class="flex items-start">
                    <div class="p-3 bg-gray-100 rounded-full backdrop-blur dark:bg-gray-700">
                        <PhGift size="28" class="text-secondary dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <h5 class="text-lg font-semibold mb-1">
                            {{ __('about_us.how_it_works.steps.step_2.title') }}
                        </h5>
                        <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base">
                            {{ __('about_us.how_it_works.steps.step_2.text') }}
                        </p>
                    </div>
                </div>
                <!-- Step 3 -->
                <div class="flex items-start">
                    <div class="p-3 bg-gray-100 rounded-full backdrop-blur dark:bg-gray-700">
                        <PhShareNetwork size="28" class="text-secondary dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <h5 class="text-lg font-semibold mb-1">
                            {{ __('about_us.how_it_works.steps.step_3.title') }}
                        </h5>
                        <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base">
                            {{ __('about_us.how_it_works.steps.step_3.text') }}
                        </p>
                    </div>
                </div>
                <!-- Step 4 -->
                <div class="flex items-start">
                    <div class="p-3 bg-gray-100 rounded-full backdrop-blur dark:bg-gray-700">
                        <PhQrCode size="28" class="text-secondary dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <h5 class="text-lg font-semibold mb-1">
                            {{ __('about_us.how_it_works.steps.step_4.title') }}
                        </h5>
                        <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base">
                            {{ __('about_us.how_it_works.steps.step_4.text') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Referral Section -->
    <section v-motion-slide-visible-bottom
        class="py-12 px-4 sm:px-8 bg-gray-50 text-gray-900 dark:bg-gray-800 dark:text-white">
        <div class="max-w-3xl mx-auto text-center">
            <h3 class="text-2xl sm:text-3xl font-medium mb-4">
                {{ __('about_us.referral.title') }}
            </h3>
            <p class="text-sm sm:text-base mb-6 text-gray-700 dark:text-gray-300">
                {{ __('about_us.referral.description') }}
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                <!-- Unique -->
                <div class="p-5 bg-white rounded-lg shadow backdrop-blur dark:bg-gray-700">
                    <h5 class="text-base font-semibold mb-1">
                        {{ __('about_us.referral.blocks.unique.title') }}
                    </h5>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.referral.blocks.unique.text') }}
                    </p>
                </div>
                <!-- Free -->
                <div class="p-5 bg-white rounded-lg shadow backdrop-blur dark:bg-gray-700">
                    <h5 class="text-base font-semibold mb-1">
                        {{ __('about_us.referral.blocks.free.title') }}
                    </h5>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.referral.blocks.free.text') }}
                    </p>
                </div>
                <!-- Discount -->
                <div class="p-5 bg-white rounded-lg shadow backdrop-blur dark:bg-gray-700">
                    <h5 class="text-base font-semibold mb-1">
                        {{ __('about_us.referral.blocks.discount.title') }}
                    </h5>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">
                        {{ __('about_us.referral.blocks.discount.text') }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section v-motion-pop-visible
        class="py-12 relative   bg-cover bg-center  px-4 sm:px-8 bg-white dark:bg-gray-900 dark:text-dark-text">
        <div class="max-w-2xl mx-auto text-center relative">
            <h3 class="text-2xl sm:text-3xl font-medium mb-3">
                {{ __('about_us.title') }}
            </h3>
            <p class="mb-4  text-sm sm:text-base">
                {{ __('about_us.paragraph_1') }}
            </p>
            <p class="mb-6  text-sm sm:text-base">
                {{ __('about_us.paragraph_2') }}
            </p>
            <div class="flex gap-2 max-sm:flex-col mb-8">
                <a class="flex items-center gap-1" href="https://www.instagram.com/cutcost_show/" target="_blank">
                    <PhInstagramLogo :size="20" /> @cutcost_show
                </a>
                <a class="flex items-center gap-1" href="https://www.youtube.com/@cutcostnet" target="_blank">
                    <PhYoutubeLogo :size="20" /> @cutcostnet
                </a>
                <a class="flex items-center gap-1" href="https://www.tiktok.com/@cutcost" target="_blank">
                    <PhTiktokLogo :size="20" /> @cutcost
                </a>
            </div>
            <!-- <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ __('about_us.referral.footer') }}
            </p> -->
        </div>
    </section>
</template>