<script setup>
import { PhEye } from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";
import { computed } from "vue";
import { inject } from "vue";

const route = inject("route");

const global = useGlobalStore();

const props = defineProps({
    article: {
        type: Object,
        required: true,
    },
});

const articleImage = computed(() => {
    return props.article.images && props.article.images.length > 0
        ? global.assets + props.article.images[0]
        : global.appUrl + "assets/images/default_article.jpg";
});
</script>

<template>
    <div
        class="flex flex-col border-secondary overflow-hidden rounded-xl border-2 bg-bg dark:bg-dark-surface dark:border-0 shadow-lg transition-transform duration-300 hover:-translate-y-1 hover:shadow-xl">

        <!-- Image & Badge -->
        <div class="relative">
            <x-gallery :media="article.media" />
            <div class="absolute top-3 right-0 bg-secondary text-primary-hover px-4 py-1 text-sm font-bold uppercase"
                style="clip-path: polygon(8% 0%, 100% 0%, 100% 100%, 0% 100%)">
                Cutcost news
            </div>
        </div>

        <!-- Content -->
        <div class="flex flex-col flex-1 p-4 justify-between">
            <!-- Title -->
            <Link :href="route('news.show', { article: article.slug })" class="text-secondary h3 hover:underline">
            {{ article.name }}
            </Link>

            <!-- Footer (Views + Button) -->
            <div class="flex items-center justify-between mt-4">
                <div class="flex items-center text-text dark:text-dark-text">
                    <PhEye :size="20" weight="bold" />
                    <span class="ml-1">{{ article.views }}</span>
                </div>
                <x-button as="Link" :href="route('news.show', { article: article.slug })">
                    {{ __('Read') }}
                </x-button>
            </div>
        </div>
    </div>

</template>
