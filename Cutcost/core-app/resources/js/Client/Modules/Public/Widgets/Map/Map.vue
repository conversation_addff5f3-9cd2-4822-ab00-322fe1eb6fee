<script setup>
import L from "leaflet";
import "leaflet.markercluster";
import { onMounted, inject, onUnmounted } from "vue";
import { router } from "@inertiajs/vue3";
import MapControls from "../../Features/Map/MapControls.vue";
import entityIcon from "../../Composables/Map/entity-icon";
import positionIcon from "../../Composables/Map/position-icon";

const route = inject("route");

const props = defineProps({
  locations: Object,
})

let map = null;
let markersClusterGroup = null;
let currentPositionMarker = null;
let searchMarker = null;

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const lat = parseFloat(urlParams.get("lat"));
  const lng = parseFloat(urlParams.get("lng"));
  const zoom = parseInt(urlParams.get("zoom")) || 6;

  const defaultCenter = lat && lng
    ? [lat, lng]
    : localStorage.getItem('map.center')
      ? JSON.parse(localStorage.getItem('map.center'))
      : [46.9631, 8.9402];


  map = L.map("map", {
    center: defaultCenter,
    zoom: zoom,
    zoomControl: false
  });

  L.control.zoom({
    position: 'bottomright'
  }).addTo(map);

  L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
    maxZoom: 19,
    attribution: "&copy; OpenStreetMap contributors",
    className: "map-tiles",
  }).addTo(map);

  const attributionControl = document.querySelector('.leaflet-control-attribution');
  if (attributionControl) {
    attributionControl.innerHTML = '&copy; <a href="https://www.openstreetmap.org/copyright">OSM</a> contributors';
  }

  // Инициализируем кластер-группу
  markersClusterGroup = L.markerClusterGroup();
  map.addLayer(markersClusterGroup);

  map.on("moveend", () => {
    const center = map.getCenter();
    const zoom = map.getZoom();

    const url = new URL(window.location.href);
    url.searchParams.set("lat", center.lat.toFixed(5));
    url.searchParams.set("lng", center.lng.toFixed(5));
    url.searchParams.set("zoom", zoom);

    window.history.replaceState({}, "", url);
  });

  addMarkers(props.locations);
});

const addMarkers = (locations) => {
  locations.forEach((location) => {
    if (!location.lnglat) return;
    const [lng, lat] = [location.lnglat.lng, location.lnglat.lat];
    const imageUrl = location.media?.[0]?.url || "404";

    const marker = L.marker([lat, lng], {
      icon: entityIcon(imageUrl, location.name)
    });

    marker.addEventListener("click", () => {

      router.get(route("location.show", { slug: location.slug }));
    });

    markersClusterGroup.addLayer(marker);
  });
};

const createMarker = (lnglat, color) => {

  const marker = L.marker([lnglat.lat, lnglat.lng], {
    icon: positionIcon(color)
  });

  marker.addTo(map);
  map.panTo(lnglat);

  return marker;

};
const handleGeolocate = (lnglat) => {

  if (currentPositionMarker) {
    currentPositionMarker.remove();
  }

  currentPositionMarker = createMarker(lnglat, 'var(--color-error)');
}

const handleSearch = (lnglat) => {
  if (searchMarker) {
    searchMarker.remove();
  }

  searchMarker = createMarker(lnglat, 'var(--color-secondary)');
}

const handleResize = () => {

  setTimeout(() => {
    map.invalidateSize();
  }, 50);
}

handleResize();

</script>

<template>
  <div class="relative" >
    <MapControls @lnglat="handleSearch" @geolocate="handleGeolocate" />
    <div v-bind="$attrs" id="map" class="sm:rounded-xl h-[600px]"></div>
  </div>
</template>
