<script setup>
import { ref, inject, onMounted } from "vue";
import { PhMagnifyingGlass } from '@phosphor-icons/vue';
import { useHelpers } from "@s";
import { router } from "@inertiajs/vue3";

const route = inject("route");

const { debounce } = useHelpers();

const search = ref('');

const click = debounce(() => {
    if (!search.value.trim()) return;

    router.get(route('advanced.search'), { search: search.value });
}, 500);

onMounted(() => {
    search.value = new URLSearchParams(window.location.search).get('search') ?? '';
});

</script>

<template>
    <div class="flex px-2 gap-2 items-center">
        <x-input v-model="search" @keyup.enter="click" type="search" :placeholder="__('Search')" />
        <x-button @click="click">
            <PhMagnifyingGlass :size="25" />
        </x-button>
    </div>
</template>