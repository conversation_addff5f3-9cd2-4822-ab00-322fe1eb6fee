<script setup>
import { AvatarAndCover, Content, Translate } from "@s";
import { CompanyControls } from "@m/Seller";
import { inject } from "vue";
import ShowIndustries from "../../../Partner/Entities/Company/ShowIndustries.vue";
import CompanyMoreInfo from "../../Features/Company/CompanyMoreInfo.vue";
import Rating from "../../Features/Rating.vue";

const route = inject("route");

const props = defineProps({
  company: Object,
  subscribersCount: Number,
  noMessage: {
    type: Boolean,
    default: false,
  },
  isOwner: {
    type: Boolean,
    default: false,
  },
});

</script>
<template>
  <AvatarAndCover :urls="[company?.avatar, company?.cover]" :editable="isOwner"
    :data="{ model_type: 'company', model_id: company.id }">
    <x-button v-if="!noMessage && !isOwner" as="Link" intent="rounded" :href="route('messenger.chat', {
      key: company.slug,
      type: 'slug',
    })
      ">{{ __("Message") }}</x-button>
    <CompanyControls :company="company" big />
  </AvatarAndCover>
  <div class="mx-2 mt-2">
    <div class="flex flex-col w-full text">
      <h1 class="h1 text-2xl font-bold">
        {{ company.name }}
      </h1>
      <Content :content="company.description" />
      <Translate field="description" :model="company" />
      <ShowIndustries :industries="company.industries" />
    </div>
    <div class="flex mt-2 gap-2">
      <CompanyMoreInfo :company="company" class="w-full   " />
      <Rating modelName="Company" :model="company" />
    </div>
  </div>
</template>
