import { inject } from "vue";

export const useCommentApi = () => {
  const route = inject("route");

  const fetchComments = async (className, id, cursor = null) => {
  
    const response = await axios.get(
      route("seller.comment.index", { class: className, id }),
      { params: { cursor } },
    );

    return response.data;
  };

  const createComment = async (className, id, payload) => {
    const response = await axios.post(
      route("seller.comment.store", { class: className, id: id }),
      payload,
    );
    return response.data;
  };

  const updateComment = async (commentId, payload) => {
    const response = await axios.put(
      route("seller.comment.update", { comment: commentId }),
      payload,
    );
    return response.data;
  };

  const deleteComment = async (commentId) => {
    const response = await axios.delete(
      route("seller.comment.destroy", { comment: commentId }),
    );
    return response.data;
  };

  return {
    fetchComments,
    createComment,
    updateComment,
    deleteComment,
  };
};
