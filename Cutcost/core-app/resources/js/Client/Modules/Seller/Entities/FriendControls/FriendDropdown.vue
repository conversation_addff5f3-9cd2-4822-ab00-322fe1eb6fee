<script setup>
import {
  PhChatCircleDots,
  PhDotsThreeVertical,
  PhUserMinus,
} from "@phosphor-icons/vue";
import { useGlobalStore } from "@s";
import { inject } from "vue";

defineEmits(["unfriend"]);

const props = defineProps({
  user: Object,
  mini: Boolean,
  isFriend: <PERSON>olean,
});

const global = useGlobalStore();
const route = inject("route");
</script>

<template>
  <Dropdown position="bottom" v-bind="$attrs">
    <template #button>
      <x-button>
        <PhDotsThreeVertical :size="global.isMobile ? 24 : 28" weight="bold" />
      </x-button>
    </template>

    <!-- Chat -->
    <DropdownItem as="Link" :href="route('messenger.chat', { key: user.nickname, type: 'nickname' })">
      <PhChatCircleDots :size="global.isMobile ? 20 : 28" />
      {{ __("Message") }}
    </DropdownItem>

    <!-- Unfriend -->
    <DropdownItem v-if="isFriend" @click="$emit('unfriend')">
      <PhUserMinus :size="global.isMobile ? 20 : 28" />
      {{ __("Unfriend") }}
    </DropdownItem>
  </Dropdown>
</template>
