<script setup>
import { Bookmarkable, Rating } from "@m/Public";
import { PhChatsTeardrop, PhEye, PhShareFat } from "@phosphor-icons/vue";
import { Share } from "@s";
import { router } from "@inertiajs/vue3";
import { inject } from "vue";
import { useProfileStore } from "../../Stores/use-profile-store";

const profile = useProfileStore();
const route = inject("route");

const props = defineProps({
    post: Object,
    show: <PERSON>ole<PERSON>,
    repost: <PERSON>ole<PERSON>,
});

defineEmits(["lookFullPost"]);

const goRepost = () => {
    profile.repostSlug = props?.post?.slug;
    router.get(route('seller.post.create'));
};

const goto = () => {

    if (props.show) return;

    router.get(route('show.post', { post: post.slug }));

}

</script>
<template>
    <div class="flex justify-between">
        <div class="flex">
            <Rating modelName="Post" :model="post" />
        </div>
        <div class="flex cursor-pointer items-center gap-1">
            <div>
                <x-button intent="rounded" @click="goto">
                    <template #icon>
                        <PhChatsTeardrop :size="20" />
                    </template>
                    {{ post.comments_count }}
                </x-button>
            </div>
            <x-button intent="rounded">
                <template #icon>
                    <PhEye :size="20" />
                </template>
                {{ post.views }}
            </x-button>
            <Bookmarkable :model="post" modelName="Post" :modelId="post?.id" />
            <Dropdown v-if="!repost">
                <template #button>
                    <x-button intent="rounded">
                        <PhShareFat :size="20" />
                    </x-button>
                </template>
                <Share :url="route('show.post', { post: post.slug })" dropdown>
                    {{ __("Share") }}
                </Share>
                <DropdownItem @click="goRepost">
                    {{ __("Repost") }}
                </DropdownItem>
            </Dropdown>
        </div>
    </div>
</template>
