<script setup>
import { router } from "@inertiajs/vue3";
import { PhDotsThreeVertical } from "@phosphor-icons/vue";
import { inject } from "vue";
import { Report, DeleteModel, useHelpers } from "@s";

const { diffForHumans } = useHelpers();

const route = inject("route");

const props = defineProps({
  post: Object,
  isOwner: Boolean,
});

const edit = () => {
  router.get(route("seller.post.edit", { post: props.post.slug }));
};


const visitProfile = () => {
  router.get(
    route("profile", {
      user: props.post?.creator?.nickname,
    }),
  );
};

</script>
<template>
  <div class=" flex w-full items-center justify-between">
    <div @click="visitProfile" class="flex hover:bg-surface  rounded-2xl pr-2 dark:hover:bg-bg-dark/40 !cursor-pointer">
      <x-model-media class="mr-2 !h-12 !w-12 " :url="post?.creator?.avatar" />
      <div class="flex  flex-col leading-4 mt-2">
        <div class="text-accent dark:text-dark-accent font-bold">
          {{ post.creator?.display_name }}
        </div>
        <div class="text-muted dark:text-dark-muted text-sm">
          @{{ post.creator?.nickname }}
        </div>
      </div>
    </div>
    <div class="flex gap-3 items-center">

      <Dropdown position="bottom-right">
        <template #button>
          <x-button intent="rounded">
            <PhDotsThreeVertical :size="20" />
          </x-button>
        </template>
        <template v-if="isOwner">
          <DropdownItem @click="edit">
            {{ __("Edit") }}
          </DropdownItem>
          <DeleteModel :model="post" modelName="Post"  dropdown :redirect="$page.component === 'Seller/Profile/ShowPost'" :url="route('seller.post.destroy', { post: post.slug })"/>
        </template>
        <Report model-type="post" :model-id="post.id" dropdown>
          {{ __("Report") }}
        </Report>
        <DropdownItem>
          {{ diffForHumans(post.created_at) }}
        </DropdownItem>
      </Dropdown>
    </div>
  </div>
</template>
