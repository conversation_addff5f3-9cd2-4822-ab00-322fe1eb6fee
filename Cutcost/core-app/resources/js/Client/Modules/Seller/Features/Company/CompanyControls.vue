<script setup>
import { router } from "@inertiajs/vue3";
import { computed } from "vue";
import { inject } from "vue";
import { useGlobalStore, useHelpers } from "@s";
import CompanyManagment from "../../../Partner/Features/Company/CompanyManagment.vue";
import { PhGearSix, PhPencil } from "@phosphor-icons/vue";

const route = inject("route");
const { debounce } = useHelpers();
const global = useGlobalStore();

const props = defineProps({
  company: Object,
  short: {
    type: Boolean,
    default: false,
  },
  big: {
    type: Boolean,
    default: false,
  },
});

const isSubscribed = computed({
  get: () => props.company.is_subscribed,
  set: (value) => props.company.is_subscribed = value
});

const subscribe = debounce(async () => {
  if (!global.auth()) {
    return;
  }

  await axios.post(
    route("seller.company.subscribe", { company: props.company.slug }),
  );

  isSubscribed.value = true;
}, 200);

const unsubscribe = debounce(async () => {
  await axios.post(
    route("seller.company.unsubscribe", {
      company: props.company.slug,
    }),
  );
  isSubscribed.value = false;
}, 200);

const isOwner = computed(() => {
  return global.user?.id === props.company?.user_id;
});

</script>
<template>
  <template v-if="isOwner">
    <CompanyManagment :company="company">
      <PhGearSix :size="big ? 24 : 20" />
      <span v-if="!short" class="hidden sm:inline">
        {{ __("Manage") }}
      </span>
    </CompanyManagment>
    <x-button intent="rounded" @click.stop class="!px-2"
      :href="route('partner.companies.edit', { company: company.slug })">
      <PhPencil :size="big ? 24 : 20" />
      <span v-if="!short" class="hidden sm:inline">
        {{ __("Edit") }}
      </span>

    </x-button>
  </template>
  <template v-else>
    <x-button v-bind="$attrs" intent="rounded" class="!text-xs" @click.stop="isSubscribed ? unsubscribe() : subscribe()"
      :class="{
        ' ':
          isSubscribed,
      }">
      {{ isSubscribed ? __("Unsubscribe")
        : __("Subscribe") }}
    </x-button>
  </template>
</template>
