<script setup>


defineProps({
    notification: Object,
});

</script>

<template>
    <div>
        <div v-if="notification.data?.message" class="text-center bg-blue-500 font-bold p-5 rounded-lg">
            {{ notification.data.message }}
        </div>
        <div v-if="notification.data?.constructor?.minutes" class="text-center  text-sm">
            {{ __('Offer valid for') }} {{ notification.data.constructor.minutes }} {{ __('minutes') }}
        </div>
        <x-button intent="secondary" classa="!w-full" v-if="notification.data?.constructor?.url" :href="notification.data.constructor.url">
            {{ __('View') }}
        </x-button>
    </div>
</template>