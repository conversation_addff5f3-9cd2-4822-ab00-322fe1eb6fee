<script setup>
import { computed, } from "vue";
import { useGlobalStore, Content, ErrorWidget, Translate } from "@s";
import PostHeader from "../Entities/Post/PostHeader.vue";
import PostFooter from "../Entities/Post/PostFooter.vue";
import { inject } from "vue";
import Comments from "../Widgets/User/Comments.vue";
import { router } from "@inertiajs/vue3";

const route = inject("route");

const global = useGlobalStore();

const props = defineProps({
    post: {
        type: [Object, null],
        required: true,
    },
    show: Boolean,
    repost: {
        type: Boolean,
        default: false,
    },
});

const isOwner = computed(() => global?.user?.id === props.post?.creator?.id);

const isNeedMore = computed(() => {
    return props.post?.content?.length > 511 && !props.show;
});

const gotoPost = () => {

    if (props.show) return;

    router.get(route('show.post', { post: props.post.slug }));

}

</script>

<template>
    <div>
        <template v-if="post">
            <div class="bg-bg dark:bg-dark-surface" @click="gotoPost"
                :class="{ 'cursor-pointer hover:opacity-80': !show }">
                <PostHeader :post="post" :isOwner class="px-4 pt-2" :class="{ '!px-0': repost }" @click.stop />
                <div class="  flex" v-if="post.media?.length">
                    <x-images :media="post.media" />
                </div>
                <div :class="{ '!px-0': repost }"
                    class="text-text dark:text-dark-text py-1 px-4 flex break-before-column flex-col text-lg font-semibold break-words">
                    <Content :content="post.content" />
                    <Translate :model="post" @click.stop />
                    <x-button v-if="isNeedMore" class="hover:text-secondary ml-auto justify-end" as="Link" intent="link"
                        :href="route('show.post', { post: post.slug })">{{ __("Read all") }}</x-button>
                    <div class="border p-1 border-muted rounded-2xl" v-if="post.repost_id">
                        <Post :post="post.repost" :repost="true" />
                    </div>
                    <x-hr class="my-2" />
                    <PostFooter :post="post" :show="show || repost" :repost @click.stop />
                </div>
            </div>
            <Comments :modelClass="'Post'" :modelId="post.id" v-if="show" :isOwner="isOwner" />
        </template>
        <template v-else>
            <ErrorWidget :message="__('Post not found...')" :back="show && !repost" />
        </template>
    </div>
</template>
