<script setup>
import { ref, onMounted, watch, inject, computed } from "vue";
import { PhCopy, PhShare, PhShareNetwork } from "@phosphor-icons/vue";
import QRCode from "qrcode";
import { Share } from "@s";

// Константы для быстрой настройки
const EMOJI_CATEGORIES = {
    Popular: ["💎", "🔥", "⭐", "✨", "💫"],
    Hearts: ["❤️", "💙", "💜", "🖤", "🤍"],
    Celebration: ["🎉", "🎊", "🥳", "🎈", "🌟"],
    Nature: ["🌸", "🦋", "🌺", "🌙", "☀️"],
    Symbols: ["💯", "⚡", "🔮", "👑", "💝"]
};

const BG_SIZE = 500;            // размер паттерна эмодзи
const EMOJI_OPACITY = 1;        // 100% видимость
const QR_SIZE = 1000;            // размер QR кода
const QR_COLOR = { dark: "#1a1a1a", light: "#ffffff" };

const route = inject("route");
const props = defineProps({ user: Object });
const user = props.user || { nickname: "" };

const selectedCategory = ref("Popular");
const selectedEmoji = ref(EMOJI_CATEGORIES.Popular[0]);
const qrPreview = ref("");
const emojiBg = ref("");

const currentEmojis = computed(() => EMOJI_CATEGORIES[selectedCategory.value] || EMOJI_CATEGORIES.Popular);

// Генерация QR
async function genPreview() {
    if (!user?.nickname) return (qrPreview.value = "");
    try {
        const url = route("profile", { user: user.nickname });
        qrPreview.value = await QRCode.toDataURL(url, {
            errorCorrectionLevel: "H",
            margin: 1,
            width: QR_SIZE,
            color: QR_COLOR
        });
    } catch (e) {
        console.error(e);
        qrPreview.value = "";
    }
}

// Генерация фона с равномерно расположенными эмодзи (как в Инсте)
function genEmojiTileDataUrl(emoji, size = BG_SIZE, opacity = EMOJI_OPACITY) {
    const canvas = document.createElement("canvas");
    canvas.width = canvas.height = size;
    const ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, size, size);
    ctx.globalAlpha = opacity;

    const numPerRow = 2;
    const padding = size / numPerRow + 10; // расстояние между эмодзи
    const fontSize = Math.floor(padding * 0.6);
    ctx.font = `${fontSize}px "Apple Color Emoji", "Segoe UI Emoji", sans-serif`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    for (let row = 0; row < numPerRow; row++) {
        for (let col = 0; col < numPerRow; col++) {
            const x = col * padding + padding / 2;
            const y = row * padding + padding / 2;
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate((Math.random() - 0.5) * Math.PI); 
            ctx.fillText(emoji, 0, 0);
            ctx.restore();
        }
    }

    return canvas.toDataURL("image/png");
}



function updateEmojiBg() {
    emojiBg.value = genEmojiTileDataUrl(selectedEmoji.value);
}


onMounted(() => { genPreview(); updateEmojiBg(); });
watch(() => user?.nickname, genPreview);
watch(selectedEmoji, updateEmojiBg);
</script>

<template>
    <x-button intent="rounded"
        class="  bg-gradient-to-r from-secondary to-pink-600/60  "
        @click="$refs.modal.modalOpen = true">
        <PhShare :size="24" class="transition-transform group-hover:rotate-12" />
        <span class="hidden sm:inline font-semibold">Share Profile</span>
    </x-button>

    <Modal  ref="modal" :title="'Share Profile'" class="!max-h-[100vh]">
        <div class="relative  p-10  rounded-2xl shadow-inner overflow-hidden" :style="{
            backgroundImage: emojiBg ? `url(${emojiBg}), linear-gradient(135deg, #ff0080, #ff8c00)` : 'linear-gradient(135deg, #ff0080, #ff8c00)',
            backgroundRepeat: 'repeat, no-repeat',
            backgroundSize: emojiBg ? '120px, cover' : 'cover'
        }">
            <div class="bg-bg rounded-2xl shadow-2xl  mx-auto w-fit ">
                <x-logo class="justify-center flex text-2xl "/>
                <img :src="qrPreview" alt="QR Code" class=" object-contain "  />
            </div>

            <div class="mt-6 text-center">
                <div class="inline-block bg-bg rounded-full px-6 py-2">
                    <span class="text-lg font-bold text-text">@{{ user.nickname }}</span>
                </div>
            </div>
        </div>

        <div class=" my-6">

            <div class="flex gap-1 overflow-x-auto pb-2">
                <x-button v-for="category in Object.keys(EMOJI_CATEGORIES)" :key="category"
                    @click="selectedCategory = category"
                    :class="selectedCategory === category ? '!bg-secondary/10 ' : ''">
                    {{ EMOJI_CATEGORIES[category][0] }}
                </x-button>
            </div>

            <div class="flex gap-1 overflow-x-auto">
                <x-button intent="secondary" v-for="emoji in currentEmojis" :key="emoji" @click="selectedEmoji = emoji">
                    {{ emoji }}
                </x-button>
            </div>
        </div>

        <div class="mx-2 pb-6">
            <div class="grid grid-cols-2 gap-3">
                <Share :url="route('profile', { user: user.nickname })" :toast="'Shared!'">
                    <x-button>
                        <PhShareNetwork :size="20" />
                        {{ __("Share") }}
                    </x-button>
                </Share>
                <Share copy :text="route('profile', { user: user.nickname })" :toast="'Copied!'">
                    <x-button>
                        <PhCopy :size="20" />
                        {{ __("Copy") }}
                    </x-button>
                </Share>
            </div>

            <p class="text-center text-sm text mt-4">
                {{ __("Share your profile with friends and clients to grow your business.") }}
            </p>
        </div>
    </Modal>
</template>
