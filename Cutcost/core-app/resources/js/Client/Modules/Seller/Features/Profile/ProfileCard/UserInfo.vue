<script setup>

import { useGlobalStore } from "@s";

defineProps({
  user: Object,
  counts: Object,
  isGuest: Boolean,
  isNotReachable: Boolean,
});

const global = useGlobalStore();


</script>
<template>
  <div class="mx-auto w-full">
    <div
      class=" flex justify-center sm:justify-start divide-x divide-gray-300 dark:divide-gray-700 text-gray-600 dark:text-gray-300">
      <component :is="(!isGuest && !global.checkAuth()) || isNotReachable ? 'span' : 'Link'"
        :href="route('seller.connections', { type: 'friends', user: user.nickname })"
        class="flex truncate flex-col items-center pr-2 hover:text-blue-500 transition">
        <span class="text-sm font-bold text-gray-900 dark:text-gray-100">{{ counts.friendsCount }}</span>
        <span class="text-xs  uppercase">{{ __('Friends') }}</span>
      </component>

      <component :is="(!isGuest && !global.checkAuth()) || isNotReachable ? 'span' : 'Link'"
        :href="route('seller.connections', { type: 'subscribers', user: user.nickname })"
        class="flex truncate flex-col items-center px-2 hover:text-blue-500 transition">
        <span class="text-sm font-bold text-gray-900 dark:text-gray-100">{{ counts.subscribersCount }}</span>
        <span class="text-xs  uppercase">{{ __('Subscribers') }}</span>
      </component>

      <component :is="(!isGuest && !global.checkAuth()) || isNotReachable ? 'span' : 'Link'"
        :href="route('seller.connections', { type: 'following', user: user.nickname })"
        class="flex truncate flex-col items-center px-2 hover:text-blue-500 transition">
        <span class="text-sm font-bold text-gray-900 dark:text-gray-100">
          {{ counts.followingCount }}
        </span>
        <span class="text-xs  uppercase">{{ __('Following') }}</span>
      </component>

      <component :is="(!isGuest && !global.checkAuth()) || isNotReachable ? 'span' : 'Link'"
        :href="route('seller.businesses', { user: user.nickname })"
        class="flex truncate flex-col items-center pl-2 hover:text-blue-500 transition">
        <span class="text-sm font-bold text-gray-900 dark:text-gray-100">
          {{ counts.businessesCount }}
        </span>
        <span class="text-xs  uppercase">{{ __('Businesses') }}</span>
      </component>
    </div>
  </div>


</template>
