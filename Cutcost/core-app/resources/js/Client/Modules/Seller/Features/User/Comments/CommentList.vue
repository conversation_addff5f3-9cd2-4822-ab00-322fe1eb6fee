<script setup>
import Comment from '../../../Entities/Comments/Comment.vue'

const props = defineProps({
  comments: { type: Array, default: () => [] },
  isOwner: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['reply', 'edit', 'delete'])
</script>

<template>
  <ul class="space-y-1">
    <li v-for="comment in comments" :key="comment.id">
      <Comment :comment="comment" @reply="emit('reply', $event)" @edit="emit('edit', $event)"
        @delete="emit('delete', $event)" :isOwner="isOwner" />

      <ul v-if="comment.replies?.length" class="relative mt-1 space-y-2 pl-1">
        <span class="absolute left-1 top-0 bottom-0 w-1 bg-primary dark:bg-dark-primary rounded"></span>

        <li v-for="reply in comment.replies" :key="reply.id" class="pl-3">
          <Comment :comment="reply" @reply="emit('reply', $event)" @edit="emit('edit', $event)"
            @delete="emit('delete', $event)" reply/>
        </li>
      </ul>
    </li>
  </ul>
</template>
