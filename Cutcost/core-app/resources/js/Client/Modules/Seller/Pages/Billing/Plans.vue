<script setup>
import { Head } from "@inertiajs/vue3";
import { useGlobalStore } from "@s";

const global = useGlobalStore();

defineProps({
    plans: Object,
});
</script>

<template>

    <Head>
        <title>{{ __('Plans') }}</title>
    </Head>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl font-extrabold text-center mb-12 text-gray-900 dark:text-white">
            {{ __('Choose Your Plan') }}
        </h1>

        <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <div v-for="plan in plans" :key="plan.id"
                class="group relative bg-white dark:bg-gray-900 rounded-2xl shadow-md hover:shadow-2xl transition-all duration-300 p-6 border border-gray-100 dark:border-gray-800 hover:border-indigo-500 hover:-translate-y-1">
                <!-- Highlight for popular plan -->
                <div v-if="plan.code === 'premium'"
                    class="absolute top-0 right-0 bg-indigo-600 text-white text-xs font-semibold px-3 py-1 rounded-bl-2xl rounded-tr-2xl">
                    {{ __('Popular') }}
                </div>

                <h2
                    class="text-2xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-indigo-600 transition">
                    {{ plan.name[global.currentLocale] ?? plan.name.en }}
                </h2>

                <p class="text-gray-500 dark:text-gray-400 mb-6">
                    {{ plan.description[global.currentLocale] ?? plan.description.en }}
                </p>

                <!-- Price -->
                <div class="mb-6">
                    <span class="text-4xl font-extrabold text-gray-900 dark:text-white">
                        {{ plan.price > 0 ? `$${plan.price}` : __('Free') }}
                    </span>
                    <span v-if="plan.price > 0" class="text-gray-500 dark:text-gray-400">
                        /{{ plan.interval }}
                    </span>
                </div>

                <!-- Features -->
                <ul class="space-y-2 mb-8">
                    <li v-for="(feature, idx) in plan.features" :key="idx" class="flex items-start">
                        <svg class="flex-shrink-0 h-5 w-5 text-indigo-600 mt-1" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span class="ml-2 text-gray-700 dark:text-gray-300">
                            {{ feature.translations[global.currentLocale] ?? feature.translations.en }}
                        </span>
                    </li>
                </ul>

                <button
                    class="w-full bg-indigo-600 text-white font-semibold py-3 rounded-xl hover:bg-indigo-700 transition focus:ring-4 focus:ring-indigo-300">
                    {{ plan.price > 0 ? __('Subscribe') : __('Get Started') }}
                </button>
            </div>
        </div>
    </div>
</template>
