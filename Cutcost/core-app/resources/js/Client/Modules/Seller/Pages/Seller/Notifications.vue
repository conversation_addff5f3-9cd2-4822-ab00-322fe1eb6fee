<script setup>
import { inject, onMounted, onUnmounted, ref } from 'vue';
import { useHeaderStore } from "@m/Fixed";
import UserRequested from "../../Features/Notifications/UserRequested.vue";
import UserMentions from "../../Features/Notifications/UserMentioned.vue";
import Banner from "../../Features/Notifications/Banner.vue";
import { useHelpers, BottomText } from '@s';
import { PhDotsThreeVertical } from '@phosphor-icons/vue';

const route = inject("route");
const header = useHeaderStore();
const { diffForHumans } = useHelpers();
const deletedIds = ref([]);

const props = defineProps({
  notifications: Object,
  users: Object,
});

onMounted(() => {
  header.title = 'Notifications';
});

onUnmounted(() => {
  header.clear();
});

const destroy = (id) => {
  axios.delete(route('seller.notifications.destroy', { id }));
  deletedIds.value.push(id);
};

const text = (type) => {
  if (type === 'friend_request') {
    return 'You received a new friend request';
  } else if (type?.includes('mention')) {
    return 'You were mentioned in a post';
  } else if (type?.includes('banner')) {
    return 'You have a new message';
  }
}
</script>

<template>

  <Head :title="__('My notifications')" />
  <div class="card-position">
    <div v-for="notification in notifications" v-if="notifications?.length" :key="notification?.id" class="text p-1"
      :class="notification.read_at ? '' : 'bg-secondary/10'">
      <div v-if="!deletedIds.includes(notification.id)" class="border-b border-border dark:border-dark-border pb-2">
        <div class="justify-between flex items-center my-1 mx-1">
          <h3 class="h3 text-center" v-if="text(notification.type)">
            {{ __(text(notification.type)) }}
          </h3>
          <Dropdown position="bottom-right">
            <template #button>
              <x-button intent="rounded">
                <PhDotsThreeVertical :size="20" />
              </x-button>
            </template>
            <DropdownItem @click="destroy(notification.id)">
              {{ __("Delete") }}
            </DropdownItem>
            <DropdownItem>
              {{ diffForHumans(notification?.created_at) }}
            </DropdownItem>
          </Dropdown>
        </div>
        <UserRequested :notification :user="users.find(u => u.id === notification?.data?.constructor?.user_id)"
          v-if="notification.type === 'friend_request'" />
        <UserMentions v-if="notification.type?.includes('mention')" :notification="notification" />
        <Banner v-if="notification.type?.includes('banner')" :notification="notification" />
      </div>
    </div>
    <div v-else class="text-center">
      <BottomText long :message="__('Your notifications will be here')" />
    </div>
  </div>
</template>
