<script setup>
import PlansSection from '../../../../Partner/Widgets/Billing/PlansSection.vue';
import PartnershipForm from '../../../Widgets/Partner/PartnershipForm.vue';

defineProps({
    application_status: String,
    subscription_status: String,
});

</script>

<template>

    <Head :title="__('Become a partner')" />
    <div class="card-position">
        <PlansSection :application_status :subscription_status />
    </div>
</template>
