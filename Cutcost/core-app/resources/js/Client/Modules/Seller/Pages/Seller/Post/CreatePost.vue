<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import Post from '../../../Features/Post.vue';
import PostForm from '../../../Features/Profile/PostForm.vue';
import Recommendations from '../../../Widgets/Recommendations.vue';
import { useHeaderStore } from "@m/Fixed";
import { useProfileStore } from '../../../Stores/use-profile-store';
import { inject } from "vue";
import { router } from "@inertiajs/vue3";

const route = inject("route");

const header = useHeaderStore();
const profile = useProfileStore();
const repost = ref(null);

onMounted(() => {
    if (profile?.repostSlug) {
        header.title = 'Repost post';
    } else {
        header.title = 'Create post';
    } 

    if (profile.repostSlug) {

        axios.get(route('api.post.get', { postSlug: profile.repostSlug })).then((response) => {
            repost.value = response.data;
        }).catch((error) => {
            console.error(error);

            // if something wrong with post, clear repost slug and redirect 
            profile.repostSlug = null;
            router.get(route('feed'));
        });
        
    }
});

onUnmounted(() => {
    header.clear();
    profile.repostSlug = null;
    repost.value = null;
});

</script>

<template>
    <Head :title="__(header.title)"/>
    <div class="card-position relative flex max-sm:flex-col">
        <div class="post-position ">
            <PostForm create :repost />
        </div>
         <div v-if="repost?.id" class="post-position space-y-2">
            <Post :post="repost" />
        </div>
        <Recommendations />
    </div>
</template>