<script setup>
import { onMounted, onUnmounted } from 'vue';
import PostForm from '../../../Features/Profile/PostForm.vue';
import Recommendations from '../../../Widgets/Recommendations.vue';
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

defineProps({
    post: Object,
});

onMounted(() => {
    header.title = 'Edit post';
});

onUnmounted(() => {
    header.clear();
});

</script>

<template>

    <Head :title="__('Edit post') " />

    <div class="card-position relative flex gap-2 max-sm:flex-col">
        <div class="post-position space-y-2">
            <PostForm :post="post" />
        </div>
        <Recommendations />
    </div>
</template>