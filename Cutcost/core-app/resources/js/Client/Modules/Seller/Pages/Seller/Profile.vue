<script setup>
import { useProfileStore } from "../../Stores/use-profile-store";
import ProfileCard from "../../Widgets/Profile/ProfileCard.vue";
import { computed } from "vue";
import { Head } from "@inertiajs/vue3";
import Recommendations from "../../Widgets/Recommendations.vue";
import { useGlobalStore, useTranslation, ErrorWidget } from "@s";
import ProfileFeed from "../../Widgets/Profile/ProfileFeed.vue";

const global = useGlobalStore();

const profile = useProfileStore();

const props = defineProps({
  friend: [Object, null],
  data: Object,
  as_guest: Boolean,
  reachable: Boolean,
  status: Number,
});

const counts = computed(() => {
  return {
    'friendsCount': props.reachable ? props.data?.friends_count ?? 0 : '?',
    'subscribersCount': props.reachable ? props.data?.subscribers_count ?? 0 : '?',
    'followingCount': props.reachable ? props.data?.following_count ?? 0 : '?',
    'businessesCount': props.reachable ? props.data?.businesses_count ?? 0 : '?',
  };
});

const user = computed(() => {
  if (props.friend) return props.friend;

  if (!global.checkAuth() && !props.as_guest) {
    return {
      id: -1,
      display_name: 'Guest',
      nickname: 'guest',
      avatar: null,
      cover: null,
    }
  } else if (props.as_guest && !props.friend) {
    return null;
  } else {
    return profile.user;
  }

});

const title = computed(() => {

  if (!user.value) return 'Profile not found...';

  return props.friend ? useTranslation.__('Profile') + ' - ' + props.friend?.display_name
    : useTranslation.__('My Profile');
});

</script>

<template>

  <Head :title />
  <div class="card-position relative flex gap-2">
    <div class="text post-position space-y-2">
      <template v-if="user">
        <ProfileCard :user :reachable :counts :isGuest="as_guest" />
        <ProfileFeed :user v-if="(as_guest ? true : global.checkAuth()) && reachable" />
      </template>
      <template v-else>
        <ErrorWidget :message="status === 403 ? __('Resource is not available') : __('Profile not found...')" back />
      </template>
    </div>
    <Recommendations class="w-1/3" v-if="!global.isMobile" />
  </div>
</template>
