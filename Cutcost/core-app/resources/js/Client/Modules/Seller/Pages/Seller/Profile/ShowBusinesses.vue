<script setup>
import { onMounted, onUnmounted, inject } from 'vue';
import { useHeaderStore } from "@m/Fixed";
import Companies from '../../../../Public/Widgets/Feeds/Companies.vue';
import { useTranslation } from "@s";

const route = inject("route");
const header = useHeaderStore();

const props = defineProps({
    user: Object,
});

const options = [
    { id: 'following', name: 'following' },
    { id: 'owned', name: 'owned' },
];


onMounted(() => {
    header.navigation = 'businesses_';
    header.options = options;
    header.title = useTranslation.__('Businesses') + ' | ' + props.user.display_name;
    header.backUrl = route('profile', { user: props.user.nickname });
    header.chosenTab = localStorage.getItem(header.navigation + '.selected') ?? 'owned';
    header.swipeEvent();
})

onUnmounted(() => {
    header.clear();
})

</script>

<template>

    <Head :title="__('Businesses')" />
    <div class="card-position">
        <Companies type="company_user" :data="{ user: user.id }" v-if="header.chosenTab === 'owned'" />
        <Companies type="company_userFollowing" :data="{ user: user.id }" v-if="header.chosenTab === 'following'" />
    </div>
</template>