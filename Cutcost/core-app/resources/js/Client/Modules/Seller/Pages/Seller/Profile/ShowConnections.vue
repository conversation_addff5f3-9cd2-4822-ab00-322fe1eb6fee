<script setup>
import { onMounted, inject, onUnmounted, watch } from 'vue';
import { useHeaderStore } from "@m/Fixed";
import { Users } from "@m/Public";
import { router } from "@inertiajs/vue3";
import { useTranslation } from "@s";

const route = inject("route");

const header = useHeaderStore();

const props = defineProps({
    type: String,
    user: Object,
});

const options = [
    { id: 'friends', name: 'friends' },
    { id: 'subscribers', name: 'subscribers' },
    { id: 'following', name: 'following' },
];

onMounted(() => {
    header.chosenTab = props.type;
    header.options = options;
    header.title = useTranslation.__('Connections') + ' | ' + props.user.display_name;
    header.navigation = 'connection';
    header.backUrl = route('profile', { user: props.user.nickname });
    header.swipeEvent();
});

watch(() => header.chosenTab, (val) => {
    router.get(route('seller.connections', { user: props.user.nickname, type: val }));
});

onUnmounted(() => {
    header.clear()
});

</script>

<template>
    <Head :title="__('Connections')" />

    <div class="card-position">
        <Users :type="'user_' + header.chosenTab" :data="{ user: user.id }" />
    </div>
</template>