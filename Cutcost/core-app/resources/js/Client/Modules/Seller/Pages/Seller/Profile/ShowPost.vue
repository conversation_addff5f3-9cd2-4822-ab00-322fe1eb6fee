<script setup>
import { Head } from "@inertiajs/vue3";
import { Recommendations } from "@m/Seller";
import Post from "../../../Features/Post.vue";
import { onMounted, onUnmounted } from "vue";
import { useHeaderStore } from "@m/Fixed";

const header = useHeaderStore();

const props = defineProps({
    post: [Object, null],
    status: [Number, null],
});

onMounted(() => {
    header.title = 'View post';
})


onUnmounted(() => {
    header.clear();
});
</script>
<template>
    <Head :title="__(header.title)" />
    <div class="card-position flex gap-2 max-sm:flex-col">
        <div class="post-position ">
            <Post :post="post" :show="true" />
        </div>
        <Recommendations />
    </div>
</template>
