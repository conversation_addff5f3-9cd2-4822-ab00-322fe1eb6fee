<script setup >
import { Head } from "@inertiajs/vue3";
import Recommendations from "../../../Widgets/Recommendations.vue";
import { Users } from "@m/Public";
import { onMounted } from "vue";
import { useHeaderStore } from "@m/Fixed";
import { useTranslation } from "@s";

const header = useHeaderStore();

const props = defineProps({
  data: {
    type: Object,
    required: false,
  },
  type: String,
  title: String,
  name: String,
});

onMounted(() => {
  header.title = props.name + ' ' + props.title;
});
</script>

<template>
  <Head :title="name + ' ' + __(title) " />
  <div class="card-position !mt-6">
    <div class="relative flex  gap-2 max-sm:flex-col ">
      <div class="post-position flex flex-col space-y-2 ">
        <Users :type :data />
      </div>
      <Recommendations />
    </div>
  </div>
</template>
