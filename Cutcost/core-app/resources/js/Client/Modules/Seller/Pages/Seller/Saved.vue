<script setup>
import { onMounted, onUnmounted } from 'vue';
import { useHeaderStore } from "@m/Fixed";
import { Coupons, Posts, Locations } from "@m/Public";

const header = useHeaderStore();

const options = [
    { id: 'coupons', name: 'coupons' },
    { id: 'posts', name: 'posts' },
    { id: 'locations', name: 'locations' },
];

onMounted(() => {
    header.navigation = 'saved';
    header.title = 'Saved';
    header.options = options;
    header.chosenTab = localStorage.getItem(header.navigation + '.selected') ?? 'coupons';
    header.swipeEvent();
});

onUnmounted(() => {
    header.clear();
});

</script>

<template>

    <Head :title="__('Saved')" />
    <div class="card-position">
        <div v-show="header.chosenTab === 'coupons'">
            <Coupons type="coupon_bookmarks" />
        </div>
        <div v-show="header.chosenTab === 'posts'">
            <Posts type="post_bookmarks" />
        </div>
        <div v-show="header.chosenTab === 'locations'">
            <Locations type="location_bookmarks" />
        </div>
    </div>
</template>
