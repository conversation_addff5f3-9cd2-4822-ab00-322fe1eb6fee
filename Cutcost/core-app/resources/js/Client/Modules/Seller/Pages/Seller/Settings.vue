<script setup>
import ProfileDetails from "../../Widgets/Settings/ProfileDetails.vue";
import AuthSettings from "../../Widgets/Settings/AuthSettings.vue";
import LanguageAndRegion from "../../Widgets/Settings/LanguageAndRegion.vue";
import PrivacyAndPreferencesSettings from "../../Widgets/Settings/PrivacyAndPreferencesSettings.vue";
import { Head } from "@inertiajs/vue3";
import { onMounted, onUnmounted, nextTick } from "vue";
import { useHeaderStore } from "@m/Fixed";
import { useGlobalStore, useToastStore, useTranslation } from "@s";
import PaymentsSettings from "../../Widgets/Settings/PaymentsSettings.vue";

const global = useGlobalStore();
const toast = useToastStore();

const header = useHeaderStore();

defineProps({
    locales: Object,
    city: Object,
    sessions: Object,
});

onMounted(async () => {
    header.title = 'Settings';

    await nextTick();

    const langAndRegion = document.getElementById('lang-and-region');

    if (langAndRegion) {
        const url = new URLSearchParams(window.location.search);
        if (url.get('tab') === 'lang-and-region') {

            if (!global.user?.preferences?.translate_to)
                toast.warning(useTranslation.__('Please select a language to translate to'));

            langAndRegion.scrollIntoView({ behavior: "smooth", block: "start" });
        }
    }
});

onUnmounted(() => {
    header.clear();
});

</script>

<template>
    <Head>
        <title>{{ __("Settings") }}</title>
    </Head>
    <div class="card-position">
        <ProfileDetails :city />
        <div id="lang-and-region">
            <LanguageAndRegion :locales :city :preferences="global.user?.preferences" />
        </div>
        <PrivacyAndPreferencesSettings :preferences="global.user?.preferences" />
        <PaymentsSettings class="mb-2"/>
        <AuthSettings :sessions />
    </div>
</template>
