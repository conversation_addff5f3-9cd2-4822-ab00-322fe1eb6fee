import { inject } from "vue";

export const friendRepository = () => {
  const route = inject("route");

  const unfriend = async (userId) => {
    try {
      const response = await axios.post(
        route("seller.delete.friend", { user: userId }),
      );
    } catch (err) {
      console.error(err);
    }
  };

  const addFriend = async (userId) => {
    try {

      const response = await axios.post(
        route("seller.send.friend.request", { user: userId }),
      );
    } catch (err) {
      console.error(err);
    }
  };

  const cancelRequest = async (userId) => {
    try {
      const response = await axios.post(
        route("seller.cancel.request", { user: userId }),
      );
    } catch (err) {
      console.error(err);
    }
  };

  const acceptFriend = async (userId) => {
    try {
      const response = await axios.post(
        route("seller.accept.friend.request", { user: userId }),
      );
    } catch (err) {
      console.error(err);
    }
  };

  return {
    unfriend,
    addFriend,
    cancelRequest,
    acceptFriend,
  };
};
