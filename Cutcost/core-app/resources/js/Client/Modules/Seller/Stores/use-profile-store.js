import { usePage } from "@inertiajs/vue3";
import { defineStore } from "pinia";
import { computed, ref, reactive, shallowRef } from "vue";

export const useProfileStore = defineStore("profile", () => {
  const page = usePage();
  const user = computed(() => page.props?.auth?.user);
  const socials = computed(() => page.props?.auth?.socials);

  const isOwner = ref(false);

  const post = reactive({});
  const isEdit = shallowRef(false);

  const repostSlug = shallowRef(null);

  const friendRequest = (friend) => {
    if (!user.value?.id) return;

    socials.value.subscribers.unshift(friend);
    socials.value.subscribers = Array.from(
      new Map(
        socials.value.subscribers
          .filter((val) => val.id)
          .map((sub) => [sub.id, sub]),
      ).values(),
    );
  };

  return {
    isOwner,
    user,
    isEdit,
    post,
    socials,
    repostSlug,
    friendRequest,
  };
});
