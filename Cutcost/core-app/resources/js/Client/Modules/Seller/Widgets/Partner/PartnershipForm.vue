<script setup>

import { inject } from 'vue';
import { useForm } from '@inertiajs/vue3';
import { useGlobalStore } from "@s";

const route = inject('route');

const global = useGlobalStore();

defineProps({
    application_status: String,
});

const form = useForm({
    full_name: '',
    phone: '',
    email: global.user?.email,
    country: '',
    address: '',
    company_name: '',
    notes: '',
})


function submit() {
    form.post(route('seller.partner.apply'))
}
</script>

<template>
    <x-form type="create" @submit="submit" noClass :form="form" v-if="!application_status">
        <div class="space-y-2">
            <x-input :message="form.errors.full_name" :label="__('Full name') + '*'" v-model="form.full_name" />
            <x-input :message="form.errors.email" label="Email*" v-model="form.email" />
            <x-input :message="form.errors.phone" :label="__('Phone') + '*'" v-model="form.phone" />
            <x-input :message="form.errors.country" :label="__('Country') + '*'" v-model="form.country" />
            <x-input :message="form.errors.address" :label="__('Address')" v-model="form.address" />
            <x-input :message="form.errors.company_name" :label="__('Company name')" v-model="form.company_name" />
            <x-textarea :message="form.errors.notes" :label="__('Notes')" v-model="form.notes" />
        </div>
    </x-form>
    <Card v-else noStyle>
        <div v-if="application_status === 'pending'">
            <p class="text-success">{{ __("Application given") }}</p>
        </div>
        <div class="mt-6">
            {{ __("Status") }}: {{ __(application_status) }}
        </div>
    </Card>
</template>
