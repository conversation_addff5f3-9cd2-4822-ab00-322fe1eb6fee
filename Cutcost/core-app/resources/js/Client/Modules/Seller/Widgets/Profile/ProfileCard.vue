<script setup>
import { PhPencil } from "@phosphor-icons/vue";
import FriendControls from "../../Features/FriendControls.vue";
import UserInfo from "../../Features/Profile/ProfileCard/UserInfo.vue";
import { computed, inject } from "vue";
import { useToastStore, useTranslation, AvatarAndCover, BottomText, useGlobalStore } from "@s";
import ShareProfile from "../../Features/Profile/ProfileCard/ShareProfile.vue";

const route = inject("route");
const toast = useToastStore();
const global = useGlobalStore();

const props = defineProps({
  counts: Object,
  user: [Object, null],
  isGuest: Boolean,
  reachable: Boolean,
});

const copyNickname = () => {
  navigator.clipboard.writeText(props.user.nickname);
  toast.success(useTranslation.__('Nickname copied'));
};

const isNotAuthAndNoUser = computed(() => {
  return !global.checkAuth() && !props.user;
});

const isNotFound = computed(() => {
  return !props.user;
});

const isNotReachable = computed(() => {
  return props.user && !props.reachable;
});

</script>

<template>
  <AvatarAndCover :urls="[user?.avatar, user?.cover]" :editable="!isGuest"
    :data="{ model_type: 'user', model_id: user?.id }">
    <div class="flex gap-1" v-if="!isGuest">
      <x-button :href="route('seller.settings')" intent="rounded">
        <template #icon>
          <PhPencil :size="24" />
        </template>
        <span class="hidden sm:inline">
          {{ __('Edit profile') }}
        </span>
      </x-button>
      <ShareProfile :user />
    </div>
    <div v-if="isGuest && user && !isNotReachable">
      <x-button v-if="global.checkAuth()" as="Link" class="!px-2" :href="route('messenger.chat', {
        type: 'nickname',
        key: user?.nickname,
      })
        ">
        {{ __("Message") }}
      </x-button>
    </div>
    <FriendControls :user :status="user.friend_status" />
  </AvatarAndCover>
  <template v-if="user">

    <div class="flex text mx-2 flex-col gap-4 sm:flex-row sm:justify-between">
      <div class="flex w-full flex-col gap-4">
        <div>
          <div class="leading-4">
            <h1 class="text-text dark:text-dark-text flex h1 font-extrabold ">
              {{ user.display_name }}
            </h1>
            <div class="flex text-muted cursor-pointer" @click="copyNickname">
              @{{ user.nickname }}
            </div>
          </div>
          <div class="text">
            {{ user.description }}
          </div>
        </div>
        <div class="flex flex-col space-y-1">
          <UserInfo :user="user" :isNotReachable :counts :isGuest />
        </div>
      </div>
    </div>
  </template>
  <template v-if="isNotAuthAndNoUser || isNotReachable">
    <div class="mt-30 justify-center flex ">
      <BottomText :message="__('User not found...')" v-if="isNotFound" />
      <BottomText :message="__('User profile is private')" v-else-if="isNotReachable" />
      <x-button intent="success" @click="global.auth()" v-else>
        {{ __("Login") }}
      </x-button>
    </div>
  </template>
</template>
