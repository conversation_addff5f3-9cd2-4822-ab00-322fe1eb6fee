<script setup>
import { UserCard } from "@s";
import { CompanyCard } from "@m/Public";

</script>
<template>
  <div v-if="false" class="w-full max-[940px]:w-full sm:sticky sm:inset-y-4 sm:h-20 md:w-1/3">
    <Card :title="__('Recommendations')">
      <div class="w-full flex flex-col scroll-hidden space-y-2 max-h-[70vh] overflow-y-auto">

        <div v-for="user in recommendations?.users">
          <UserCard :user mini noControls />
        </div>
        <x-hr class="my-2" v-if="
          recommendations?.users.length && recommendations?.companies.length
        " />
        <div v-for="company in recommendations?.companies">
          <CompanyCard :company short/>
        </div>
      </div>

    </Card>
  </div>
</template>
