<script setup>
import { router } from '@inertiajs/vue3'
import { ref, inject } from "vue";

const route = inject("route");

defineProps({ sessions: Array })

const deleteUserDialog = ref(false);
const deleteSessionDialog = ref(false);

const sessionId = ref(null);

const destroySession = () => {
    router.delete(route('seller.auth.session.destroy', { sessionId: sessionId.value }), {
        preserveScroll: true,
    });
    sessionId.value = null;
    deleteSessionDialog.value = false;
}

const deleteUser = () => {
    router.delete(route('seller.auth.user.destroy'));
    deleteUserDialog.value = false;
}

const cancelDeleteSession = () => {
    deleteSessionDialog.value = false;
    sessionId.value = null;
}

const cancelDeleteUser = () => {
    deleteUserDialog.value = false;
}
</script>

<template>
    <x-confirmation v-if="deleteUserDialog" strict for="delete_user" @confirm="deleteUser" @cancel="cancelDeleteUser"
        :message="__('Are you sure you want to delete your account?')" />
    <x-confirmation v-if="deleteSessionDialog" @confirm="destroySession" @cancel="cancelDeleteSession"
        for="delete_session" :message="__('Are you sure')" />

    <Card :title="__('Auth')">

        <div v-if="sessions.length" class="space-y-4">
            <div v-for="session in sessions" :key="session.id"
                class="p-2 gap-4 text rounded-xl shadow-sm bg-surface dark:bg-bg-dark flex justify-between items-center">
                <div>
                    <p class="font-semibold">
                        {{ session.ip_address }}
                        <span v-if="session.is_current_device" class="text-success text-sm ml-2">
                            ({{ __('Current Device') }})
                        </span>
                    </p>
                    <p class="text-sm text-muted ">{{ session.user_agent }}</p>
                    <div class="flex gap-2 items-center justify-between">
                        <p class="mt-3 text-center ">{{ session.last_active }}</p>
                        <x-button intent="danger"  class="mt-1" v-if="!session.is_current_device"
                        @click="deleteSessionDialog = true; sessionId = session.id">
                        {{ __('Logout') }}
                    </x-button>
                </div>
                </div>
            </div>
        </div>
        <x-hr class="my-4" />
        <div class="flex justify-end gap-2">
            <x-button as="Link" method="post" :href="route('logout')">
                {{ __('Logout') }}
            </x-button>
            <x-button intent="danger"  @click="deleteUserDialog = true">
                {{ __('Delete account') }}
            </x-button>
        </div>
    </Card>
</template>
