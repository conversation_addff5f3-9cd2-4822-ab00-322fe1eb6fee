<script setup>
import { inject } from "vue";
import { useForm } from "@inertiajs/vue3";
import Cities from "../../../Public/Widgets/Feeds/Cities.vue";
import { useCache } from "@s";

const route = inject("route");
const { set } = useCache();

const props = defineProps({
    locales: Object,
    preferences: Object,
    city: Object,
});

const form = useForm({
    translate_to: props.preferences?.translate_to ?? null,
    dont_translate: props.preferences?.dont_translate ?? [],
    timezone: props.preferences?.timezone ?? Intl.DateTimeFormat().resolvedOptions().timeZone,
    city_id: props.preferences?.city_id,
});

const update = () => {

    form.put(route("seller.settings.lang-and-region"), {
        preserveScroll: true
    });

    set('temporary_translate_to', form.translate_to);
};

const translateLocales = [
    'en',
    'ru',
    'lt',
    'pl',
    'de',
    'es',
]

</script>
<template>
    <x-form @submit="update" class="flex flex-col " :form :title="__('Language and region')">
        <div class="space-y-2">
            <div>
                <x-select :data="locales.filter(l => translateLocales.includes(l.code))" v-model="form.translate_to"
                    :label="__('Translate to')" :message="form.errors.locales" lang search use="code" />
            </div>
            <div>
                <x-select :data="locales.filter(l => l.code !== form.translate_to)" v-model="form.dont_translate"
                    :label="__('Never translate this languages')" :message="form.errors.locales" multiple lang search
                    use="code" />
            </div>
            <div>
                <x-input v-model="form.timezone" :label="__('Timezone')" disabled />
            </div>
            <Cities @apply="form.city_id = $event.id" :cityId="form.city_id" />
        </div>
    </x-form>
</template>