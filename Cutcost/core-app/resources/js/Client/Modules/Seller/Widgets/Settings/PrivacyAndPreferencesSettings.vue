<script setup>
import { useForm } from "@inertiajs/vue3";
import { inject } from "vue";
import { useGlobalStore } from "@s";

const route = inject("route");
const global = useGlobalStore();
const props = defineProps({
    preferences: Object,
});

const form = useForm({
    visibility: props.preferences?.visibility ?? 'everyone',
    messenger_visibility: props.preferences?.messenger_visibility ?? 'everyone',
    allow_comments: props.preferences?.allow_comments ?? true,
    allow_personalized_ads: props.preferences?.allow_personalized_ads ?? false,
    allow_email_notifications: props.preferences?.allow_email_notifications ?? false,
    store_prefrences_on_device: props.preferences?.store_prefrences_on_device ?? true,
    disable_unnecessary_logs: props.preferences?.disable_unnecessary_logs ?? false,
});

const visibilityOptions = [
    { id: 'everyone', name: 'Everyone' },
    { id: 'friends', name: 'Friends only' },
    { id: 'nobody', name: 'Nobody' },
];

const messengerOptions = [
    { id: 'everyone', name: 'Everyone' },
    { id: 'friends', name: 'Friends only' },
    { id: 'nobody', name: 'Nobody' },
];

const update = () => {
    form.put(route("seller.settings.privacy-preferences"), {
        preserveScroll: true,
    });

    global.user.preferences = {
        ...global.user.preferences,
        ...form,
    }; 
};
</script>

<template>
    <div>
        <x-form :title="__('Privacy & Preferences')" :form :type="__('ui.buttons.update')" @submit="update">
            <div class="flex space-y-2 flex-col">
                <x-select :label="__('Visibility')" v-model="form.visibility" :data="visibilityOptions" translate />
                <x-select :label="__('Who can send you direct messages')" v-model="form.messenger_visibility"
                    :data="messengerOptions" translate />
                <x-checkbox v-model="form.allow_comments" :label="__('Allow comments')" />
                <x-checkbox v-model="form.allow_personalized_ads" :label="__('Allow personalized ads')" />
                <x-checkbox v-model="form.allow_email_notifications" :label="__('Allow email notifications')" />
                <x-checkbox v-model="form.store_prefrences_on_device" :label="__('Store preferences on your device')" />
                <x-checkbox v-model="form.disable_unnecessary_logs" :label="__('Disable unnecessary logs')" />
            </div>
        </x-form>
    </div>
</template>
