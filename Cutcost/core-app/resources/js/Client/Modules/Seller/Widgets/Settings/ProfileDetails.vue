<script setup>
import { useForm, usePage } from "@inertiajs/vue3";
import { computed } from "vue";
import { inject } from "vue";
import { useGlobalStore } from "@s";

const route = inject("route");
const global = useGlobalStore();

const page = usePage();
const user = computed(() => page.props.auth.user);

const detailsForm = useForm({
  description: user.value.description ?? "",
  display_name: user.value.display_name ?? "",
  nickname: user.value.nickname ?? "",
});

const updateDetails = () => {
  detailsForm.put(route("seller.settings.details"));
  global.isReload = true;
};

</script>

<template>
  <div>
    <section>
      <div>
        <x-form @submit="updateDetails" class="flex flex-col " back :form="detailsForm" :title="__('Profile details')">
          <div class="flex flex-col gap-4">
            <div>
              <x-input :max="16" v-model="detailsForm.nickname" :label="__('Nickname')" :placeholder="user.nickname"
                :message="detailsForm.errors.nickname" />
            </div>
            <div>
              <x-textarea v-model="detailsForm.description" :label="__('Description')"
                :message="detailsForm.errors.description" />
              <x-input v-model="detailsForm.display_name" :label="__('Name')"
                :message="detailsForm.errors.display_name" />
            </div>
          
          </div>
        </x-form>

      </div>
    </section>
  </div>
</template>
