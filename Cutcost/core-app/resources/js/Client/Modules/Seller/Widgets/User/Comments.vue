<script setup>
import { ref, watch, useTemplateRef, nextTick, computed } from "vue";
import { useCommentApi } from "../../Api/CommentApi";
import CommentList from "../../Features/User/Comments/CommentList.vue";
import { useGlobalStore, BottomText, useToastStore } from "@s";
import Textarea from "../../../Messenger/Features/InputField/Textarea.vue";

const props = defineProps({
    modelClass: String,
    modelId: Number,
    isOwner: {
        type: Boolean,
        default: false,
    },
});

// State
const comments = ref([]);
const newComment = ref("");
const replyingTo = ref(null);
const editingComment = ref(null);
const editingBody = ref("");
const cursor = ref(null);
const hasMore = ref(true);
const loadingMore = ref(false);
const modal = useTemplateRef("modal");
const commentId = ref(null);
const toast = useToastStore();

const restricted = ref(false);

const global = useGlobalStore()

// API
const { fetchComments, createComment, updateComment, deleteComment } = useCommentApi();

// Load a page of comments
async function loadPage() {
    if (restricted.value) return;
    if (!hasMore.value || loadingMore.value) return;

    loadingMore.value = true;

    try {
        const res =
            await fetchComments(props.modelClass, props.modelId, cursor.value);

        if (res?.message && res?.message === 'restricted') {
            restricted.value = true;
            return;
        }

        const { comments: newComments, next_cursor } = res;


        if (newComments?.length) {
            comments.value.push(...newComments);
        }

        // обновляем курсор и флаг
        cursor.value = next_cursor;
        hasMore.value = next_cursor !== null;
    } catch (err) {
        console.error("Ошибка loadPage:", err);
    } finally {
        loadingMore.value = false;
    }
}

watch(() => modal?.value?.modalOpen, () => {
    if (!modal?.value?.modalOpen) {
        nextTick(async () => {
            cancelReply();
            cancelEdit();
        });
    }
});

async function handleSubmit() {
    if (editingComment.value) {
        // Edit existing
        if (!editingBody.value.trim()) return;

        try {
            const updated = await updateComment(editingComment.value.id, { body: editingBody.value });
            // Update local comment
            updateLocalComment(updated);
            cancelEdit();

        } catch (err) {
            if (err?.response?.status === 404 || err?.response?.status === 403) {
                toast.error("You can't edit this comment");
                removeLocalComment(editingComment.value.id);
            }
        }
    } else {
        // Create new
        if (!newComment.value.trim()) return;
        const created = await createComment(props.modelClass, props.modelId, {
            body: newComment.value,
            parent_id: replyingTo.value || null,
        });
        // Insert local
        insertLocalComment(created);
    }
    cancelReply();
    cancelEdit();
}

// Helpers to update local state
function insertLocalComment(comment) {
    if (comment.parent_id) {
        // Find parent
        const parent = findCommentById(comments.value, comment.parent_id);
        if (parent) {
            parent.replies = parent.replies || [];
            parent.replies.push(comment);
        }
    } else {
        comments.value.unshift({ ...comment, replies: [] });
    }
}

function updateLocalComment(updated) {
    const comment = findCommentById(comments.value, updated.id);
    if (comment) {
        Object.assign(comment, updated);
    }
}

function removeLocalComment(id) {
    function recurse(list) {
        const idx = list.findIndex(c => c.id === id);
        if (idx !== -1) return list.splice(idx, 1);
        for (const c of list) {
            if (c.replies) recurse(c.replies);
        }
    }
    recurse(comments.value);

    if (comments.value.length > 0) {
        const last = comments.value[comments.value.length - 1];
        cursor.value = last.id;
        hasMore.value = true;
    } else {
        cursor.value = null;
        hasMore.value = false;
    }
}

function findCommentById(list, id) {
    for (const c of list) {
        if (c.id === id) return c;
        if (c.replies) {
            const found = findCommentById(c.replies, id);
            if (found) return found;
        }
    }
    return null;
}

// Handlers
function startReply(id) {
    replyingTo.value = id;
    newComment.value = "";
    if (modal.value)
        modal.value.modalOpen = true;
    editingComment.value = null;
}

function startEdit(comment) {
    editingComment.value = comment;
    editingBody.value = comment.body;
    if (modal.value)
        modal.value.modalOpen = true;
    replyingTo.value = null;
}

function cancelReply() {
    replyingTo.value = null;
    newComment.value = "";
    if (modal.value)
        modal.value.modalOpen = false;
}

function cancelEdit() {
    editingComment.value = null;
    editingBody.value = "";
    if (modal.value)
        modal.value.modalOpen = false;
}

async function handleDelete(id) {
    commentId.value = id;
}

const destroy = async (id) => {
    try {
        commentId.value = null;
        await deleteComment(id);
    } catch (err) {
        if (err?.response?.status === 404 || err?.response?.status === 403) {
            toast.error("You can't delete this comment");
        }
    } finally {
        removeLocalComment(id);
    }
}

const canSend = computed(() => {
    return newComment.value.trim() || editingBody.value.trim();
});
</script>

<template>
    <x-confirmation v-if="commentId" @cancel="commentId = null" @confirm="destroy(commentId)"
        :message="__('Are you sure')" />
    <div class="relative w-full" id="comments_container" v-if="!restricted">
        <Modal v-if="global.user?.id" ref="modal" :title="replyingTo ? __('Reply to a comment') : __('Edit comment')">
            <div class="mx-2 my-2">
                <Textarea v-if="!editingComment" v-model="newComment" :max="512" :min="1"
                    :placeholder="__('Add a comment')" />
                <Textarea v-if="editingComment" :max="512" :min="1" v-model="editingBody"
                    :placeholder="__('Edit comment')" />
            </div>
            <div class="flex gap-2 justify-end mt-2">
                <x-button intent="secondary" @click="cancelReply(); cancelEdit()">
                    {{ __('Cancel') }}
                </x-button>
                <x-button @click="handleSubmit">
                    {{ editingComment ? __('Update') : __('Send') }}
                </x-button>
            </div>
        </Modal>
        <div :title="__('Comments')">
            <div class="px-2 pt-1 my-2 bg" v-if="!replyingTo && !editingComment && global.user?.id">
                <div>
                    <Textarea v-model="newComment" :max="512" :min="1" :placeholder="__('Add a comment')"
                        preventEnter />
                </div>
            </div>
            <x-button @click="handleSubmit" type="button" class="my-2  ml-auto mx-2" v-if="global.user?.id && canSend">
                {{ editingComment ? __('Update') : __('Send') }}
            </x-button>
            <CommentList :comments="comments" @reply="startReply" @edit="startEdit" @delete="handleDelete" :isOwner />
            <x-observe @intersect="loadPage" />
            <div class="text-center py-4 text-gray-500">
                <span v-if="loadingMore">
                </span>
                <BottomText :message="!hasMore && comments.length === 0 ? __('No comments') :
                    !hasMore ? __('No more comments') : __('Loading')
                    " />
            </div>
        </div>
    </div>
    <div v-if="restricted" class="text-center py-4 text-gray-500">
        {{ __('Comments are disabled') }}
    </div>
</template>
