import { inject } from "vue";

export const useTranslateApi = () => { 

    const route = inject("route");
     
    const translate = async (text, sourceLang, targetLang) => {
        try {
            const response = await axios.post(
                route("api.translate"),
                {
                    text: text,
                    source: sourceLang,
                    target: targetLang,
                }
            );
            return response.data;
        } catch (err) {
            console.error(err);
        }
    };

    return {
        translate,
    };
};
