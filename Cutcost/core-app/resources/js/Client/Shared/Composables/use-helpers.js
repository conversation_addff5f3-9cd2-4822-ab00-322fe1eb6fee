import { useGlobalStore } from "@s";
import { router } from "@inertiajs/vue3";
import { formatDistanceToNow } from 'date-fns'

export const useHelpers = () => {

  const global = useGlobalStore();

  const back = (url) => {
    const redirectBack = () => {
      const referrer = document.referrer;
      const currentDomain = window.location.hostname;

      if (referrer) {
        const referrerDomain = new URL(referrer).hostname;
        if (referrerDomain === currentDomain) {
          window.history.back();
        } else {
          window.location.href = "/";
        }
      } else if (global.prevUrl?.includes(global.appUrl)) {
        window.history.back();
      } else {
        window.location.href = "/";
      }
    };

    if (url) {
      router.get(url);
      return;
    }

    return redirectBack(); // reload param was redundant
  };

  const getIdsFromObject = (object) => {
    return Array.isArray(object) ? object.map((item) => item.id) : [];
  };

  const countryCodeToEmoji = (code) => {
    if (!code || typeof code !== "string") return "🌍";
    return code
      .toUpperCase()
      .replace(/./g, (char) =>
        String.fromCodePoint(char.charCodeAt(0) + 127397)
      );
  };

  const debounce = (func, timeout = 300) => {
    let timer;
    return function (...args) {
      const context = this;
      clearTimeout(timer);
      timer = setTimeout(() => func.apply(context, args), timeout);
    };
  }

  const isObject = (val) =>
    typeof val === "object" && val !== null && !Array.isArray(val);


  const diffForHumans = (date) => {

    if (date instanceof Date) return formatDistanceToNow(date, { addSuffix: true });
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  }

  return {
    isObject,
    debounce,
    back,
    getIdsFromObject,
    countryCodeToEmoji,
    diffForHumans,
  };
};
