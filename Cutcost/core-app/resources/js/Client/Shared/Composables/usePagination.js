import { useCache } from "../Stores/useCache";
import { computed, reactive, ref, } from "vue";
import { useHelpers } from './use-helpers';

export const usePagination = () => {

  const { get, set } = useCache();
  const { debounce } = useHelpers();

  const loading = ref();

  // when data loaded from cache 
  // we make request to refresh data and get status
  let gotFromCache = ref(false);

  const textNeeded = computed(() => {
    return (paginationData.status === 'noMore' || paginationData.status === 'nothing');
  });

  const bottomText = computed(() => {

    if (paginationData.status === 'noMore') {
      return 'Nothing more';
    } else if (paginationData.status === 'nothing') {
      return 'Nothing';
    } else {
      return 'Loading';
    }
  });


  const paginationData = reactive({
    data: [],
    cursor: null,
    // 'noMore', 'nothing'
    status: false,
  });

  const loadByKey = (key) => {
    const cached = get(key);
    if (cached) {
      paginationData.data = cached.data;
      paginationData.cursor = cached.cursor;
      gotFromCache.value = true;
    }
  }

  const saveByKey = (key) => {
    set(key, {
      data: [...paginationData.data],
      cursor: paginationData.cursor,
    });
  }

  const loadNext = debounce(async (url, params) => {

    loading.value = true;

    params.cursor = paginationData.cursor;

    await axios
      .get(
        url,
        { params },
      )
      .then((res) => {
        const items = res.data.items;

        paginationData.data.push(...items);

        // server gives us next cursor, if null, then no more data
        if (res.data.next_cursor === null) {
          paginationData.status = 'noMore';
        } else {
          paginationData.cursor = res.data.next_cursor;
        }

        console.log(res.data.next_cursor);

        paginationData.status = res.data.status;
      })
      .catch((e) => {
        console.error('Error paginating:', e);
      })
      .finally(() => {
        loading.value = false;
        if (params?.noCache) return;
        saveByKey(window.location.href + params?.type);
      });
  }, 500);

  return {
    bottomText,
    loadNext,
    paginationData,
    loading,
    loadByKey,
    textNeeded,
  };
};
