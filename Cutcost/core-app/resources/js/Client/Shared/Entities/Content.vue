<script setup>
import { computed, inject } from "vue";
import { router } from "@inertiajs/vue3";

const route = inject("route");

const props = defineProps({
  content: String,
});

const urlRegex = /(https?:\/\/[^\s]+)/g;
const mentionRegex = /@[\w]+/g;

// Helper: break long words into chunks
function breakWords(text, size) {
  if (!size || text.length <= size) return [text];
  const result = [];
  for (let i = 0; i < text.length; i += size) {
    result.push(text.slice(i, i + size));
  }
  return result;
}

const parsedContent = computed(() => {
  if (!props.content) return [];

  const parts = [];
  let lastIndex = 0;
  let match;

  // Разбираем ссылки
  while ((match = urlRegex.exec(props.content)) !== null) {
    const url = match[0];
    const index = match.index;

    if (index > lastIndex) {
      const textPart = props.content.slice(lastIndex, index);
      // Разбиваем на упоминания и обычный текст
      const subParts = splitMentions(textPart);
      parts.push(...subParts);
    }

    parts.push({ text: url, isLink: true });
    lastIndex = index + url.length;
  }

  if (lastIndex < props.content.length) {
    const textPart = props.content.slice(lastIndex);
    const subParts = splitMentions(textPart);
    parts.push(...subParts);
  }

  return parts;
});

// Разделяет текст на упоминания и обычный текст
function splitMentions(text) {
  const result = [];
  let lastIndex = 0;
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    const mention = match[0];
    const index = match.index;

    if (index > lastIndex) {
      result.push({ text: text.slice(lastIndex, index), isLink: false });
    }

    result.push({ text: mention, isLink: false, isMention: true });
    lastIndex = index + mention.length;
  }

  if (lastIndex < text.length) {
    result.push({ text: text.slice(lastIndex), isLink: false });
  }

  return result;
}

const goto = (link) => {
  if (
    link.includes("https://cutcost.net") ||
    link.includes("https://www.cutcost.net") ||
    link.includes("http://localhost:8000")
  ) {
    window.location.href = link;
  } else {
    router.get(route("redirect", { url: preg }));
  }
};

const goPerson = (nickname) => {
  router.get(route("profile", { user: nickname.replace("@", "") }));
}
</script>

<template>
  <div class="!select-text text break-words">
    <template v-for="(part, i) in parsedContent" :key="i">
      <span
        v-if="part.isLink"
        @click="goto(part.text)"
        rel="noopener noreferrer"
        :title="part.text"
        class="text-secondary underline cursor-pointer"
      >
        {{ part.text }}
      </span>
      <span
        v-else-if="part.isMention"
        @click="goPerson(part.text)"
        class="text-secondary font-bold cursor-pointer"
      >
        {{ part.text }}
      </span>
      <span v-else>{{ part.text }}</span>
    </template>
  </div>
</template>
