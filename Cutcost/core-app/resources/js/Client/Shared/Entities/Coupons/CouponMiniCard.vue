<script setup>
import { inject } from "vue";

const route = inject("route");

const props = defineProps({
  coupon: Object,
  blank: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <div class="bg-bg dark:bg-bg-dark flex items-center gap-3 rounded-lg p-3 shadow-md">
    <div class="flex flex-1 flex-col">
      <div class="text-text dark:text-dark-accent text-sm font-semibold">
        {{ coupon.name }}
      </div>

    </div>

    <x-button :as="blank ? 'a' : 'Link'" :target="blank ? '_blank' : '_self'"
      :href="route('coupon.view', { coupon: coupon.slug })">
      {{ __("Look") }}
    </x-button>
  </div>
</template>
