<script setup>
import { router } from "@inertiajs/vue3";
import { computed, inject } from "vue";
import { CouponDiscount } from "@m/Public";

const route = inject("route");

const props = defineProps({
  coupon: Object,
  query: String,
});

const visitAdvancedSearch = () => {
  if (!props?.query) {
    return router.get(
      route("coupon.view", { coupon: props.coupon.slug }),
    );
  }
  router.get(
    route("advanced.search", {
      type: "coupon",
      id: props?.coupon?.id,
      query: props?.query,
    }),
  );
};

const name = computed(() => {
  let n = props.coupon.name;
  if (global.isMobile) return n.slice(0, 18) + (n.length > 18 ? "…" : "");
  return n;
});

</script>

<template>
  <div class="group cursor-pointer rounded-2xl p-4 
           bg-white/80 dark:bg-white/10 backdrop-blur-md 
           shadow-md hover:shadow-xl duration-300 
            hover:ring-blue-400/50 dark:hover:ring-blue-300/40
           flex flex-col  text-center">
    <x-gallery :media="coupon.media" :h="30" />
    <div @click="visitAdvancedSearch">
      <span v-if="coupon?.label" class="absolute -top-2 -right-2 
                 font-bold px-2 py-0.5 rounded-full shadow-md">
        {{ coupon.label }}
      </span>

      <p class="mt-3 font-semibold text-sm sm:text-base text-gray-900 dark:text-gray-100 
      truncate w-full px-1">
        {{ name }}
      </p>

      <div class="mt-2">
        <CouponDiscount :coupon="coupon" mini :currency="coupon?.currency"
          class="text-gray-600 dark:text-gray-300 text-sm" />
      </div>
    </div>
  </div>
</template>
