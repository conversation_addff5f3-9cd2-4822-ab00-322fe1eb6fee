<script setup>
import { PhArrowLeft } from "@phosphor-icons/vue";
import { useHelpers } from "../Composables/use-helpers";

const { back } = useHelpers();

const props = defineProps({
    url: String,
})

const goBack = () => {
    back(props.url);
};

</script>

<template>
    <x-button intent="fade" class="flex !px-1 !border-0" @click="goBack" v-if="$page.component !== 'Discount/Feed'">
        <template #icon>
            <PhArrowLeft :size="20" />
        </template>
    </x-button>
</template>