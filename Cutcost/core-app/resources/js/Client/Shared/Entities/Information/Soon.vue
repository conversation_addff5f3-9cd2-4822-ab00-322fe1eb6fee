<script setup>
import { useHelpers } from "@s";

const helpers = useHelpers();

defineProps({
    title: String,
});
</script>
<template>
    <div class="bg-primary dark:bg-dark-surface w-full rounded-b-xl px-4 py-6 text-center sm:px-8 sm:py-10">
        <h1 class="text-3xl !font-extrabold h1 !tracking-wide !uppercase sm:text-5xl">
            {{ __("Feature in developing", { title }) }}
        </h1>
        <div class="my-10 mt-20 flex justify-center">
            <x-button @click="helpers.back()">
                {{ __("Back") }}
            </x-button>
        </div>
    </div>
</template>
