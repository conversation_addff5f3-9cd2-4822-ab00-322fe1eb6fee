<script setup>
import { computed, ref, onMounted } from "vue";
import { useGlobalStore, useCache } from "@s";
import { useTranslateApi } from "../Api/translate-api";
import { router } from "@inertiajs/vue3";
import { inject } from "vue";

const global = useGlobalStore();
const { get } = useCache();
const { translate } = useTranslateApi();
const route = inject("route");

const props = defineProps({
    model: { type: Object, required: true },
    field: { type: [String, Array], default: "content" },
});

const delimiter = "%$^#";

const dontTranslate = computed(() => {
    if (!global.user?.preferences?.translate_to) {
        if (global.user) {
            global.user.preferences ??= {};
            global.user.preferences.translate_to = get("temporary_translate_to");
        }
        return false;
    }

    if (props.model.base_locale === global.currentLocale || !props.model.base_locale) return true;

    return (
        global.user.preferences.dont_translate.includes(props.model.base_locale) ||
        global.user.preferences.translate_to === props.model.base_locale
    );
});

const fields = computed(() => (Array.isArray(props.field) ? props.field : [props.field]));

const originals = {};
const originalContent = ref("");
const translatedContent = ref("");
const showingTranslation = ref(false);

const init = () => {
    fields.value.forEach((f) => {
        originals[f] = props.model?.[f] ?? "";
    });

    if (fields.value.length === 1) {
        originalContent.value = props.model?.[fields.value[0]] ?? "";
    } else {
        originalContent.value = fields.value.map((f) => props.model?.[f] ?? "").join(delimiter);
    }
};

onMounted(init);

const tryTranslate = async () => {
    if (!global.auth()) return;
    if (dontTranslate.value) return;

    if (!global.user?.preferences?.translate_to) {
        router.get(route("seller.settings", { tab: "lang-and-region" }));
        return;
    }

    if (translatedContent.value && !showingTranslation.value) {
        applyTranslation(translatedContent.value);
        showingTranslation.value = true;
        return;
    }

    try {
        if (fields.value.length === 1) {
            originalContent.value = props.model?.[fields.value[0]] ?? "";
        } else {
            originalContent.value = fields.value.map((f) => props.model?.[f] ?? "").join(delimiter);
        }

        const result = await translate(
            originalContent.value,
            props.model.base_locale,
            global.user.preferences.translate_to
        );

        translatedContent.value = result;
        applyTranslation(result);
        showingTranslation.value = true;
    } catch (e) {
        console.error("Translation failed:", e);
    }
};

const applyTranslation = (text) => {
    if (fields.value.length === 1) {
        props.model[fields.value[0]] = text;
    } else {
        const parts = text.split(delimiter);
        fields.value.forEach((f, idx) => {
            props.model[f] = parts[idx] ?? "";
        });
    }
};

const showOriginal = () => {
    fields.value.forEach((f) => {
        props.model[f] = originals[f] ?? "";
    });
    showingTranslation.value = false;
};
</script>

<template>
    <div v-if="!dontTranslate && global.checkAuth()" class="flex justify-end gap-2">
        <x-button intent="link" @click="tryTranslate" v-if="!showingTranslation">
            {{ __("Translate") }}
        </x-button>
        <x-button intent="link" @click="showOriginal" v-else>
            {{ __("Show original") }}
        </x-button>
    </div>
</template>
