<script setup>
import { FriendControls } from "@m/Seller";
import { router } from "@inertiajs/vue3";
import { computed } from "vue";
import { inject } from "vue";
import { useGlobalStore } from "@s";

const route = inject("route");

const props = defineProps({
  user: {
    type: Object,
    required: true,
  },
  mini: {
    type: Boolean,
    default: false,
  },
  noControls: {
    type: Boolean,
    default: false,
  },
});
const global = useGlobalStore();

const visitProfile = () => {
  router.get(route("profile", { user: props.user.nickname }));
};
const isFriend = false;

const isSingleWord = computed(() => {
  return !/\s/.test(props.user.display_name);
});
</script>

<template>
  <x-button class="w-full !px-2 !rounded-none" @click="visitProfile">
    <div class="flex items-center gap-3 w-full">
      <!-- аватар -->
      <div class="flex-none">
        <x-model-media :url="user.avatar" class="!h-12 !w-12" />
      </div>

      <!-- текст -->
      <div class="flex flex-col flex-1 min-w-0">
        <h3 :class="[
          'text-base sm:text-lg font-semibold text-text dark:text-dark-text leading-snug',
          isSingleWord ? 'truncate' : 'break-words whitespace-normal'
        ]">
          {{ global.isMobile ? user.display_name.slice(0, 32) : user.display_name }}
        </h3>
        <span v-if="isFriend" class="text-sm text-accent dark:text-dark-accent">
          {{ __("Friend") }}
        </span>
      </div>

      <!-- кнопки -->
      <div class="flex-none">
        <div v-if="$slots.actions">
          <slot name="actions" />
        </div>
        <div v-else-if="!noControls" @click.stop>
          <FriendControls :user="user" :status="user?.friend_status" />
        </div>
      </div>
    </div>
  </x-button>
</template>
