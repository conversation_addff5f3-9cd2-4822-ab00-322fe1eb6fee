<script setup>
import { useProfileStore } from "@m/Seller";
import { computed } from "vue";
import { router } from "@inertiajs/vue3";
import { inject } from "vue";

const route = inject("route");

const props = defineProps({
  user: Object,
  query: String,
});

const profile = useProfileStore();

const isFriend = computed(() => {
  return profile?.socials?.friends?.some(
    (friend) => props?.user?.id === friend.id,
  );
});

const visitAdvancedSearch = () => {
  router.get(
    route("advanced.search", {
      type: "user",
      id: props?.user?.id,
      query: props?.query,
    }),
  );
};
</script>
<template>
  <div @click="visitAdvancedSearch"
    class="bg-surface dark:bg-bg-dark hover:dark:bg-bg-dark/60 hover:bg-surface/60 flex w-full cursor-pointer rounded-2xl p-2">
    <x-model-media :image="user.avatar" class="!h-10 !w-10" />
    <div class="text-primary ml-2 flex w-full items-center justify-between text-xl font-bold">
      <div>{{ user.display_name }}</div>
      <div class="max-sm:text-sm sm:mr-2">
        {{ isFriend ? __("friend") : "" }}
      </div>
    </div>
  </div>
</template>
