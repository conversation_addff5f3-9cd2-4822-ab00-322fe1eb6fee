<script setup>
import { useHelpers } from "@s";

const helpers = useHelpers();

defineProps({
  message: String,
  long: <PERSON><PERSON><PERSON>,
  back: <PERSON><PERSON><PERSON>,
});
</script>
<template>
  <div class="flex w-full my-4 flex-col justify-center" :class="{ 'my-20': long }">
    <h1 class="h1 flex !text-muted mx-auto text-center dark:!text-dark-muted">
      {{ message }}
    </h1>
    <div v-if="back" class="mt-5 mx-auto">
      <x-button @click="helpers.back()">
        {{ __("Back") }}
      </x-button>
    </div>
  </div>
</template>
