<script setup>
import { useHelpers } from "@s";
import { getCurrentInstance, onMounted, ref, watch, inject } from "vue";
import CouponMiniSearchResult from "../Entities/Coupons/CouponMiniSearchResult.vue";

const route = inject("route");
const { debounce } = useHelpers();

const props = defineProps({
    search: String,
    type: String,
});
const { proxy } = getCurrentInstance();

const emits = defineEmits(["status"]);

const coupons = ref({});

onMounted(async () => {
    watch(
        () => props.search,
        (newSearch) => {
            couponsSearch(newSearch);
        },
    );

    watch(
        () => props.type,
        () => {
            couponsSearch(props.search);
        },
    );
});

const couponsSearch = debounce((query) => {
    if (props.type !== "coupons") return;
    if (props.search.length > 1)
        axios
            .post(route("search.coupons"), {
                search: query,
            })
            .then(function (response) {
                coupons.value = response.data.coupons;
                if (coupons.value.length === 0) {
                    emits(
                        "status",
                        proxy.__("Nothing found"),
                    );
                } else {
                    emits("status", null);
                }
            })
            .catch(function (error) {
                console.error(error);
            });
}, 500);
</script>
<template>
    <div v-if="props.search.length > 1" class="grid grid-cols-2 gap-2">
        <CouponMiniSearchResult v-for="coupon in coupons" :key="coupon.id" :coupon="coupon" :query="search" />
    </div>
</template>
