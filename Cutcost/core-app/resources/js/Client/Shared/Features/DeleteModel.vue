<script setup>
import { ref, inject } from "vue";
import { useHelpers } from "@s";

const { back } = useHelpers();

const props = defineProps({
    model: {
        type: Object,
        required: true,
    },
    modelName: {
        type: String,
        required: true,
    },
    url: {
        type: [String, Function],
        required: true,
    },
    redirect: {
        type: Boolean,
        default: false,
    },
    dropdown: {
        type: Boolean,
        default: false,
    },
});

const route = inject("route");
const showConfirmation = ref(false);

const askDelete = () => {
    showConfirmation.value = true;
};

const deleteModel = () => {
    const deleteUrl =
        typeof props.url === "function"
            ? props.url(props.model)
            : props.url;

    axios.delete(deleteUrl).then(() => {
        if (props.redirect) {
            back();
        } else {
            const event = new CustomEvent(`${props.modelName}-deleted`.toLowerCase(), {
                detail: { id: props.model.id },
            });
            document.dispatchEvent(event);
        }
    });

    showConfirmation.value = false;
};

const cancelDelete = () => {
    showConfirmation.value = false;
};
</script>

<template>
    <div>
        <template v-if="!dropdown">
            <x-button intent="danger" @click="askDelete">
                {{ __('Delete') }}
            </x-button>
        </template>
        <template v-else>
            <DropdownItem @click="askDelete">
                {{ __('Delete') }}
            </DropdownItem>
        </template>

        <x-confirmation v-if="showConfirmation" :message="__('Are you sure')" :for="model.id" @confirm="deleteModel"
            @cancel="cancelDelete" />
    </div>
</template>
