<script setup>
import { onMounted } from 'vue';
import L from 'leaflet';
import { positionIcon } from "@m/Public";

const props = defineProps({
    lnglat: {
        type: Object,
        required: false,
        default: null,
    },
    address: String,
});

const emit = defineEmits(['update:lnglat', 'update:address']);

let map = null;
let marker = null;

// Reverse geocode using OpenStreetMap Nominatim API
async function getAddress(lat, lng) {
    try {
        const res = await fetch(
            `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${lat}&lon=${lng}`
        );
        const data = await res.json();
        return data.display_name || '';
    } catch (err) {
        console.error(err);
        return '';
    }
}

onMounted(() => {

    // LAT | LNG in leaflet
    const defaultCenter = props.lnglat?.lat && props.lnglat?.lng
        ? [props.lnglat.lat, props.lnglat.lng]
        : localStorage.getItem('map.center')
            ? JSON.parse(localStorage.getItem('map.center'))
            : [46.9631, 8.9402];

    map = L.map('form-map', {
        center: defaultCenter,
        zoom: 6,
        zoomControl: true
    });

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

  const attributionControl = document.querySelector('.leaflet-control-attribution');
    if (attributionControl) {
        attributionControl.innerHTML = '&copy; <a href="https://www.openstreetmap.org/copyright">OSM</a> contributors';
    }

    // Place initial marker if lnglat provided
    if (props.lnglat && props.lnglat.lat && props.lnglat.lng) {
        marker = L.marker([props.lnglat.lat, props.lnglat.lng], {
            icon: positionIcon('var(--color-secondary)')
        }).addTo(map);
    }

    // Handle map click
    map.on('click', async (e) => {
        const { lat, lng } = e.latlng;
        console.log(lat, lng);

        // Remove previous marker
        if (marker) map.removeLayer(marker);

        // Add new marker
        marker = L.marker([lat, lng], {
            icon: positionIcon('var(--color-secondary)')
        }).addTo(map);

        // Emit coordinates
        emit('update:lnglat', { lng, lat });

        // Reverse geocode to get address
        const address = await getAddress(lat, lng);
        emit('update:address', address);
    });

});
</script>

<template>
    <div id="form-map" class="h-100 w-full"></div>
</template>
