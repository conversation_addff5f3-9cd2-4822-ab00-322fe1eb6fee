<script setup>
import { ref } from 'vue';
import { Ph<PERSON>erson, PhTarget } from '@phosphor-icons/vue';
import NavigationButton from '../../Modules/Fixed/Entities/NavigationButton.vue';

const emit = defineEmits(['geolocate', 'geolocate-error']);

const geoLoading = ref(false);

defineProps({
    button: {
        type: Boolean,
        default: false,
    },
})

const geolocate = () => {
    if (!navigator.geolocation) {
        emit('geolocate-error', { message: 'Geolocation not supported' });
        return;
    }
    geoLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (pos) => {
            geoLoading.value = false;
            localStorage.setItem('map.center', JSON.stringify([pos.coords.latitude, pos.coords.longitude]))
            emit('geolocate', { lat: pos.coords.latitude, lng: pos.coords.longitude })
        },
        (err) => {
            geoLoading.value = false;
            emit('geolocate-error', { message: err.message })
        },
        { enableHighAccuracy: true, timeout: 10000 }
    );
}
</script>

<template>
    <component :is="!button ? NavigationButton : 'x-button'" intent="secondary" @click="geolocate" :disabled="geoLoading">
        <template #icon v-if="!$slots.default">
            <div v-if="!geoLoading">
                <PhPerson :size="20" />
            </div>
            <div class="animate-spin" v-if="geoLoading">
                <PhTarget :size="20" />
            </div>
        </template>
        <slot />
    </component>
</template>