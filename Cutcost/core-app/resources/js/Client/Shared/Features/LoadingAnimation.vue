<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useGlobalStore } from '@s';

// Access global store
const global = useGlobalStore();

// Reactive state for loading
const isLoading = ref(true);

// Simulate loading state (replace with actual logic, e.g., router events)
onMounted(() => {
    // Simulate loading for 3 seconds
    setTimeout(() => {
        isLoading.value = false;
    }, 3000);
});

// Clean up on component unmount
onUnmounted(() => {
    isLoading.value = false;
});
</script>

<template>
    <div v-if="isLoading" class="loader-overlay">
        <svg class="loader" viewBox="0 0 100 100" aria-label="Loading">
            <circle cx="50" cy="50" r="40" stroke="var(--color-primary, #3498db)" stroke-width="8" fill="none"
                stroke-dasharray="188.4" stroke-dashoffset="0" />
            <circle cx="50" cy="50" r="30" stroke="var(--color-secondary, #1abc9c)" stroke-width="6" fill="none"
                stroke-dasharray="141.3" stroke-dashoffset="0" />
        </svg>
    </div>
</template>

<style scoped>
.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    pointer-events: none;
}

.loader {
    width: 80px;
    height: 80px;
    animation: spin 1.5s linear infinite;
}

.loader circle:first-child {
    animation: glow 2s ease-in-out infinite, dash 2s linear infinite;
}

.loader circle:last-child {
    animation: spin-reverse 1s linear infinite, glow-secondary 2s ease-in-out infinite;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes spin-reverse {
    100% {
        transform: rotate(-360deg);
    }
}

@keyframes dash {
    0% {
        stroke-dashoffset: 188.4;
    }

    50% {
        stroke-dashoffset: 0;
    }

    100% {
        stroke-dashoffset: -188.4;
    }
}

@keyframes glow {

    0%,
    100% {
        stroke: var(--color-primary, #3498db);
        filter: drop-shadow(0 0 5px var(--color-primary, #3498db));
    }

    50% {
        stroke: var(--color-accent, #e74c3c);
        filter: drop-shadow(0 0 10px var(--color-accent, #e74c3c));
    }
}

@keyframes glow-secondary {

    0%,
    100% {
        stroke: var(--color-secondary, #1abc9c);
        filter: drop-shadow(0 0 5px var(--color-secondary, #1abc9c));
    }

    50% {
        stroke: var(--color-accent, #e74c3c);
        filter: drop-shadow(0 0 8px var(--color-accent, #e74c3c));
    }
}

/* Accessibility: Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {

    .loader,
    .loader circle {
        animation: none;
    }

    .loader circle {
        stroke-dashoffset: 0;
        filter: none;
    }
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .loader {
        width: 60px;
        height: 60px;
    }
}
</style>