<script setup>
import { usePagination } from "@s";
import { inject, onMounted, ref, computed } from "vue";

const route = inject("route");

const url = route('load.paginated');
const { paginationData, loadNext, textNeeded } = usePagination();
const searchQuery = ref('');

const props = defineProps({
  filters: Object,
  type: String,
  required: <PERSON>olean,
})

const model = defineModel(); 
const ids = ref(null);
const loading = ref(false);

// ---------------------------
// Загрузка данных
// ---------------------------
const load = async () => {
  await loadNext(url, {
    filters: { search_query: searchQuery.value, ids: ids.value, country_id: props?.country_id ?? null },
    noCache: true, type: props.type
  });
};

// Подгрузка при скролле
const intersect = async () => {
  if (textNeeded.value || searchQuery.value) return;
  await load();
};

// ---------------------------
// Поиск: обновляем searchQuery
// ---------------------------
const search = (query) => {
  searchQuery.value = query ? String(query).toLowerCase() : '';
  paginationData.data = [];
  paginationData.cursor = null;
  console.log('search', searchQuery.value);
  load();
};

// ---------------------------
// Данные для селекта: выбранные сверху, затем результаты поиска, затем остальные
// ---------------------------
const sortedData = computed(() => {
  const data = paginationData.data ?? [];
  // нормализованные выбранные id в виде строк
  const selectedIds = (model.value ? (Array.isArray(model.value) ? model.value : [model.value]) : []).map(String);

  // выбранные элементы (те, которые уже есть в data)
  const selectedItems = data.filter(item => selectedIds.includes(String(item.id)));

  // строка поиска (у тебя уже приводится к lower при установке)
  const q = searchQuery.value ? String(searchQuery.value).toLowerCase() : '';

  // остальные элементы, не выбранные
  const others = data.filter(item => !selectedIds.includes(String(item.id)));

  // совпадающие с поиском из остальных — вверх после selected
  const matching = q ? others.filter(item => String(item.name || '').toLowerCase().includes(q)) : others;

  // оставшиеся (не выбранные и не совпали с поиском)
  const nonMatching = q ? others.filter(item => !String(item.name || '').toLowerCase().includes(q)) : [];

  // порядок: selected -> matching -> nonMatching
  return [...selectedItems, ...matching, ...nonMatching];
});


// ---------------------------
// Инициализация
// ---------------------------
onMounted(async () => {
  if (model.value) {
    loading.value = true;
    ids.value = Array.isArray(model.value) ? model.value : [model.value];
    await load();
    loading.value = false;
    ids.value = null;
  }
});
</script>

<template>
  <div>
    <x-select
      v-bind="$attrs"
      v-if="!loading"
      @intersect="intersect"
      v-model="model"
      @search="search"
      observe
      search
      :required
      :data="sortedData"
    >
      <template #default="{ item }">
        <slot :item="item"/>
      </template>
    </x-select>

    <div v-else class="bg-reverse p-5.5 rounded-xl animate-pulse">
      <!-- Skeleton Loading -->
    </div>
  </div>
</template>
