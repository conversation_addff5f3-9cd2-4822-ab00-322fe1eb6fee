<script setup>
import { computed } from "vue";
import { Header, Aside, BroadcastNotifications, BottomBar } from "@m/Fixed";
import { useGlobalStore } from "@s";
import { router } from "@inertiajs/vue3";
import CookieConsent from "../Widgets/CookieConsent.vue";
import { LoginModal } from "@m/Fixed";
import { useHeaderStore } from "@m/Fixed";

const global = useGlobalStore();
const header = useHeaderStore();

router.on('before', (event) => {
  event.detail.preserveScroll = true
})

const isMobileAndInChat = computed(() => {
  return global.isMobile && global.inChat;
});

const isNavigation = computed(() => {
  return header.navigation;
});

const isTitle = computed(() => {
  return header.title;
});

const styles = computed(() => {

  if (global.inChat && global.isMobile) {
    return 'pt-0';
  }

  if (isNavigation.value && isTitle.value) {
    return 'pt-27';
  }

  if (isTitle.value || isNavigation.value) {
    return 'pt-17';
  }

  return 'pt-7';
}
);

const inMap = computed(() => {
  return global.page.component === 'Discount/ShowLocations' && header.chosenTab === 'map';
});

</script>

<template>
  <div class="flex overflow-x-hidden"  >
    <LoginModal />
    <Header v-show="!global.inChat || !global.isMobile" />
    <div class="mx-auto flex max-[1300px]:w-full">
      <div id="global-ripple-container"
        style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 9999;">
      </div>

      <Aside />
      <Toast />

      <main class="h-full pb-12 select-none w-full shrink-0 pl-62 max-md:w-full max-sm:pl-0 lg:w-300"
        :class="styles, { '!pb-0': inMap || global.inChat }">
        <BroadcastNotifications />
        <CookieConsent />
        <slot />
      </main>
    </div>
    <BottomBar v-show="global.isMobile && !global.inChat" />
  </div>
</template>

<style>
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-secondary);
  border-radius: 10px;
}

* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-secondary) transparent;
}

.scroll-hidden {
  overflow: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
}
</style>
