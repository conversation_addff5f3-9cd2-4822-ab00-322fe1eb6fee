<script setup>
import { inject, ref } from 'vue';
import { router } from "@inertiajs/vue3";
import { useHelpers } from "@s";

const { back } = useHelpers();

const route = inject("route");
const redirectUrl = ref(new URLSearchParams(window.location.search).get('url'));

function manualRedirect() {
    if (redirectUrl.value) {
        window.open(redirectUrl.value, '_blank');
    } else {
        router.get(route("feed"));
    }
}
</script>

<template>
    <Head :title="__('Redirect')" />
    <div class=" !flex mt-8">
        <div class=" !mx-auto bg-surface dark:bg-dark-surface border border-border dark:border-dark-border 
             shadow-sm rounded-xl p-6 w-full max-w-md text-center">
            <h1 class="text-2xl font-bold text-primary dark:text-dark-primary mb-4">
                {{ __("Attention") }}
            </h1>

            <p class="text-text dark:text-dark-text mb-3">
                {{ __("You are leaving") }}
            </p>

            <p class="text-sm text-muted dark:text-dark-muted break-words mb-6">
                {{ redirectUrl || '404' }}
            </p>
            <div class="flex gap-2 justify-center">
                <x-button @click="manualRedirect" class="!bg-secondary" :disabled="!redirectUrl">
                    {{ __("Follow link") }}
                </x-button>

                <x-button @click="back">
                    {{ __("Back") }}
                </x-button>
            </div>

            <p class="text-xs text-muted dark:text-dark-muted mt-4">
                {{ __("Not sure") }}
            </p>
        </div>
    </div>
</template>
