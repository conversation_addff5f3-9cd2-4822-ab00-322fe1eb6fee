<script setup>
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { ref } from "vue";
import { useGlobalStore } from "@s";
import {
    PhBug,
    PhCreditCard,
    PhUser,
    PhLightbulb,
    PhQuestion,
    PhArrowRight,
    PhChatCircle,
    PhEnvelope,
    PhPhone,
    PhCheckCircle,
    PhArrowLeft
} from "@phosphor-icons/vue";

const props = defineProps({
    categories: Array,
});

const { __ } = useGlobalStore();

const selectedCategory = ref(null);

const iconMap = {
    'bug': PhBug,
    'credit-card': PhCreditCard,
    'user': PhUser,
    'lightbulb': PhLightbulb,
    'question': PhQuestion,
};

const getIcon = (iconName) => {
    return iconMap[iconName] || PhQuestion;
};

const selectCategory = (category) => {
    selectedCategory.value = category;
};

const goBack = () => {
    selectedCategory.value = null;
};
</script>

<template>
    <Head>
        <title>{{ __("Support Center") }} - CutCost</title>
    </Head>

    <div class="card-position">
        <Card :title="__('Support Center')" :url="selectedCategory ? null : route('help.index')">
            <!-- Back Button -->
            <div v-if="selectedCategory" class="mb-6">
                <button
                    @click="goBack"
                    class="flex items-center text-text-secondary dark:text-dark-text-secondary hover:text-primary transition-colors"
                >
                    <PhArrowLeft :size="20" class="mr-2" />
                    {{ __("Back to categories") }}
                </button>
            </div>

            <!-- Category Selection -->
            <div v-if="!selectedCategory">
                <!-- Header -->
                <div class="text-center mb-12">
                    <h1 class="text-3xl font-bold text-text dark:text-dark-text mb-4">
                        {{ __("How can we help you?") }}
                    </h1>
                    <p class="text-lg text-text-secondary dark:text-dark-text-secondary max-w-2xl mx-auto">
                        {{ __("Select the category that best describes your issue to get the right support.") }}
                    </p>
                </div>

                <!-- Categories Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                    <div
                        v-for="category in categories"
                        :key="category.id"
                        @click="selectCategory(category)"
                        class="bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border p-6 hover:shadow-lg hover:border-primary dark:hover:border-primary transition-all cursor-pointer group"
                    >
                        <div class="flex flex-col items-center text-center">
                            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                                <component :is="getIcon(category.icon)" :size="32" class="text-primary" />
                            </div>
                            
                            <h3 class="font-semibold text-text dark:text-dark-text mb-2 group-hover:text-primary transition-colors">
                                {{ __(category.title) }}
                            </h3>
                            
                            <p class="text-sm text-text-secondary dark:text-dark-text-secondary mb-4 line-clamp-2">
                                {{ __(category.description) }}
                            </p>
                            
                            <div class="flex items-center text-primary group-hover:translate-x-1 transition-transform">
                                <span class="text-sm font-medium">{{ __("Select") }}</span>
                                <PhArrowRight :size="16" class="ml-1" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alternative Support Options -->
                <div class="bg-gray-50 dark:bg-dark-card/50 rounded-xl p-8">
                    <h2 class="text-xl font-bold text-text dark:text-dark-text mb-6 text-center">
                        {{ __("Other ways to get help") }}
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Link
                            :href="route('help.index')"
                            class="flex flex-col items-center p-6 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary dark:hover:border-primary transition-all group"
                        >
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
                                <PhQuestion :size="24" class="text-primary" />
                            </div>
                            <h3 class="font-semibold text-text dark:text-dark-text mb-1">{{ __("Help Center") }}</h3>
                            <p class="text-sm text-text-secondary dark:text-dark-text-secondary text-center">{{ __("Browse articles and FAQs") }}</p>
                        </Link>

                        <Link
                            :href="route('help.faq')"
                            class="flex flex-col items-center p-6 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary dark:hover:border-primary transition-all group"
                        >
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
                                <PhChatCircle :size="24" class="text-primary" />
                            </div>
                            <h3 class="font-semibold text-text dark:text-dark-text mb-1">{{ __("FAQ") }}</h3>
                            <p class="text-sm text-text-secondary dark:text-dark-text-secondary text-center">{{ __("Quick answers to common questions") }}</p>
                        </Link>

                        <a
                            href="mailto:<EMAIL>"
                            class="flex flex-col items-center p-6 bg-white dark:bg-dark-card rounded-lg border border-border dark:border-dark-border hover:border-primary dark:hover:border-primary transition-all group"
                        >
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
                                <PhEnvelope :size="24" class="text-primary" />
                            </div>
                            <h3 class="font-semibold text-text dark:text-dark-text mb-1">{{ __("Email Us") }}</h3>
                            <p class="text-sm text-text-secondary dark:text-dark-text-secondary text-center">{{ __("Send us a direct message") }}</p>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Selected Category Details -->
            <div v-else>
                <div class="max-w-2xl mx-auto">
                    <!-- Category Header -->
                    <div class="text-center mb-8">
                        <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <component :is="getIcon(selectedCategory.icon)" :size="40" class="text-primary" />
                        </div>
                        
                        <h2 class="text-2xl font-bold text-text dark:text-dark-text mb-2">
                            {{ __(selectedCategory.title) }}
                        </h2>
                        
                        <p class="text-text-secondary dark:text-dark-text-secondary">
                            {{ __(selectedCategory.description) }}
                        </p>
                    </div>

                    <!-- Examples -->
                    <div class="bg-white dark:bg-dark-card rounded-xl border border-border dark:border-dark-border p-6 mb-8">
                        <h3 class="font-semibold text-text dark:text-dark-text mb-4">
                            {{ __("Common issues in this category:") }}
                        </h3>
                        
                        <ul class="space-y-2">
                            <li
                                v-for="example in selectedCategory.examples"
                                :key="example"
                                class="flex items-center text-text-secondary dark:text-dark-text-secondary"
                            >
                                <div class="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></div>
                                {{ __(example) }}
                            </li>
                        </ul>
                    </div>

                    <!-- Coming Soon Notice -->
                    <div class="bg-primary/5 border border-primary/20 rounded-xl p-8 text-center">
                        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <PhCheckCircle :size="32" class="text-primary" />
                        </div>
                        
                        <h3 class="text-xl font-semibold text-text dark:text-dark-text mb-2">
                            {{ __("Ticket System Coming Soon") }}
                        </h3>
                        
                        <p class="text-text-secondary dark:text-dark-text-secondary mb-6">
                            {{ __("We're working on a comprehensive ticket system. For now, please use one of the alternative support options below.") }}
                        </p>

                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link
                                :href="route('help.index')"
                                class="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                            >
                                <PhQuestion :size="20" class="mr-2" />
                                {{ __("Browse Help Articles") }}
                            </Link>
                            
                            <a
                                href="mailto:<EMAIL>"
                                class="inline-flex items-center justify-center px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary/5 transition-colors"
                            >
                                <PhEnvelope :size="20" class="mr-2" />
                                {{ __("Email Support") }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    </div>
</template>
