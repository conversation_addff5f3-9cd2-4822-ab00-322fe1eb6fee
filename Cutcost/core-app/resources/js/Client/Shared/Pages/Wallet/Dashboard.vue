<script setup>
import { Head, <PERSON>, router } from "@inertiajs/vue3";
import { ref, computed } from "vue";
import { useGlobalStore } from "@s";
import { Card, Stats } from "@s/UI";
import { 
    PhWallet,
    PhCoins,
    PhCreditCard,
    PhTrendUp,
    PhPlus,
    PhArrowRight,
    PhShieldCheck,
    PhCrown,
    PhCalendar,
    PhReceipt
} from "@phosphor-icons/vue";

const global = useGlobalStore();

const props = defineProps({
    wallet_overview: Object,
    transaction_history: Array,
    wallet_statistics: Object,
    payment_methods: Array,
    available_plans: Array,
});

// Computed properties
const recentTransactions = computed(() => {
    return props.transaction_history?.slice(0, 5) || [];
});

const hasPaymentMethods = computed(() => {
    return props.payment_methods?.length > 0;
});

const needsVerification = computed(() => {
    return !props.wallet_overview?.is_payment_verified;
});

// Methods
const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getTransactionIcon = (type) => {
    switch (type) {
        case 'deposit': return PhTrendUp;
        case 'purchase': return PhCoins;
        case 'referral': return PhCrown;
        default: return PhReceipt;
    }
};

const getTransactionColor = (type) => {
    switch (type) {
        case 'deposit': return 'text-green-600';
        case 'purchase': return 'text-blue-600';
        case 'referral': return 'text-purple-600';
        case 'withdrawal': return 'text-red-600';
        default: return 'text-gray-600';
    }
};
</script>

<template>
    <Head>
        <title>{{ __("Wallet") }}</title>
    </Head>

    <div class="card-position">
        <!-- Header -->
        <section class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                        <PhWallet :size="28" />
                        {{ __('My Wallet') }}
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        {{ __('Manage your cuts and payment methods') }}
                    </p>
                </div>
                
                <div class="flex gap-3">
                    <Link 
                        :href="route('wallet.plans')"
                        class="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                    >
                        <PhPlus :size="16" />
                        {{ __('Buy Cuts') }}
                    </Link>
                </div>
            </div>
        </section>

        <!-- Verification Alert -->
        <section v-if="needsVerification" class="mb-6">
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <PhShieldCheck :size="24" class="text-yellow-600" />
                        <div>
                            <h3 class="font-semibold text-yellow-800 dark:text-yellow-200">
                                {{ __('Account Verification Required') }}
                            </h3>
                            <p class="text-sm text-yellow-700 dark:text-yellow-300">
                                {{ __('Verify your account with a €2 payment to unlock all features') }}
                            </p>
                        </div>
                    </div>
                    <Link 
                        :href="route('wallet.verification')"
                        class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition text-sm"
                    >
                        {{ __('Verify Now') }}
                    </Link>
                </div>
            </div>
        </section>

        <!-- Balance Overview -->
        <section class="mb-8">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <Stats :title="__('Wallet Balance')" :value="wallet_overview?.formatted_wallet_balance || '€0.00'">
                    <PhWallet :size="24" class="text-blue-500" />
                </Stats>
                <Stats :title="__('Cuts Balance')" :value="wallet_overview?.formatted_cuts_balance || '0 cuts'">
                    <PhCoins :size="24" class="text-yellow-500" />
                </Stats>
                <Stats :title="__('Total Spent')" :value="'€' + (wallet_overview?.total_spent || 0).toFixed(2)">
                    <PhTrendUp :size="24" class="text-red-500" />
                </Stats>
                <Stats :title="__('Total Earned')" :value="'€' + (wallet_overview?.total_earned || 0).toFixed(2)">
                    <PhTrendUp :size="24" class="text-green-500" />
                </Stats>
            </div>
        </section>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Recent Transactions -->
            <div class="lg:col-span-2">
                <Card :title="__('Recent Transactions')">
                    <template #actions>
                        <Link 
                            :href="route('wallet.transactions')"
                            class="text-blue-500 hover:text-blue-600 text-sm flex items-center gap-1"
                        >
                            {{ __('View All') }}
                            <PhArrowRight :size="16" />
                        </Link>
                    </template>
                    
                    <div class="p-4">
                        <div v-if="recentTransactions.length > 0" class="space-y-3">
                            <div 
                                v-for="transaction in recentTransactions" 
                                :key="transaction.id"
                                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                            >
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                        <component 
                                            :is="getTransactionIcon(transaction.type)" 
                                            :size="20" 
                                            :class="getTransactionColor(transaction.type)"
                                        />
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ transaction.description }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ formatDate(transaction.created_at) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div v-if="transaction.amount" class="font-semibold" :class="getTransactionColor(transaction.type)">
                                        {{ transaction.formatted_amount }}
                                    </div>
                                    <div v-if="transaction.cuts_amount" class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ transaction.formatted_cuts }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
                            <PhReceipt :size="48" class="mx-auto mb-3 opacity-50" />
                            <p>{{ __('No transactions yet') }}</p>
                        </div>
                    </div>
                </Card>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-6">
                <!-- Active Subscription -->
                <Card v-if="wallet_overview?.active_subscription" :title="__('Active Subscription')">
                    <div class="p-4">
                        <div class="text-center">
                            <PhCrown :size="32" class="text-yellow-500 mx-auto mb-2" />
                            <h3 class="font-semibold text-gray-900 dark:text-gray-100">
                                {{ wallet_overview.active_subscription.plan?.name?.en || 'Premium Plan' }}
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                {{ wallet_overview.active_subscription.status_display }}
                            </p>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>{{ __('Amount') }}:</span>
                                    <span class="font-semibold">{{ wallet_overview.active_subscription.formatted_amount }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>{{ __('Cuts Received') }}:</span>
                                    <span class="font-semibold">{{ wallet_overview.active_subscription.cuts_received }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>

                <!-- Payment Methods -->
                <Card :title="__('Payment Methods')">
                    <template #actions>
                        <button class="text-blue-500 hover:text-blue-600 text-sm">
                            {{ __('Manage') }}
                        </button>
                    </template>
                    
                    <div class="p-4">
                        <div v-if="hasPaymentMethods" class="space-y-2">
                            <div 
                                v-for="method in payment_methods.slice(0, 2)" 
                                :key="method.id"
                                class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
                            >
                                <div class="flex items-center gap-2">
                                    <PhCreditCard :size="16" />
                                    <span class="text-sm">{{ method.display_name }}</span>
                                </div>
                                <span v-if="method.is_default" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    {{ __('Default') }}
                                </span>
                            </div>
                        </div>
                        <div v-else class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <PhCreditCard :size="32" class="mx-auto mb-2 opacity-50" />
                            <p class="text-sm">{{ __('No payment methods') }}</p>
                        </div>
                    </div>
                </Card>

                <!-- Quick Stats -->
                <Card :title="__('Statistics')">
                    <div class="p-4 space-y-3">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('Total Transactions') }}</span>
                            <span class="font-semibold">{{ wallet_statistics?.total_transactions || 0 }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('Cuts Earned') }}</span>
                            <span class="font-semibold">{{ wallet_statistics?.total_cuts_earned || 0 }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('Avg Transaction') }}</span>
                            <span class="font-semibold">€{{ (wallet_statistics?.avg_transaction_amount || 0).toFixed(2) }}</span>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    </div>
</template>
