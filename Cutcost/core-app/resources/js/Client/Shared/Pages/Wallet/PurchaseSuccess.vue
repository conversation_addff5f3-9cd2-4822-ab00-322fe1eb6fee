<script setup>
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { 
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>oi<PERSON>,
    PhArrowRight,
    PhReceipt,
    PhTrendUp
} from "@phosphor-icons/vue";

const props = defineProps({
    message: String,
    purchase_details: Object,
});
</script>

<template>
    <Head>
        <title>{{ __("Purchase Successful") }}</title>
    </Head>

    <div class="card-position">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Success Icon -->
            <div class="mb-8">
                <div class="w-24 h-24 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <PhCheck :size="48" class="text-green-500" />
                </div>
                
                <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                    {{ __('Purchase Successful!') }}
                </h1>
                
                <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
                    {{ message || __('Your cuts purchase was successful! The cuts have been added to your account.') }}
                </p>
            </div>

            <!-- Purchase Details -->
            <div v-if="purchase_details" class="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">
                    {{ __('Purchase Details') }}
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4">
                        <div class="flex items-center gap-2 mb-2">
                            <PhCoins :size="20" class="text-green-500" />
                            <span class="font-medium text-gray-900 dark:text-gray-100">
                                {{ __('Cuts Received') }}
                            </span>
                        </div>
                        <p class="text-2xl font-bold text-green-600">
                            {{ purchase_details.cuts_amount || '0' }} {{ __('cuts') }}
                        </p>
                        <p v-if="purchase_details.bonus_cuts" class="text-sm text-green-500">
                            +{{ purchase_details.bonus_cuts }} {{ __('bonus cuts') }}
                        </p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4">
                        <div class="flex items-center gap-2 mb-2">
                            <PhTrendUp :size="20" class="text-blue-500" />
                            <span class="font-medium text-gray-900 dark:text-gray-100">
                                {{ __('Amount Paid') }}
                            </span>
                        </div>
                        <p class="text-2xl font-bold text-blue-600">
                            €{{ parseFloat(purchase_details.amount || 0).toFixed(2) }}
                        </p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ purchase_details.payment_method || __('Card payment') }}
                        </p>
                    </div>
                </div>
                
                <div v-if="purchase_details.reference" class="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <PhReceipt :size="16" />
                        <span>{{ __('Reference') }}: {{ purchase_details.reference }}</span>
                    </div>
                </div>
            </div>

            <!-- What's Next -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4">
                    {{ __('What\'s Next?') }}
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="text-center">
                        <PhCoins :size="32" class="text-blue-500 mx-auto mb-2" />
                        <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-1">
                            {{ __('Use Your Cuts') }}
                        </h3>
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            {{ __('Start using your cuts for discounts and offers') }}
                        </p>
                    </div>
                    
                    <div class="text-center">
                        <PhReceipt :size="32" class="text-blue-500 mx-auto mb-2" />
                        <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-1">
                            {{ __('Track Spending') }}
                        </h3>
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            {{ __('Monitor your cuts usage in transaction history') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                    :href="route('wallet.index')"
                    class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition font-semibold"
                >
                    {{ __('Go to Wallet') }}
                    <PhArrowRight :size="16" />
                </Link>
                
                <Link 
                    :href="route('wallet.transactions')"
                    class="inline-flex items-center justify-center gap-2 px-6 py-3 border border-green-500 text-green-500 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition font-semibold"
                >
                    <PhReceipt :size="16" />
                    {{ __('View Transactions') }}
                </Link>
            </div>
        </div>
    </div>
</template>
