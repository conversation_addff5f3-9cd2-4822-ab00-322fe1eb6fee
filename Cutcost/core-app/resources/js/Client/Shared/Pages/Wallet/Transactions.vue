<script setup>
import { <PERSON>, <PERSON>, router } from "@inertiajs/vue3";
import { ref, computed, watch } from "vue";
import { useGlobalStore } from "@s";
import { 
    PhReceipt,
    PhArrowLeft,
    PhFunnel,
    PhDownload,
    PhMagnifyingGlass,
    PhCalendar,
    PhCoins,
    PhTrendUp,
    PhTrendDown,
    PhCrown,
    PhCreditCard,
    PhCheck,
    PhX,
    PhClock
} from "@phosphor-icons/vue";

const global = useGlobalStore();

const props = defineProps({
    transactions: Array,
    filters: Object,
    transaction_types: Array,
    transaction_statuses: Array,
    wallet_overview: Object,
});

// State
const showFilters = ref(false);
const localFilters = ref({
    type: props.filters?.type || '',
    status: props.filters?.status || '',
    date_from: props.filters?.date_from || '',
    date_to: props.filters?.date_to || '',
    search: props.filters?.search || '',
});

// Computed properties
const filteredTransactions = computed(() => {
    return props.transactions || [];
});

const totalTransactions = computed(() => {
    return filteredTransactions.value.length;
});

const hasFilters = computed(() => {
    return Object.values(localFilters.value).some(value => value !== '');
});

// Methods
const applyFilters = () => {
    const params = {};
    
    Object.keys(localFilters.value).forEach(key => {
        if (localFilters.value[key]) {
            params[key] = localFilters.value[key];
        }
    });

    router.get(route('wallet.transactions'), params, {
        preserveState: true,
        preserveScroll: true,
    });
};

const clearFilters = () => {
    localFilters.value = {
        type: '',
        status: '',
        date_from: '',
        date_to: '',
        search: '',
    };
    applyFilters();
};

const exportTransactions = () => {
    // Implementation for exporting transactions
    global.addToast('info', 'Export feature coming soon');
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getTransactionIcon = (type) => {
    switch (type) {
        case 'deposit': return PhTrendUp;
        case 'withdrawal': return PhTrendDown;
        case 'purchase': return PhCoins;
        case 'refund': return PhTrendUp;
        case 'bonus': return PhCoins;
        case 'referral': return PhCrown;
        default: return PhReceipt;
    }
};

const getTransactionColor = (type) => {
    switch (type) {
        case 'deposit': return 'text-green-600';
        case 'withdrawal': return 'text-red-600';
        case 'purchase': return 'text-blue-600';
        case 'refund': return 'text-green-600';
        case 'bonus': return 'text-purple-600';
        case 'referral': return 'text-yellow-600';
        default: return 'text-gray-600';
    }
};

const getStatusIcon = (status) => {
    switch (status) {
        case 'completed': return PhCheck;
        case 'failed': return PhX;
        case 'cancelled': return PhX;
        default: return PhClock;
    }
};

const getStatusColor = (status) => {
    switch (status) {
        case 'completed': return 'text-green-600 bg-green-100';
        case 'failed': return 'text-red-600 bg-red-100';
        case 'cancelled': return 'text-gray-600 bg-gray-100';
        case 'pending': return 'text-yellow-600 bg-yellow-100';
        case 'processing': return 'text-blue-600 bg-blue-100';
        default: return 'text-gray-600 bg-gray-100';
    }
};

// Watch for filter changes
watch(localFilters, () => {
    // Auto-apply filters after a delay
    clearTimeout(window.filterTimeout);
    window.filterTimeout = setTimeout(() => {
        if (localFilters.value.search !== props.filters?.search) {
            applyFilters();
        }
    }, 500);
}, { deep: true });
</script>

<template>
    <Head>
        <title>{{ __("Transaction History") }}</title>
    </Head>

    <div class="card-position">
        <!-- Header -->
        <section class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <Link 
                            :href="route('wallet.index')"
                            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                            <PhArrowLeft :size="20" />
                        </Link>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                            <PhReceipt :size="28" />
                            {{ __('Transaction History') }}
                        </h1>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">
                        {{ __('View and manage your wallet transaction history') }}
                    </p>
                </div>
                
                <div class="flex gap-3">
                    <button 
                        @click="showFilters = !showFilters"
                        class="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition"
                        :class="{ 'bg-blue-50 border-blue-300 text-blue-700': hasFilters }"
                    >
                        <PhFunnel :size="16" />
                        {{ __('Filters') }}
                    </button>
                    <button 
                        @click="exportTransactions"
                        class="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                    >
                        <PhDownload :size="16" />
                        {{ __('Export') }}
                    </button>
                </div>
            </div>
        </section>

        <!-- Filters -->
        <section v-if="showFilters" class="mb-6">
            <Card :title="__('Filter Transactions')">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <!-- Search -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Search') }}
                            </label>
                            <div class="relative">
                                <PhMagnifyingGlass :size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input 
                                    type="text" 
                                    v-model="localFilters.search"
                                    :placeholder="__('Search transactions...')"
                                    class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                                />
                            </div>
                        </div>

                        <!-- Type Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Type') }}
                            </label>
                            <select 
                                v-model="localFilters.type"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            >
                                <option value="">{{ __('All Types') }}</option>
                                <option v-for="type in transaction_types" :key="type.value" :value="type.value">
                                    {{ type.label }}
                                </option>
                            </select>
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Status') }}
                            </label>
                            <select 
                                v-model="localFilters.status"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            >
                                <option value="">{{ __('All Statuses') }}</option>
                                <option v-for="status in transaction_statuses" :key="status.value" :value="status.value">
                                    {{ status.label }}
                                </option>
                            </select>
                        </div>

                        <!-- Date Range -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Date Range') }}
                            </label>
                            <div class="flex gap-2">
                                <input 
                                    type="date" 
                                    v-model="localFilters.date_from"
                                    class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                                />
                                <input 
                                    type="date" 
                                    v-model="localFilters.date_to"
                                    class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                                />
                            </div>
                        </div>
                    </div>

                    <div class="flex gap-3">
                        <button 
                            @click="applyFilters"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                        >
                            {{ __('Apply Filters') }}
                        </button>
                        <button 
                            @click="clearFilters"
                            v-if="hasFilters"
                            class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition"
                        >
                            {{ __('Clear Filters') }}
                        </button>
                    </div>
                </div>
            </Card>
        </section>

        <!-- Summary Stats -->
        <section class="mb-8">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <Stats :title="__('Total Transactions')" :value="totalTransactions.toString()">
                    <PhReceipt :size="24" class="text-blue-500" />
                </Stats>
                <Stats :title="__('Current Balance')" :value="wallet_overview?.formatted_wallet_balance || '€0.00'">
                    <PhCreditCard :size="24" class="text-green-500" />
                </Stats>
                <Stats :title="__('Cuts Balance')" :value="wallet_overview?.formatted_cuts_balance || '0 cuts'">
                    <PhCoins :size="24" class="text-yellow-500" />
                </Stats>
                <Stats :title="__('Total Spent')" :value="'€' + (wallet_overview?.total_spent || 0).toFixed(2)">
                    <PhTrendDown :size="24" class="text-red-500" />
                </Stats>
            </div>
        </section>

        <!-- Transactions List -->
        <section>
            <Card :title="__('Transactions')">
                <div class="p-6">
                    <div v-if="filteredTransactions.length > 0" class="space-y-3">
                        <div 
                            v-for="transaction in filteredTransactions" 
                            :key="transaction.id"
                            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition"
                        >
                            <div class="flex items-center gap-4">
                                <!-- Icon -->
                                <div class="w-12 h-12 bg-white dark:bg-gray-700 rounded-full flex items-center justify-center shadow-sm">
                                    <component 
                                        :is="getTransactionIcon(transaction.type)" 
                                        :size="20" 
                                        :class="getTransactionColor(transaction.type)"
                                    />
                                </div>

                                <!-- Details -->
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ transaction.description }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ formatDate(transaction.created_at) }}
                                    </div>
                                    <div class="text-xs text-gray-400 dark:text-gray-500">
                                        {{ __('Ref') }}: {{ transaction.reference }}
                                    </div>
                                </div>
                            </div>

                            <div class="text-right">
                                <!-- Amount -->
                                <div class="flex flex-col items-end gap-1">
                                    <div v-if="transaction.amount" 
                                         class="font-semibold" 
                                         :class="getTransactionColor(transaction.type)">
                                        {{ transaction.formatted_amount }}
                                    </div>
                                    <div v-if="transaction.cuts_amount" 
                                         class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ transaction.formatted_cuts }}
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="mt-2">
                                    <span 
                                        class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                                        :class="getStatusColor(transaction.status)"
                                    >
                                        <component :is="getStatusIcon(transaction.status)" :size="12" />
                                        {{ transaction.status_display || transaction.status }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-else class="text-center py-12">
                        <PhReceipt :size="64" class="mx-auto mb-4 text-gray-400" />
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            {{ hasFilters ? __('No Matching Transactions') : __('No Transactions Yet') }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            {{ hasFilters 
                                ? __('Try adjusting your filters to see more results') 
                                : __('Your transaction history will appear here once you make your first transaction') 
                            }}
                        </p>
                        <div class="flex gap-3 justify-center">
                            <button 
                                v-if="hasFilters"
                                @click="clearFilters"
                                class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition"
                            >
                                {{ __('Clear Filters') }}
                            </button>
                            <Link 
                                v-else
                                :href="route('wallet.plans')"
                                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                            >
                                {{ __('Buy Cuts') }}
                            </Link>
                        </div>
                    </div>
                </div>
            </Card>
        </section>
    </div>
</template>
