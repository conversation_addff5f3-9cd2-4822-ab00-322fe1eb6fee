<script setup>
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { 
    <PERSON><PERSON><PERSON>,
    Ph<PERSON>rrowLeft,
    PhR<PERSON><PERSON>,
    PhShieldCheck
} from "@phosphor-icons/vue";

const props = defineProps({
    message: String,
});

const refreshPage = () => {
    window.location.reload();
};
</script>

<template>
    <Head>
        <title>{{ __("Verification Pending") }}</title>
    </Head>

    <div class="card-position">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Pending Icon -->
            <div class="mb-8">
                <div class="w-24 h-24 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <PhClock :size="48" class="text-blue-500" />
                </div>
                
                <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                    {{ __('Verification in Progress') }}
                </h1>
                
                <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
                    {{ message || __('Your verification payment is being processed. This usually takes a few minutes.') }}
                </p>
            </div>

            <!-- Status Info -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 mb-8">
                <div class="flex items-center justify-center gap-3 mb-4">
                    <PhShieldCheck :size="24" class="text-blue-500" />
                    <h2 class="text-xl font-semibold text-blue-800 dark:text-blue-200">
                        {{ __('What Happens Next?') }}
                    </h2>
                </div>
                
                <div class="space-y-3 text-left max-w-md mx-auto">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                            1
                        </div>
                        <div>
                            <p class="font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Payment Processing') }}
                            </p>
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                {{ __('Your payment is being verified by our payment processor') }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                            2
                        </div>
                        <div>
                            <p class="font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Account Verification') }}
                            </p>
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                {{ __('Once confirmed, your account will be automatically verified') }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                            3
                        </div>
                        <div>
                            <p class="font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Features Unlocked') }}
                            </p>
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                {{ __('All premium features will be available immediately') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button 
                    @click="refreshPage"
                    class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition font-semibold"
                >
                    <PhRefresh :size="16" />
                    {{ __('Check Status') }}
                </button>
                
                <Link 
                    :href="route('wallet.index')"
                    class="inline-flex items-center justify-center gap-2 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition font-semibold"
                >
                    <PhArrowLeft :size="16" />
                    {{ __('Back to Wallet') }}
                </Link>
            </div>

            <!-- Help Text -->
            <div class="mt-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ __('If your verification takes longer than expected, please contact our support team.') }}
                </p>
            </div>
        </div>
    </div>
</template>
