<script setup>
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>row<PERSON>
} from "@phosphor-icons/vue";

const props = defineProps({
    message: String,
});
</script>

<template>
    <Head>
        <title>{{ __("Verification Successful") }}</title>
    </Head>

    <div class="card-position">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Success Icon -->
            <div class="mb-8">
                <div class="w-24 h-24 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <PhShieldCheck :size="48" class="text-green-500" />
                </div>
                
                <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                    {{ __('Account Verified Successfully!') }}
                </h1>
                
                <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
                    {{ message || __('Your account has been successfully verified. You can now access all features.') }}
                </p>
            </div>

            <!-- Benefits -->
            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">
                    {{ __('What\'s Unlocked') }}
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <PhCoins :size="32" class="text-green-500 mx-auto mb-2" />
                        <h3 class="font-medium text-green-800 dark:text-green-200 mb-1">
                            {{ __('Unlimited Cuts') }}
                        </h3>
                        <p class="text-sm text-green-700 dark:text-green-300">
                            {{ __('Purchase any amount of cuts') }}
                        </p>
                    </div>
                    
                    <div class="text-center">
                        <PhCrown :size="32" class="text-green-500 mx-auto mb-2" />
                        <h3 class="font-medium text-green-800 dark:text-green-200 mb-1">
                            {{ __('Premium Features') }}
                        </h3>
                        <p class="text-sm text-green-700 dark:text-green-300">
                            {{ __('Access to all premium features') }}
                        </p>
                    </div>
                    
                    <div class="text-center">
                        <PhCheck :size="32" class="text-green-500 mx-auto mb-2" />
                        <h3 class="font-medium text-green-800 dark:text-green-200 mb-1">
                            {{ __('Secure Payments') }}
                        </h3>
                        <p class="text-sm text-green-700 dark:text-green-300">
                            {{ __('Enhanced payment security') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                    :href="route('wallet.index')"
                    class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition font-semibold"
                >
                    {{ __('Go to Wallet') }}
                    <PhArrowRight :size="16" />
                </Link>
                
                <Link 
                    :href="route('wallet.plans')"
                    class="inline-flex items-center justify-center gap-2 px-6 py-3 border border-green-500 text-green-500 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition font-semibold"
                >
                    <PhCoins :size="16" />
                    {{ __('Buy Cuts') }}
                </Link>
            </div>
        </div>
    </div>
</template>
