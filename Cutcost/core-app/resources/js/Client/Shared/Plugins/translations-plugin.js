export const useTranslation = {
  __: function (key, replacements = {}) {
    const getNestedTranslation = (obj, path) => {
      return path
        ?.split(".")
        ?.reduce((o, i) => (o && o[i] !== undefined ? o[i] : key), obj);
    };

    let translation = getNestedTranslation(
      window._translations,
      key?.toLowerCase(),
    );

    Object.keys(replacements).forEach((r) => {
      translation = translation.replace(`:${r}`, replacements[r]);
    });

    return translation;
  },

  __has: function (key) {
    const getNestedTranslation = (obj, path) => {
      return path
        ?.split(".")
        ?.reduce((o, i) => (o && o[i] !== undefined ? o[i] : undefined), obj);
    };

    return getNestedTranslation(window._translations, key) !== undefined;
  },
  translate: function (object) {
    return useGlobalStore().translate(object);
  },
};

export default {
  install(app) {
    app.config.globalProperties.__ = useTranslation.__;
    app.config.globalProperties.__has = useTranslation.__has;
  },
};
