import { defineStore } from "pinia";
import { reactive, shallowRef, watch } from "vue";

export const useDialogStore = defineStore("dialog", () => {
    const show = shallowRef(false);
    const types = ["success", "info", "warning", "error"];

    const status = reactive({
        id: null,
        isAccept: null,
    });

    const dialog = reactive({
        intent: null,
        message: null,
        id: null,
        confirmation: null,
        persistent: null,
    });

    watch(show, (val) => {
        if (val) {
            document.body.classList.add("!overflow-hidden");
        } else {
            document.body.classList.remove("!overflow-hidden");
        }
    });

    const warning = (id, message, persistent = false, confirmation = true) => {
        send("warning", message, id, confirmation, persistent);
    };

    const info = (id, message, persistent = false, confirmation = true) => {
        send("info", message, id, confirmation, persistent);
    };

    const success = (id, message, persistent = false, confirmation = true) => {
        send("success", message, id, confirmation, persistent);
    };

    const error = (id, message, persistent = false, confirmation = true) => {
        send("error", message, id, confirmation, persistent);
    };

    const send = (
        intentV,
        messageV = "Are you sure?",
        idV,
        confirmationV = true,
        persistentV,
    ) => {
        if (!types.includes(intentV)) {
            throw new Error("Dialog intent not found");
        }

        if (intentV && messageV) {
            dialog.id = idV;
            dialog.confirmation = confirmationV ?? false;
            dialog.persistent = persistentV ?? false;
            dialog.message = messageV;
            dialog.intent = intentV;
            show.value = true;
        }
    };

    //remove dialog
    const rmd = () => {
        show.value = false;
        dialog.message = null;
        dialog.intent = null;
        dialog.confirmation = null;
        dialog.persistent = null;
        dialog.id = null;
    };

    const accept = () => {
        status.id = dialog.id;
        status.isAccept = true;
        rmd();
    };

    const cancel = () => {
        status.id = dialog.id;
        status.isAccept = false;
        rmd();
    };

    return {
        show,
        accept,
        cancel,
        status,
        rmd,
        warning,
        dialog,
        success,
        error,
        info,
    };
});
