import { usePage } from "@inertiajs/vue3";
import { defineStore } from "pinia";
import { computed, ref, reactive } from "vue";

export const useGlobalStore = defineStore("global", () => {
  const langs = ref([
    { id: 1, locale: "en", name: "English" },
    { id: 2, locale: "lt", name: "Lietuvos" },
    { id: 3, locale: "ru", name: "Русский" },
    { id: 4, locale: "pl", name: "<PERSON><PERSON>" },
    { id: 5, locale: "de", name: "<PERSON><PERSON><PERSON>" },
    { id: 6, locale: "es", name: "<PERSON>spaño<PERSON>" },
  ]);

  const consent = ref(JSON.parse(localStorage.getItem("consent")) ?? false);
  const isAppleDevice = /iPhone|iPad|iPod|Macintosh/.test(navigator.userAgent);

  const isMobile = computed(() => window.innerWidth < 415);

  const page = usePage();
  const user = computed(() => page.props.auth.user);
  const notificationsCount = computed(() => page.props.notifications_count);
  const userRoles = computed(() => page.props.auth.roles);
  const banned = computed(() => page.props.banned);

  const isPartner = computed(() => userRoles.value.includes("partner"));

  const assets = import.meta.env.VITE_APP_URL + "/storage/";
  const appUrl = import.meta.env.VITE_APP_URL + "/";
  const isProd = import.meta.env.VITE_APP_ENV === "production";
  const appName = "CutCost";

  const currentCity = computed(() => page.props.city);
  const currentLocale = ref(document.documentElement.lang ?? "en");
  const currentCountry = computed(() => page.props.country);

  const prevUrl = ref(null);
  const isReload = ref(false);

  const inMessenger = computed(() => {
    return page.url.includes("/cuttalk");
  });

  const inChat = computed(() => {
    return page.url.includes("/cuttalk/c");
  });

  const checkAuth = () => {
    if (user.value?.id) return true;
    else return false;
  }

  const auth = () => {
    if (checkAuth()) {
      return true;
    } else {
      console.trace('auth() called from:');
      document.dispatchEvent(new Event("login-modal-open"));
      return false;
    }
  };

  const setLocale = computed({
    get: () => currentLocale.value,
    set: (newLocale) => {
      currentLocale.value = newLocale;
    },
  });

  const isMyProfile = computed(() => {
    const nickname = () => page.url?.split("?")?.[1]?.split("=")?.[1];
    return (nickname() === user.value?.nickname || !nickname()) && page.component === 'Seller/Profile';
  });

  return {
    isMyProfile,
    notificationsCount,
    currentCountry,
    appName,
    isProd,
    inMessenger,
    inChat,
    appUrl,
    consent,
    langs,
    isPartner,
    userRoles,
    banned,
    page,
    currentCity,
    isAppleDevice,
    isReload,
    isMobile,
    currentLocale,
    assets,
    setLocale,
    user,
    checkAuth,
    auth,
    prevUrl
  };
});
