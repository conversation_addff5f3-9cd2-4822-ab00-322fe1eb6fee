import { defineStore } from "pinia";
import { ref } from "vue";

export const useToastStore = defineStore("toasts", () => {
    //example
    // toast.add({
    //     message:
    //         "You get friend request from: " +
    //         notification.request_from,
    //     type: "info",
    //     key: Symbol,
    // });

    const messages = ref([]);

    function remove(index) {
        messages.value.splice(index, 1);
    }

    function add(message) {
        messages.value.unshift(message);
    }

    const raw = (message, type) => {
        add({
            message: message,
            type: type,
            key: Symbol(),
        });
    };

    const info = (message) => {
        add({
            message: message,
            type: "info",
            key: Symbol(),
        });
    };

    const warning = (message) => {
        add({
            message: message,
            type: "warning",
            key: Symbol(),
        });
    };

    const error = (message) => {
        add({
            message: message,
            type: "error",
            key: Symbol(),
        });
    };

    const success = (message) => {
        add({
            message: message,
            type: "success",
            key: Symbol(),
        });
    };

    return { messages, remove, add, info, success, error, warning, raw };
});
