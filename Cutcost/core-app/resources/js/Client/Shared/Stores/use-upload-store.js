import { defineStore } from "pinia";
import { ref } from "vue";
import { inject } from "vue";

export const useUploadStore = defineStore("upload", () => {
  const route = inject("route");
  const mediaList = ref([]);
  const tempUuid = ref(null);
  const errors = ref([]);
  const deletedIds = ref([]);
  const loading = ref(false);

  const addMedia = (newMedia) => {
    const existing = mediaList.value;
    const map = new Map(existing.map((item) => [item.id, item]));
    newMedia.forEach((item) => map.set(item.id, item));
    mediaList.value = Array.from(map.values());
  };

  const replaceMedia = (newMedia) => {
    mediaList.value = newMedia;
  };

  const destroyMedia = async (ids) => {
    try {
      const { data } = await axios.delete(route("media.destroy"), {
        data: { ids: ids },
      });
      if (data.success) {
        mediaList.value = mediaList.value.filter(
          (item) => !ids.includes(item.id),
        );
        deletedIds.value.push(...ids);
      }
    } catch (e) {
      console.error(e);
      alert("Ошибка удаления");
    }
  };

  const saveSortOrder = async () => {
    const orderedIds = mediaList.value.map((i) => i.id);
    await axios.post(route("media.sort"), {
      temp_uuid: tempUuid.value,
      ordered_ids: orderedIds,
    });
  };

  return {
    mediaList,
    tempUuid,
    errors,
    deletedIds,
    loading,
    addMedia,
    replaceMedia,
    destroyMedia,
    saveSortOrder,
  };
});
