<script setup>
defineProps({
    current: Boolean,
    as: {
        type: String,
        default: "Link",
    },
    badge: {
        type: [<PERSON><PERSON><PERSON>, Number],
        default: false,
    },
});
</script>

<template>
    <x-button :as="as" v-bind="$attrs"
        class="[-webkit-user-drag:none] !mb-1 dark:!bg-dark-surface !bg-bg !border-none hover:!bg-black/5 dark:hover:!bg-white/5 !justify-start w-full sm:!text-lg max-sm:!rounded-none"
        :class="{ '!text-secondary': current }">
        <div class="flex items-center w-full">
            <span class="relative flex-shrink-0 mr-2 sm:[&_svg]:!h-7 sm:[&_svg]:!w-7" v-if="$slots.icon">
                <slot name="icon" />
                <span v-if="badge"
                    class="absolute -top-1 -right-1 flex h-4 min-w-[16px] items-center justify-center rounded-full bg-red-500 text-[10px] font-bold text-white px-[2px]">
                    {{ typeof badge === 'number' ? badge : '!' }}
                </span>
            </span>

            <span class="text-start max-sm:!text-xl">
                <slot />
            </span>
        </div>
    </x-button>
</template>
