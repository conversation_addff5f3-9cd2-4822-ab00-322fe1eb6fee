<script setup>
import { PhSpinner } from "@phosphor-icons/vue";
import { computed, ref, useTemplateRef, inject } from "vue";
import { useTippy } from "vue-tippy";
import { router } from "@inertiajs/vue3";

const route = inject("route", () => { });

defineOptions({
  name: "XButton",
});

const props = defineProps({
  loading: Boolean,
  href: {
    type: String,
    required: false,
  },
  as: {
    type: String,
    default: "button",
    validator(value) {
      return ["button", "a", "Link", "div"].includes(value);
    },
  },
  method: {
    type: String,
    default: "get",
    validator(value) {
      return ["get", "post"].includes(value);
    },
  },
  tippy: String,
  active: Boolean,
  current: Boolean,
  intent: {
    type: String,
    default: "primary",
    validator(value) {
      return [
        "success",
        "danger",
        "fade",
        "warning",
        "primary",
        "secondary",
        "rounded",
        "link",
      ].includes(value);
    },
  },
});

if (props.tippy) {
  const button = useTemplateRef(props.tippy);
  useTippy(button, {
    content: props.tippy,
    placement: "bottom",
  });
}

const classIntent = computed(() => {
  const base = `
    rounded-xl
    border border-border 
    dark:border-dark-border
    font-semibold
    focus:outline-none
    disabled:opacity-50
    disabled:pointer-events-none
  `;

  const intentClasses = {
    primary: `
      ${base}
      bg-surface
      dark:bg-bg-dark
      text-text
      hover:bg-border/20
      active:bg-dark-surface/20
      dark:text-dark-text
      dark:hover:bg-dark-border/90
      dark:active:bg-dark-border/20
    `,
    secondary: `
  ${base}
  text-text
  bg-surface border border-border
  hover:bg-[#f0f0f2] hover:text-primary
  active:bg-[#e6e6e6]
  
  dark:bg-dark-surface
  dark:text-dark-text
  dark:border-dark-border
  dark:hover:bg-[#3a3a40]
  dark:hover:text-dark-primary
  dark:active:bg-[#2a2a2e]
`,

    success: `
    ${base}
    bg-success text-black
    hover:bg-dark-success/80
    active:bg-success
    dark:bg-dark-success
    dark:hover:bg-dark-success/80
    dark:active:bg-dark-success
  `,
    danger: `
      ${base}
       text-surface
       !bg-dark-error
      hover:bg-dark-error
      active:bg-error
      dark:bg-dark-error
      dark:hover:bg-dark-error
      dark:active:bg-dark-error
    `,
    warning: `
      ${base}
      bg-warning text-black
      hover:#e6be20
      active:#c7a818
      dark:bg-dark-warning
      dark:hover:#f0d966
      dark:active:#d1b500
    `,
    fade: `
      ${base}
      bg-transparent text-text
      hover:bg-bg
      active:bg-border
      dark:text-dark-text
      dark:hover:bg-dark-surface
      dark:active:bg-dark-border
    `,
    rounded: `
      ${base}
      bg-surface/90 backdrop-blur-sm text-text
      !rounded-full
      border border-border
      !p-2
      hover:bg-bg
      active:bg-border
      dark:bg-dark-surface/90
      dark:text-dark-text
      dark:border-dark-border
      dark:hover:bg-dark-border
      dark:active:bg-dark-surface
    `,
    link: `
      
      bg-transparent text-primary underline
      hover:text-secondary
      active:text-accent
      focus:outline-none
      dark:text-dark-primary
      dark:hover:text-dark-secondary
      dark:active:text-dark-accent
    `,
  };

  return intentClasses[props.intent] || intentClasses.primary;
});


const classActive = computed(() => {
  return props.active ? " !text-secondary" : "";
});

// Ripple logic для зажатия
const ripples = ref([]);
let rippleTimeout = null;

function createRipple(event) {
  if (props.loading) return;

  const button = event.currentTarget;
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;

  rippleTimeout = setTimeout(() => {
    ripples.value = [{
      id: "active-ripple",
      style: {
        width: size + "px",
        height: size + "px",
        top: y + "px",
        left: x + "px",
      },
    }];
  }, 100);
}

function removeRipple() {
  clearTimeout(rippleTimeout);
  ripples.value = [];
}

const goto = (force = false) => {
  const href = props.href;
  const current = props.current;

  if (!href) return;

  if (href.startsWith("https") && !(href.includes("https://cutcost.net") ||
    href.includes("https://www.cutcost.net") ||
    href.includes("http://localhost:8000"))) {
    router.get(route("redirect", { url: href }));
    return;
  }


  const isSameUrl = href === window.location.href;
  const openInNewTab = (props.as === 'a' || force) && !current;

  console.log(openInNewTab);

  console.log('isSameUrl', isSameUrl, force);

  if (current) {
    window.open(href, '_self');
    return;
  }

  if (isSameUrl && !force) return;

  if (openInNewTab) {
    window.open(href, '_blank');
    return;
  }

  if (props?.method === 'post') {
    router.post(href);
  } else {
    router.get(href);
  }
};

</script>

<template>
  <button @click="(e) => goto(e.ctrlKey)" v-bind="$attrs" :ref="tippy" type="button"
    :class="[classIntent, classActive], { '!items-start !justify-start !block  text-left !p-0': intent === 'link' }"
    class="relative max-sm:!text-sm overflow-hidden flex items-center justify-center gap-2 py-2 px-6 text-base font-medium select-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed "
    @mousedown="createRipple" @mouseup="removeRipple" @mouseleave="removeRipple" @touchstart="createRipple"
    @touchend="removeRipple">
    <PhSpinner v-if="loading" class="h-6 w-6 animate-spin text-current" />
    <slot name="icon" v-if="!loading" />
    <slot v-if="!loading" />
    <!-- Ripple elements -->
    <span v-for="ripple in ripples" :key="ripple.id" class="ripple bg-black/20" :style="ripple.style"></span>
  </button>
</template>

<style scoped>
.ripple {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  user-select: none;
  animation: ripple-grow 600ms linear forwards;
  transform-origin: center;
  transform: scale(0);
}

@keyframes ripple-grow {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
</style>
