<script setup>
import GoBack from "../Entities/GoBack.vue";
import { useGlobalStore } from "@s";

const global = useGlobalStore();

const props = defineProps({
  title: {
    type: String,
    required: false,
  },
  noStyle: Boolean,
  url: {
    type: String,
    required: false,
  },
  back: {
    type: Boolean,
    default: false,
  }
});


</script>
<template>
  <div class="sm:rounded-xl"
    :class="noStyle ? 'px-2' : 'bg-bg dark:bg-dark-surface border-border border-1 pb-2 shadow backdrop-blur-2xl dark:border-0 dark:shadow-none', { 'pt-2': !title }">
    <div v-if="title || $slots.title || $slots.titleLeft" :class="noStyle ? '' : 'px-4 pt-1'">
      <div class="mt-2 flex flex-row justify-between" :class="noStyle ? '!mt-0' : ''">
        <div class="flex items-center gap-2">
          <slot name="titleLeft" />
          <div v-if="(back || url) && !global.isMobile ">
           <GoBack :url />
          </div>
          <h2 class="h2 my-auto uppercase select-none max-sm:!text-md"> {{ title }} </h2>
        </div>
        <div class="flex" :class="noStyle ? 'h2 my-auto max-sm:!text-sm select-none' : ''">
          <slot name="title" />
        </div>
      </div> <x-hr class="mt-2" :class="noStyle ? '!mt-0 hidden' : ''" />
    </div>
    <div :class="noStyle ? 'mt-2' : 'px-4 py-2'" class="text-text dark:text-dark-accent">
      <slot />
    </div>
    <div class="px-4 pb-4" v-if="$slots.footer"> <x-hr />
      <slot name="footer" />
    </div>
  </div>
</template>