<script setup>
import { ref, watch, nextTick, onMounted, defineProps, onUnmounted } from 'vue'

defineOptions({ inheritAttrs: false })

const props = defineProps({
  position: {
    type: String,
    default: 'bottom-left',
    validator: (val) => [
      'top-left', 'top-right', 'top',
      'bottom-left', 'bottom-right', 'bottom',
      'left-top', 'left-bottom',
      'right-top', 'right-bottom'
    ].includes(val)
  }
})

const isOpen = ref(false)
const trigger = ref(null)
const menu = ref(null)
const menuStyle = ref({ top: '0px', left: '0px' })

async function positionMenu() {
  await nextTick()
  if (!trigger.value || !menu.value) return

  const rect = trigger.value.getBoundingClientRect()
  const mRect = menu.value.getBoundingClientRect()
  const vw = window.innerWidth
  const vh = window.innerHeight
  const margin = 8

  let top = 0, left = 0

  switch (props.position) {
    case 'top-left':
      top = rect.top - mRect.height - margin
      left = rect.left
      break
    case 'top-right':
      top = rect.top - mRect.height - margin
      left = rect.right - mRect.width
      break
    case 'top':
      top = rect.top - mRect.height - margin
      left = rect.left + (rect.width - mRect.width) / 2
      break
    case 'bottom-left':
      top = rect.bottom + margin
      left = rect.left
      break
    case 'bottom-right':
      top = rect.bottom + margin
      left = rect.right - mRect.width
      break
    case 'bottom':
      top = rect.bottom + margin
      left = rect.left + (rect.width - mRect.width) / 2
      break
    case 'left-top':
      top = rect.top
      left = rect.left - mRect.width - margin
      break
    case 'left-bottom':
      top = rect.bottom - mRect.height
      left = rect.left - mRect.width - margin
      break
    case 'right-top':
      top = rect.top
      left = rect.right + margin
      break
    case 'right-bottom':
      top = rect.bottom - mRect.height
      left = rect.right + margin
      break
  }

  // Корректируем, чтобы меню не выходило за экран
  top = Math.min(Math.max(margin, top), vh - mRect.height - margin)
  left = Math.min(Math.max(margin, left), vw - mRect.width - margin)

  menuStyle.value = { top: `${top}px`, left: `${left}px` }
}

watch(isOpen, open => {
  if (open) positionMenu()
})

let onDocClick;
let onResize;
let onScroll;

onMounted(async () => {
  await nextTick()

  onResize = () => {
    if (isOpen.value) positionMenu()
  }
  window.addEventListener('resize', onResize)

  onScroll = () => {
    if (isOpen.value) positionMenu()
  }
  window.addEventListener('scroll', onScroll)

  onDocClick = (e) => {
    const t = trigger.value
    const m = menu.value
    if (
      isOpen.value &&
      t && m &&
      !t.contains(e.target) &&
      !m.contains(e.target)
    ) {
      isOpen.value = false;
    }
  }
  document.addEventListener('mousedown', onDocClick)
})

onUnmounted(() => {
  window.removeEventListener('resize', onResize)
  window.removeEventListener('scroll', onScroll)
  document.removeEventListener('mousedown', onDocClick)
})
</script>

<template>
  <div ref="trigger" class="inline-block" @click.stop="isOpen = !isOpen">
    <slot name="button" />
  </div>

  <Teleport to="body">
    <div v-motion-slide-visible-top ref="menu" @click.stop="isOpen = false" :style="{
      position: 'fixed',
      top: menuStyle.top,
      left: menuStyle.left,
      zIndex: 1000
    }" v-show="isOpen" class="w-max min-w-48 rounded-xl shadow-lg 
               border border-gray-200/70 dark:border-white/10
               bg-white/80 dark:bg-black/80 text-sm text-gray-800 dark:text-gray-100
               ring-1 ring-black/5 dark:ring-white/10 py-2">
      <slot />
    </div>
  </Teleport>
</template>