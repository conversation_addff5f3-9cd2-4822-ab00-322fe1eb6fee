<script setup>
defineProps({
    as: {
        type: String,
        default: "div",
    },
});
</script>

<template>
    <component :is="as"
        class="text-text dark:text-dark-text max-sm:!p-2 format-accent flex w-full cursor-pointer p-3 select-none max-md:!text-sm hover:bg-dark-surface/20 dark:hover:bg-surface/20 hover:backdrop-blur-sm active:bg-dark-surface/10 dark:active:bg-surface/10  rounded-xl">
        <div class="my-auto ml-2 max-sm:!text-sm flex items-center gap-2 font-bold [&>*]:items-center">
            <slot />
        </div>
    </component>
</template>
