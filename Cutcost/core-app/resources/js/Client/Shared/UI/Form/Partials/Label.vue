<script setup>
import { PhInfo } from "@phosphor-icons/vue";
import { computed, useSlots } from "vue";

const slots = useSlots();

defineProps({
    help: {
        type: Object,
        default: null,
    }
});

const label = computed(() => {
    const content = slots.default?.()[0]?.children || '';
    return content.replace(/\*+$/, '');
});

const required = computed(() => {
    const content = slots.default?.()[0]?.children || '';
    return /\*+$/.test(content);
});
</script>

<template>
    <label class="text-text/80 items-center dark:text-dark-text/80 text-md mb-0.5 ml-1 flex font-semibold select-none">
        {{ label }}
        <span v-if="required" class="text-error">*</span>
        <template v-if="help">
            <PhInfo size="18" class="ml-1 cursor-pointer" @click="$refs.modal.modalOpen = true" />
        </template>
        <Modal ref="modal" :title="help?.title ?? __('Tooltip')">
            <div v-html="help?.text" class="text">
            </div>
        </Modal>
    </label>
</template>
