<script setup>
import draggable from 'vuedraggable';
import { useUploadStore } from '../../../Stores/use-upload-store';
import { useGlobalStore, BottomText } from "@s";
import { PhX } from '@phosphor-icons/vue';
import { ref } from 'vue';
const uploadStore = useUploadStore();
const global = useGlobalStore();

defineProps({
  separate: {
    type: Boolean,
    default: false,
  },
});

const askRemoveModal = ref(false);
const removeId = ref(null);
const dragDisabled = ref(false);

const onSortEnd = async () => {
  if (uploadStore.mediaList.length < 2) return;

  const loadingTimeout = setTimeout(() => {
    uploadStore.loading = true;
  }, 150);

  uploadStore.replaceMedia(uploadStore.mediaList);
  await uploadStore.saveSortOrder();
  clearTimeout(loadingTimeout);
  uploadStore.loading = false;
}

const remove = async () => {
  try {
    await uploadStore.destroyMedia([removeId.value]);
    askRemoveModal.value = false;
  } catch (e) {
    console.error(e);
    alert('error');
  }
}

const askRemove = (id) => {
  askRemoveModal.value = true;
  removeId.value = id;
}

const onDragStart = (evt) => {
  const target = evt.originalEvent.target;
  if (target.closest && target.closest('[aria-label="delete"]')) {
    dragDisabled.value = true;
  } else {
    dragDisabled.value = false;
  }
}
</script>

<template>
  <div class="  transition-colors duration-200"
    v-if="uploadStore.mediaList.length">

    <x-confirmation v-if="askRemoveModal" @confirm="remove" :for="removeId" @cancel="askRemoveModal = false"
      :message="__('Are you sure')" />

    <draggable v-model="uploadStore.mediaList" item-key="id" @start="onDragStart" :disabled="dragDisabled"
      @end="onSortEnd" :delay="global.isMobile ? 150 : 0" class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4">
      <template #item="{ element }">
        <div
          class="relative rounded-2xl overflow-hidden border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200"
          style="aspect-ratio: 4 / 3;">
          <img :src="global.assets + element.path" class="w-full h-full object-cover" alt="media item" loading="lazy" />
          <x-button intent="rounded" @click.stop="askRemove(element.id)"
            class="!absolute m-1 !top-0 !right-0 touch-manipulation" aria-label="delete"
            style="min-width: 32px; min-height: 32px;">
            <PhX :size="18" class="!fill-error" />
          </x-button>
        </div>
      </template>
    </draggable>

    <div v-if="uploadStore.mediaList.length === 0" class="flex justify-center py-6">
      <BottomText :message="__('Nothing')" />
    </div>
  </div>
</template>
