<script setup>
import { inject } from 'vue';
import axios from 'axios';
import { useUploadStore } from '../../../Stores/use-upload-store';
import { useTranslation, useToastStore } from "@s";

const uploadStore = useUploadStore();
const toast = useToastStore();
const route = inject('route');

const props = defineProps({
  modelId: { type: [Number, String], default: null },
  modelType: { type: String, default: null },
  max: { type: Number, default: 6 },
  separate: { type: Boolean, default: false },
});

function onFilesChange(event) {
  const files = Array.from(event.target.files);
  uploadFiles(files);
}

async function uploadFiles(files) {
  if (!files || !files.length) return;

  const currentCount = uploadStore.mediaList.length || 0;
  if (currentCount + files.length > props.max) {
    toast.error(
      useTranslation.__("You can upload maximum :max files", { max: props.max })
    );
    return;
  }

  let loadingTimeout = null;
  let showLoading = false;

  // debounce: only set loading after 300ms
  loadingTimeout = setTimeout(() => {
    showLoading = true;
    uploadStore.loading = true;
  }, 300);

  const MAX_SIZE = 8 * 1024 * 1024; // 8MB

  const validFiles = files.filter(f => {
    if (f.size > MAX_SIZE) {
      toast.error(useTranslation.__("File is too large", { file: f.name }));
      return false;
    }
    return true;
  });

  if (!validFiles.length) {
    clearTimeout(loadingTimeout);
    return;
  }

  const form = new FormData();
  validFiles.forEach(f => form.append('images[]', f));

  if (props.modelId && props.modelType) {
    form.append('model_id', props.modelId);
    form.append('model_type', props.modelType);
  }

  const uuid = props.tempUuid;
  if (uuid) form.append('temp_uuid', uuid);

  try {
    const { data } = await axios.post(route('media.upload'), form, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

    if (data.success) {
      if (data.temp_uuid) {
        uploadStore.tempUuid = data.temp_uuid;
      }
      uploadStore.addMedia(data.media);
    } else {
      console.warn('Upload failed', data);
      toast.error(useTranslation.__('error occurred'));
    }
  } catch (err) {
    console.error(err);
    toast.error(useTranslation.__('error occurred'));
  } finally {
    // clear timeout and remove loading if it was shown
    clearTimeout(loadingTimeout);
    if (showLoading) uploadStore.loading = false;
  }
}

</script>

<template>
  <x-button v-if="!separate" class="w-full" :disabled="uploadStore.loading" @click="$refs.upload.click()">
    {{ uploadStore.loading ? __('Loading') : __('Choose photos') + ' | ' + __('max') + ' ' + max
    }}
  </x-button>
  <input type="file" multiple accept="image/*" class="hidden" @change="onFilesChange" ref="upload"
    :disabled="uploadStore.loading" />
  <div @click="$refs.upload.click()" v-if="separate">
    <slot />
  </div>
</template>