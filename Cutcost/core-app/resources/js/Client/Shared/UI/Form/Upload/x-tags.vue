<script setup>
import { ref, watch, nextTick, computed } from 'vue';
import { useTranslation } from "@s";
import Error from '../Partials/Error.vue';
import { PhX } from '@phosphor-icons/vue';
import Label from '../Partials/Label.vue';

const model = defineModel({ type: String, default: '' });

// Props
const props = defineProps({
    placeholder: { type: String, default: '' },
    label: String,
    // режим: "edit" — интерактивно (по умолчанию), "view" — только отображение
    mode: { type: String, default: 'edit' }, // 'edit' | 'view'
});

// internal computed флаг редактируемости
const isEditable = computed(() => props.mode === 'edit');

const input = ref('');
const internalTags = ref(
    model.value ? model.value.split(',').map(t => t.trim()).filter(Boolean) : []
);
const error = ref('');
const textareaRef = ref(null);

watch(model, (newValue) => {
    internalTags.value = newValue ? newValue.split(',').map(t => t.trim()).filter(Boolean) : [];
});

// синхронизируем модель при изменении internalTags (без эмитов)
watch(
    internalTags,
    (newTags) => {
        model.value = newTags.join(',');
    },
    { deep: true }
);

// автоподгонка высоты textarea
function autosize() {
    const ta = textareaRef.value;
    if (!ta) return;
    ta.style.height = 'auto';
    ta.style.height = `${ta.scrollHeight}px`;
}

function normalizeTag(t) {
    return String(t ?? '').trim();
}

function addTagRaw(raw) {
    if (!raw) return;
    const tags = raw.split(',').map(normalizeTag).filter(Boolean);
    for (const tag of tags) {
        if (!internalTags.value.includes(tag)) {
            internalTags.value.push(tag);
            // раньше здесь был emit('add'), теперь его нет — просто обновляем model через watch
        } else {
            error.value = useTranslation.__ ? useTranslation.__("Tag already exists") : "Tag already exists";
            setTimeout(() => (error.value = ''), 2000); // очистим ошибку через 2s
        }
    }
    input.value = '';
    nextTick(autosize);
}

function removeTag(index) {
    // В режиме view удаление запрещено — но дополнительная защита:
    if (!isEditable.value) return;
    internalTags.value.splice(index, 1);
    nextTick(autosize);
}

// клавиатурные / paste хендлеры (работают только в режиме edit)
function onKeydown(e) {
    if (!isEditable.value) return;
    if (['Enter', ','].includes(e.key)) {
        e.preventDefault();
        addTagRaw(input.value);
        return;
    }

    if (e.key === 'Backspace' && input.value === '' && internalTags.value.length) {
        removeTag(internalTags.value.length - 1);
    }
}

function onPaste(e) {
    if (!isEditable.value) return;
    e.preventDefault();
    const pasted = (e.clipboardData || window.clipboardData).getData('text');
    addTagRaw(pasted);
}

// стартовая автоподгонка при изменении input
watch(input, () => nextTick(autosize));
</script>

<template>
    <div class="w-full">
        <Label v-if="label">
            {{ label }}
        </Label>

        <div class="flex flex-wrap items-center gap-2 p-1.5 border border-zinc-300 dark:border-zinc-700 rounded-xl bg-reverse focus-within:ring-1 focus-within:ring-secondary transition-all"
            :class="{ 'border-error': error }, {
                '!bg-transparent': mode === 'view'
            }">
            <template v-for="(tag, index) in internalTags" :key="tag + '-' + index">
                <span class="inline-flex items-center gap-1 px-2 py-1 bg rounded-full text-sm text"
                    :class="{ 'dark:!bg-bg-dark !bg-surface !rounded-lg': mode === 'view' }">
                    <span class="truncate max-w-[12rem]" :class="{ 'text-success ': mode === 'view' }">{{ tag }}</span>

                    <template v-if="isEditable">
                        <x-button type="button" class="!py-1 !px-1" @click="removeTag(index)" intent="rounded">
                            <PhX :size="14" class="fill-error" />
                        </x-button>
                    </template>
                </span>
            </template>

            <template v-if="isEditable">
                <textarea ref="textareaRef" v-model="input" @keydown="onKeydown" @paste="onPaste"
                    class="flex-1 min-w-[10rem] resize-none text bg-transparent outline-none text-sm p-1"
                    :placeholder="internalTags.length ? '' : placeholder" rows="1"></textarea>
            </template>
        </div>

        <Error v-if="error" class="mt-1">{{ error }}</Error>
    </div>
</template>

<style scoped>
textarea {
    line-height: 1.5;
    min-height: 28px;
}
</style>
