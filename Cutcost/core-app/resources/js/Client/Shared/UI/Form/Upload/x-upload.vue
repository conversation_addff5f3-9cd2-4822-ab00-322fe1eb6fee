<script setup>
import { onMounted, onUnmounted, watchEffect } from 'vue';
import MediaUpload from './MediaUpload.vue';
import MediaPreview from './MediaPreview.vue';
import { useUploadStore } from '../../../Stores/use-upload-store';

const uploadStore = useUploadStore();

const props = defineProps({
  modelId: {
    type: [Number, String],
    default: null
  },
  modelType: {
    type: String,
    default: null
  },
  max: {
    type: Number,
    default: 6
  },
  media: {
    type: Array,
    default: []
  },
  separate: {
    type: Boolean,
    default: false
  }
});

const model = defineModel({
  type: Object,
  default: () => ({
    temp_uuid: null,
    deleted_ids: [],
  }),
});

watchEffect(() => {
  if (props.separate) return;

  model.value.temp_uuid = uploadStore.tempUuid;
  model.value.deleted_ids = uploadStore.deletedIds;
});

onMounted(() => {

  if (props.media?.length) {
    uploadStore.replaceMedia(props.media);
  }

});

onUnmounted(() => {
  uploadStore.tempUuid = null;
  uploadStore.deletedIds = [];
  uploadStore.mediaList = [];
  uploadStore.errors = [];
});

</script>

<template>
  <div class="space-y-2">
    <x-overlay v-if="uploadStore.loading" isBody>
      <div class="flex justify-center items-center h-full h1 !text-5xl">
        {{ __("Loading") }}
      </div>
    </x-overlay>
    <MediaUpload :modelId :modelType :separate>
      <slot />
    </MediaUpload>

    <MediaPreview v-if="!separate" />
  </div>
</template>
