<script setup>
import { PhCheckFat, PhX } from "@phosphor-icons/vue";

const model = defineModel({
  type: [Boolean, Object],
  required: false,
});

defineProps({
  label: {
    type: String,
  },
  id: {
    type: String,
  },
  falseCross: Boolean,
});

const uniqueId = "id-" + Math.random().toString(36).slice(2, 11);

</script>

<template>
  <div class="inline-flex items-center ">
    <label class="relative flex cursor-pointer  rounded-full p-2" for="ripple-on" data-ripple-dark="true">
      <input :id="uniqueId + id" type="checkbox" v-model="model" v-bind="$attrs" class="peer relative h-6 w-6 cursor-pointer appearance-none rounded border shadow
         border-border dark:bg-bg-dark dark:border-0 bg-surface
         before:absolute before:top-1/2 before:left-1/2 before:block before:h-12 before:w-12
         before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:opacity-0
         hover:shadow-md hover:before:opacity-10
         checked:border-border checked:bg-secondary checked:before:bg-primary" :class="{
          '!bg-secondary dark:!bg-secondary before:!bg-primary': falseCross, 
        }" />

      <span
        class="text-bg pointer-events-none opacity-0 peer-checked:opacity-100 absolute top-2/4 left-2/4 -translate-x-2/4 -translate-y-2/4  transition-opacity ">
        <PhCheckFat :size="16" weight="fill" />
      </span>
      <span v-if="falseCross"
        class="text-bg pointer-events-none opacity-100 peer-checked:opacity-0 absolute top-2/4 left-2/4 -translate-x-2/4 -translate-y-2/4  transition-opacity ">
        <PhX :size="16" weight="bold" />
      </span>
    </label>
    <label :for="uniqueId + id" class="text-text/80 mt-0.5 dark:text-dark-text/80 text-md flex font-semibold select-none">{{
      label }}
      <slot />
    </label>
  </div>
</template>
