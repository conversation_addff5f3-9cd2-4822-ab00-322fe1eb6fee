<script setup>
import { defineProps, defineEmits } from "vue";
import { PhWarning } from "@phosphor-icons/vue";

const props = defineProps({
  form: Object,
  title: String,
  type: String,
  noClass: Boolean,
  back: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(["submit"]);

const submitForm = () => {
  if (!props.form.isDirty) return;
  if (!props.form.processing) emits("submit");
};
</script>

<template>
  <form :id="'_form' + title + type" @submit.prevent="submitForm" v-bind="$attrs">
    <Card :title="title" :back v-if="!noClass">
      <template #title>
        <slot name="title" />
      </template>
      <slot />
    </Card>
    <slot v-else />
  </form>

  <div class="my-2 flex max-sm:mx-4 " :class="form.isDirty ? 'justify-between' : 'justify-end'">
    <div v-if="form.isDirty"
      class="  flex max-w-sm  items-center gap-2 rounded-lg p-2 text-center  shadow-sm select-none  md:max-w-lg">
      <PhWarning :size="22" class="text-error " />
      <div class="text-text dark:text-dark-text max-sm:text-sm">
        {{ __("Save changes") }}
      </div>
    </div>

    <x-button :loading="form.processing" intent="secondary" class=" flex  max-sm:ml-auto" :form="'_form' + title + type"
      type="submit" :disabled="form.processing || !form.isDirty">
      {{
        type === "create" ? __("Create") : __("Update")
      }}
    </x-button>
  </div>

  <div class="my-2">
    <x-errors :errors="form.errors" />
  </div>
</template>
