<script setup>
import { ref, computed, useAttrs } from "vue";
import Error from "./Partials/Error.vue";
import { PhEye, PhEyeClosed, PhX } from "@phosphor-icons/vue";

const model = defineModel({ type: null });
const input = ref(null);
const isFocused = ref(false);
const passwordShow = ref(false);

const attrs = useAttrs();

const props = defineProps({
  label: String,
  type: {
    type: String,
    default: "text",
  },
  message: String,
  max: [Number, String],
  min: [Number, String],
});

const inputType = ref(props.type);

const togglePassword = () => {
  passwordShow.value = !passwordShow.value;
  inputType.value = passwordShow.value ? "text" : "password";
};

defineExpose({
  focus: () => input.value?.focus(),
});

const inputAttrs = computed(() => {
  const result = { ...attrs };

  if (props.type === 'number') {
    if (result.max === undefined && props.max !== undefined) result.max = props.max;
    if (result.min === undefined && props.min !== undefined) result.min = props.min;
  }

  if (['text', 'search', 'email'].includes(props.type)) {
    if (result.maxlength === undefined && props.max !== undefined) {
      result.maxlength = props.max;
    }
    if (result.minlength === undefined && props.min !== undefined) {
      result.minlength = props.min;
    }
  }

  return result;
});
</script>

<template>
  <div class="w-full" :class="{ '!mt-0': !label }">
    <Label v-if="label">
      {{ label }}
    </Label>

    <div class="relative">
      <input ref="input" v-model="model" :type="inputType" @focus="isFocused = true" @blur="isFocused = false"
        v-bind="inputAttrs" class="w-full rounded-xl border bg-surface dark:bg-bg-dark text-text dark:text-dark-text px-3 py-2
         transition focus:border-primary focus:shadow-md focus:shadow-primary/10
         " :class="message ? 'border-error' : 'border-zinc-300 dark:border-zinc-700',
          { '!pr-8': props.type === 'search' }" />

      <span class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center cursor-pointer">
        <template v-if="props.type === 'search' && model?.length">
          <PhX class="block fill-secondary" weight="bold" :size="16" @click="model = ''" />
        </template>
        <template v-if="props.type === 'password'" @click="togglePassword">
          <PhEye v-if="passwordShow" class="block" :size="16" />
          <PhEyeClosed v-else class="block" :size="16" />
        </template>
        <slot name="icon" />
      </span>

    </div>

    <Error v-if="message" class="mt-1 text-sm text-error">{{ message }}</Error>
  </div>
</template>
