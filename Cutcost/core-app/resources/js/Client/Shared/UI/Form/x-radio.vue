<script setup>
  import { ref, useSlots } from "vue";

  const model = defineModel({ required: false });

  const slots = useSlots();
  const label = ref(slots.default()[0].children);

  const uniqueId = "id-" + Math.random().toString(36).slice(2, 11);
</script>
<template>
  <div class="inline-flex items-center">
    <label class="relative flex cursor-pointer items-center" :for="uniqueId">
      <input
        v-bind="$attrs"
        name="framework"
        v-model="model"
        type="radio"
        class="peer checked:border-border h-5 w-5 cursor-pointer appearance-none rounded-full border-2 border-slate-400 transition-all"
        :id="uniqueId"
      />
      <span
        class="bg-secondary absolute top-1/2 left-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform rounded-full opacity-0 transition-opacity duration-200 peer-checked:opacity-100"
      >
      </span>
    </label>
    <label
      class="text-text dark:text-dark-text ml-2 cursor-pointer text-sm"
      :for="uniqueId"
      ><slot
    /></label>
  </div>
</template>
