
<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted, watchEffect, useSlots } from 'vue';
import { useTranslation } from '@s';
import { PhCaretCircleDown, PhTrash } from '@phosphor-icons/vue';

const __ = useTranslation.__;
const emit = defineEmits(['intersect', 'search']);

// Props
const props = defineProps({
  data: { type: Array, required: true },
  label: String,
  placeholder: String,
  message: String,
  multiple: Boolean,
  lang: Boolean,
  search: Boolean,
  required: { type: Boolean, default: false },
  use: { type: String, default: 'id' },
  observe: { type: Boolean, default: false },
  help: {
    type: Object,
    default: null,
  },
});

const modelValue = defineModel(); // оставляем привычный helper
const slots = useSlots();
// local refs
const show = ref(false);
const trigger = ref(null);
const dropdown = ref(null);
const searchTerm = ref('');
const dropdownStyles = ref({ top: '0px', left: '0px', '--dropdown-width': '200px' });

// helpers
const placeholderText = computed(() => props.placeholder ?? __('Select option'));
const isObject = v => typeof v === 'object' && v !== null && !Array.isArray(v);

const displayName = (item) => {
  if (!item) return '';
  const n = item.name;
  if (props.lang) {
    try { return __(n); } catch (e) { /* noop */ }
  }
  return isObject(n) ? JSON.stringify(n) : n;
};

const getValue = (item) => {
  if (!item) return null;
  return Object.prototype.hasOwnProperty.call(item, props.use) ? item[props.use] : (item.id ?? null);
};

// filtered list (search)
const filtered = computed(() => {
  if (!props.search || !searchTerm.value) return props.data ?? [];
  const q = searchTerm.value.toLowerCase();
  return (props.data ?? []).filter(it => {
    const n = it.name;
    const candidates = isObject(n) ? Object.values(n) : [n];
    if (props.lang) {
      try { candidates.push(__(n)); } catch (e) { }
    }
    return candidates.some(t => t && String(t).toLowerCase().includes(q));
  });
});

// model <> selected sync
const selected = ref(null); // for single: item object
const selectedValues = ref([]); // for multiple: array of returned values
const tags = computed(() => {
  // map selectedValues to label if available in data, otherwise show value as label
  return selectedValues.value.map(val => {
    const found = (props.data ?? []).find(d => getValue(d) === val);
    return { value: val, label: found ? displayName(found) : String(val), ...found };
  });
});

watch(searchTerm, (v) => {
  emit('search', v);
});

watch(modelValue, (v) => {
  if (props.multiple) {
    selectedValues.value = Array.isArray(v) ? [...v] : [];
  } else {
    // single
    if (v === null || v === undefined) {
      selected.value = null;
    } else {
      selected.value = (props.data ?? []).find(d => getValue(d) === v) ?? null;
    }
  }
}, { immediate: true });

watchEffect(() => {
  const val = modelValue.value;
  const data = props.data;

  if (props.multiple) {
    selectedValues.value = Array.isArray(val) ? [...val] : [];
  } else {
    if (val == null || val == undefined) {
      selected.value = null;
    } else {
      selected.value = (data ?? []).find(d => getValue(d) == val) ?? null;
    }
  }
});

// UI helpers
const selectedLabel = computed(() => selected.value ? displayName(selected.value) : null);
const triggerClass = 'flex w-full cursor-pointer items-center border-muted border justify-between rounded-xl p-2.5 bg-surface dark:bg-bg-dark hover:text-secondary text-text dark:text-dark-text';

// item classes
const itemClass = (item) => {
  const isActive = (!props.multiple && selected.value?.id === item.id) || (props.multiple && selectedValues.value.includes(getValue(item)));
  return [
    'p-3 cursor-pointer !select-none',
    isActive ? 'font-extrabold text-secondary' : 'text-text dark:text-dark-text hover:bg-bg dark:hover:bg-dark-surface',
    slots.default ? '!p-0' : ''
  ];
};

// selection
const onSelect = (item) => {
  if (props.multiple) {
    const val = getValue(item);
    const idx = selectedValues.value.indexOf(val);
    if (idx >= 0) {
      if (props.required && selectedValues.value.length <= 1) return;
      selectedValues.value.splice(idx, 1);
    } else {
      selectedValues.value.unshift(val);
    }
    modelValue.value = [...selectedValues.value];
  } else {
    const was = selected.value && getValue(selected.value) === getValue(item);
    if (was) {
      if (props.required) return;
      selected.value = null;
      modelValue.value = null;
    } else {
      selected.value = item;
      modelValue.value = getValue(item);
    }
    show.value = false;
  }
};

const removeTag = (i) => {
  if (props.required && selectedValues.value.length <= 1) return;
  selectedValues.value.splice(i, 1);
  modelValue.value = [...selectedValues.value];
};

// positioning (robust)
const measureHeight = (el) => {
  if (!el) return 0;
  const h = el.offsetHeight;
  if (h > 0) return h;
  // temporary reveal for measurement
  const prevVis = el.style.visibility;
  const prevDisp = el.style.display;
  el.style.visibility = 'hidden';
  el.style.display = 'block';
  const m = el.offsetHeight || 0;
  el.style.display = prevDisp;
  el.style.visibility = prevVis;
  return m;
};

const updatePosition = async () => {
  if (!trigger.value || !dropdown.value) return;
  await nextTick();
  await new Promise(r => requestAnimationFrame(r));

  const rect = trigger.value.getBoundingClientRect();
  const neededHeight = measureHeight(dropdown.value) || 200;
  const neededWidth = rect.width || dropdown.value.offsetWidth || 200;

  const spaceBelow = window.innerHeight - rect.bottom;
  const topPx = spaceBelow >= neededHeight + 6 ? Math.round(rect.bottom + 6) : Math.round(Math.max(6, rect.top - neededHeight - 6));

  const spaceRight = window.innerWidth - rect.right;
  const leftPx = spaceRight >= neededWidth ? Math.round(rect.left) : Math.round(Math.max(6, rect.right - neededWidth));

  dropdownStyles.value = { top: `${topPx}px`, left: `${leftPx}px`, '--dropdown-width': `${rect.width}px` };
};

// open/close
const toggle = () => {
  if (props.disabled) return;
  show.value = !show.value;
};

// watchers & events
watch(() => show.value, async (v) => {
  if (v) {
    await updatePosition();
    requestAnimationFrame(() => { if (show.value) updatePosition(); });
  }
});

// click outside & scroll/resize
const onClickOutside = (e) => {
  if (!trigger.value || !dropdown.value) return;
  if (!trigger.value.contains(e.target) && !dropdown.value.contains(e.target)) show.value = false;
};
const onScroll = (e) => {
  // allow scrolling inside dropdown
  if (dropdown.value && dropdown.value.contains(e.target)) return;
  show.value = false;
};
const onResize = () => { if (show.value) updatePosition(); };

onMounted(() => {
  document.addEventListener('mousedown', onClickOutside);
  document.addEventListener('scroll', onScroll, true);
  window.addEventListener('resize', onResize);
});

onUnmounted(() => {
  document.removeEventListener('mousedown', onClickOutside);
  document.removeEventListener('scroll', onScroll, true);
  window.removeEventListener('resize', onResize);
});
</script>


<template>
  <Label v-if="props.label" :help>
    {{ props.label }}
  </Label>

  <div class="relative w-full select-none">
    <div ref="trigger" @click="toggle" :class="triggerClass" v-bind="$attrs">
      <span v-if="!props.multiple">
        <template v-if="$slots.default && selectedLabel">
          <slot :item="selected" />
        </template>
        <template v-else>
          {{ selectedLabel ?? placeholderText }}
        </template>
      </span>
      <span v-else>{{ placeholderText }}</span>

      <PhCaretCircleDown v-if="!props.disabled" :size="20" :class="{ 'rotate-180': show }" class="transition-all" />
    </div>

    <Teleport to="body">
      <ul v-show="show" ref="dropdown" :style="dropdownStyles"
        class="fixed bg-surface shadow-md dark:bg-bg-dark shadow-border dark:shadow-dark-border rounded-xl max-h-78 overflow-y-auto overscroll-contain z-1000 w-[var(--dropdown-width)]">
        <li v-if="props.search" class="sticky top-0 z-10 bg-surface dark:bg-bg-dark p-2">
          <x-input v-model="searchTerm" :placeholder="__('Search')"
           />
        </li>

        <li v-for="(item, i) in filtered" :key="item.id ?? i" @click="onSelect(item)" :class="itemClass(item)">
          <template v-if="$slots.default">
            <slot :item="item" />
          </template>
          <template v-else>
            {{ displayName(item) }}
          </template>
        </li>

        <li v-if="!filtered.length && !$slots.nothing" class="text-text dark:text-dark-text p-3 text-center">
          {{ __('Nothing') }}
        </li>

        <x-observe v-if="observe" @intersect="$emit('intersect')" />
        <slot name="nothing" />
      </ul>
    </Teleport>
  </div>

  <Error v-if="message">{{ message }}</Error>

  <div v-if="props.multiple" class="mt-2 flex flex-wrap select-none">
    <div v-for="(tag, idx) in tags" :key="idx"
      class="bg-surface truncate dark:bg-bg-dark flex items-center mr-1 mb-1 rounded-xl p-2">
      <span class="flex truncate">
        <template v-if="$slots.default">
          <slot :item="tag" />
        </template>
        <template v-else>
          {{ tag.label }}
        </template>
      </span>
      <PhTrash class="ml-2 cursor-pointer text-error" size="16" @click="removeTag(idx)" />
    </div>
  </div>
</template>

<style scoped>
*::-webkit-scrollbar {
  width: 6px;
}

*::-webkit-scrollbar-thumb {
  background: #a0aec0;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}
</style>
