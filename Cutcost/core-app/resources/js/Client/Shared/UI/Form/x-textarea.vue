<script setup>
import Label from "./Partials/Label.vue";
import Error from "./Partials/Error.vue";
import { useAttrs } from "vue";

const model = defineModel({
    required: true,
});

const attrs = useAttrs();

defineProps({
    label: String,
    placeholder: String,
    message: String,
    rows: {
        type: Number,
        default: 4,
    },
    max: {
        type: Number,
        default: 128,
    },
    min: {
        type: Number,
        default: 0,
    },
    noCount: Boolean,
});

</script>
<template>
    <div>
        <Label v-if="label" class="mb-1  block  font-medium text-text dark:text-dark-text">
            {{ label }}
        </Label>

        <div class="flex flex-col space-y-1">
            <textarea :rows="rows" :maxlength="max" :minlength="min" v-bind="attrs" :placeholder="placeholder"
                v-model="model" :class="[
                    'w-full rounded-xl border px-4 py-2 resize-none',
                    'bg-surface dark:bg-bg-dark text-text dark:text-dark-text',
                    'transition focus:border-primary focus:shadow-md focus:shadow-primary/10',
                    message ? 'border-error' : 'border-zinc-300 dark:border-zinc-700',
                ]" />


            <div class="flex items-center text-sm">
                <Error v-if="message" class="text-error">{{ message }}</Error>
                <div v-if="!noCount" class="ml-auto select-none text-zinc-500 dark:text-dark-muted"
                    :class="{ '!text-error': model?.length === max }">
                    {{ model?.length ?? 0 }} / {{ max }}
                </div>
            </div>
        </div>
    </div>
</template>
