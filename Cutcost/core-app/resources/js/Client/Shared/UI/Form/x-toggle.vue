<script setup>
    defineProps({
        label: {
            type: String,
        },
    });

    const model = defineModel({
        type: Boolean,
        required: false,
    });
</script>

<template>
    <label class="inline-flex cursor-pointer items-center">
        <input
            type="checkbox"
            v-model="model"
            class="peer sr-only flex"
            v-bind="$attrs"
        />
        <div
            class="peer peer-checked:bg-secondary relative h-6 w-11 rounded-full bg-gray-200 peer-focus:ring-4 peer-focus:ring-secondary/60 peer-focus:outline-none after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-secondary dark:peer-focus:ring-secondary"
        ></div>
        <span class="ms-3 text-sm font-medium dark:text-gray-300">{{
            label
        }}</span>
    </label>
</template>
