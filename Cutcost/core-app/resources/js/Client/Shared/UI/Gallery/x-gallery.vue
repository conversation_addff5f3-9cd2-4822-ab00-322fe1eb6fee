<script setup>
import { ref, computed, watch } from "vue";
import { useGlobalStore } from "@s";
import { useFileDriver } from "@s";
import { PhArrowLeft, PhArrowRight } from "@phosphor-icons/vue";

const props = defineProps({
  media: {
    type: Object,
    default: () => [],
  },
  start: Number,
  max: <PERSON><PERSON><PERSON>,
  h: {
    type: [Number, String],
    default: 60,
  },
  def: {
    type: String,
    default: '',
  }
});

const emit = defineEmits(["change"]);

const global = useGlobalStore();
const { handleImageError } = useFileDriver();

// Internal state
const index = ref(props.start ?? 0);

// Computed: Total images
const images = computed(() => props.media?.map(m => m.path) || []);

// Navigation
const up = () => {
  index.value = (index.value + 1) % images.value.length;
  emit("change", index.value);
};

const down = () => {
  index.value = (index.value - 1 + images.value.length) % images.value.length;
  emit("change", index.value);
};

watch(() => props.start, (val) => {
  index.value = val;
});
</script>

<template>
  <div class="relative flex w-full" v-if="media.length">
    <!-- Left Arrow -->
    <div v-if="images.length > 1" @click="down"
      class="hover:bg-text/10 absolute left-0 h-full w-1/2 cursor-pointer sm:hover:rounded-l-xl">
      <span class="flex h-full w-full">
        <PhArrowLeft :size="35" class="my-auto p-2 sm:rounded-full bg-black mr-auto ml-10 fill-white opacity-40" />
      </span>
    </div>

    <!-- Right Arrow -->
    <div v-if="images.length > 1" @click="up"
      class="hover:bg-text/10 absolute right-0 h-full w-1/2 cursor-pointer sm:hover:rounded-r-xl">
      <span class="flex h-full w-full ">
        <PhArrowRight :size="35" class="my-auto p-2 sm:rounded-full bg-black mr-10 ml-auto fill-white opacity-40" />
      </span>
    </div>

    <img loading="lazy" @error="handleImageError"
      :src="images[index].includes('http') || images[index].includes('data:image') || images[index].includes('http') ? images[index] : global.assets + images[index]"
      style="-webkit-user-drag: none !important" :class="[
        'w-full sm:rounded-2xl select-none',
        max ? 'max-h-[90vh] object-contain' : 'object-cover',
      ]" :style="{ height: (h * 4) + 'px' }" />
  </div>
  <div v-else-if="def">
    <div :style="{ height: (h * 4) + 'px' }" style="-webkit-user-drag: none !important" :class="[
      'w-full sm:rounded-2xl select-none dark:bg-dark-border bg-border',
    ]">
      <img loading="lazy" @error="handleImageError"
        :src="global.appUrl + def"
        style="-webkit-user-drag: none !important" :class="[
          'w-full sm:rounded-2xl select-none',
          max ? 'max-h-[90vh] object-contain' : 'object-cover',
        ]" :style="{ height: (h * 4) + 'px' }" />
    </div>
  </div>
</template>
