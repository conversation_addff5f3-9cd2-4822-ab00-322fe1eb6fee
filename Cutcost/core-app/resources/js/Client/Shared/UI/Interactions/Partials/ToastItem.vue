<script setup>
import { onMounted, useTemplateRef, ref, computed } from "vue";

const toast = useTemplateRef("toastItem");
const emit = defineEmits(["remove"]);
const progressWidth = ref(0);
let elapsedTime = 0;
let interval = null;
let timeoutId = null;

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  duration: {
    type: Number,
    default: 3000,
  },
});

const getIcon = computed(() => {
  switch (props.item.type) {
    case "success":
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>`;
    case "error":
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>`;
    case "warning":
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
            </svg>`;
    case "info":
    default:
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            </svg>`;
  }
});

onMounted(() => {
  let startTime = Date.now();

  const updateProgress = () => {
    progressWidth.value = Math.min(
      ((Date.now() - startTime + elapsedTime) / props.duration) * 100,
      100,
    );
    if (progressWidth.value >= 100) clearInterval(interval);
  };

  interval = setInterval(updateProgress, 50);
  timeoutId = setTimeout(() => {
    emit("remove");
  }, props.duration);

  toast.value.addEventListener("mouseenter", () => {
    clearTimeout(timeoutId);
    clearInterval(interval);
    elapsedTime += Date.now() - startTime;
  });

  toast.value.addEventListener("mouseleave", () => {
    let remainingTime = props.duration - elapsedTime;
    timeoutId = setTimeout(() => {
      emit("remove");
    }, remainingTime);
    startTime = Date.now();
    interval = setInterval(() => {
      progressWidth.value = Math.min(
        ((Date.now() - startTime + elapsedTime) / props.duration) * 100,
        100,
      );
      if (progressWidth.value >= 100) clearInterval(interval);
    }, 50);
  });
});
</script>

<template>
  <div ref="toastItem"
    class="border-border bg-bg text-text dark:border-dark-border dark:bg-dark-surface dark:text-dark-text flex w-full max-w-sm transform flex-col rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm transition-all duration-300 ease-out sm:max-w-md md:max-w-lg dark:shadow-2xl">
    <div class="flex items-start gap-3">
      <!-- Icon -->
      <div :class="[
        'mt-0.5 flex-shrink-0',
        item.type === 'success'
          ? 'text-green-600 dark:text-green-400'
          : item.type === 'error'
            ? 'text-red-500 dark:text-red-400'
            : item.type === 'warning'
              ? 'text-yellow-600 dark:text-yellow-400'
              : 'text-blue-600 dark:text-blue-400',
      ]" v-html="getIcon"></div>

      <!-- Content -->
      <div class="min-w-0 flex-1 break-words">
        <div class="text-sm leading-5 font-medium break-words whitespace-pre-wrap sm:text-base sm:leading-6">
          {{ __(item.message) }}
        </div>
        <div v-if="item.description"
          class="mt-1 text-xs leading-4 break-words whitespace-pre-wrap opacity-70 sm:text-sm sm:leading-5">
          {{ __(item.description) }}
        </div>
      </div>

      <button
        class="border-border bg-bg text-text hover:text-text dark:border-dark-border dark:bg-bg-dark dark:text-dark-text dark:hover:bg-dark-surface dark:hover:text-dark-text flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full border transition-all duration-200 hover:bg-gray-200"
        @click="emit('remove')">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
        </svg>
      </button>
    </div>

    <!-- Progress bar -->
    <div class="bg-bg dark:bg-bg-dark mt-3 h-1 w-full overflow-hidden rounded-full">
      <div :style="{ width: progressWidth + '%' }" :class="[
        'h-full rounded-full transition-all duration-100 ease-linear',
        item.type === 'success'
          ? 'bg-green-600 dark:bg-green-400'
          : item.type === 'error'
            ? 'bg-red-500 dark:bg-red-400'
            : item.type === 'warning'
              ? 'bg-yellow-600 dark:bg-yellow-400'
              : 'bg-blue-600 dark:bg-blue-400',
      ]"></div>
    </div>
  </div>
</template>
