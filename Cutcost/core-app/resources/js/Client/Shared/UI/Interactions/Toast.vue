<script setup>
  import { usePage, router } from "@inertiajs/vue3";
  import { useToastStore } from "@s";
  import ToastItem from "./Partials/ToastItem.vue";
  import { onMounted } from "vue";

  const toast = useToastStore();
  const page = usePage();

  router.on("finish", () => {
    startToast();
  });

  const startToast = () => {
    const flashMessages = {
      info: "info",
      success: "success",
      warning: "warning",
      error: "error",
    };

    Object.entries(flashMessages).forEach(([key, type]) => {
      if (page?.props?.flash?.[key]) {
        toast.messages.unshift({
          message: page?.props?.flash?.[key],
          type,
          key: Symbol(),
        });
      }
    });
  };

  const remove = (index) => {
    toast.remove(index);
  };

  onMounted(() => {
    startToast();
  });
</script>

<template>
  <TransitionGroup
    tag="div"
    enter-from-class="translate-x-full opacity-0"
    enter-active-class="duration-500"
    leave-active-class="duration-500"
    leave-to-class="translate-x-full opacity-0"
    class="fixed bottom-15 z-1000 w-full space-y-2 px-4 sm:right-8 sm:left-auto sm:w-auto sm:px-0"
  >
    <ToastItem
      v-for="(item, index) in toast.messages"
      :item="item"
      :key="item.key"
      @remove="remove(index)"
    >
    </ToastItem>
  </TransitionGroup>
</template>
