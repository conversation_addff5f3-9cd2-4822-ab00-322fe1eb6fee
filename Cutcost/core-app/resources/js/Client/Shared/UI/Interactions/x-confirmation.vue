<script setup>
import { computed, ref, watch } from "vue";

const props = defineProps({
  message: String,
  for: [String, Number],
  strict: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["confirm", "cancel"]);

const userAnswer = ref("");
const problem = ref("");
const correctAnswer = ref(null);

// Генерируем простую задачу сложения двух чисел 1-10
function generateProblem() {
  const a = Math.floor(Math.random() * 10) + 1;
  const b = Math.floor(Math.random() * 10) + 1;
  problem.value = `${a} + ${b} = ?`;
  correctAnswer.value = a + b;
}

watch(
  () => props.strict,
  (strict) => {
    if (strict) {
      generateProblem();
      userAnswer.value = "";
    }
  },
  { immediate: true }
);

function confirm() {
  if (props.strict) {
    if (Number(userAnswer.value) === correctAnswer.value) {
      emit("confirm");
    } else {
      emit("cancel");
    }
  } else {
    emit("confirm");
  }
}

function cancel() {
  emit("cancel");
}

const isCorrect = computed(() => {
  return !props.strict || Number(userAnswer.value) === correctAnswer.value;
})
</script>

<template>
  <Teleport to="body">
    <div  class="fixed inset-0 z-10000  flex items-center justify-center">
      <x-overlay @click="cancel" class="!z-0" />

      <div v-motion-slide-visible-bottom class="relative z-10 max-w-sm w-full text  rounded-lg  p-6 text-center shadow-lg  
               bg">
        <h2 class="mb-6 text-lg font-medium leading-relaxed">{{ message }}</h2>

        <template v-if="strict">
          <p class="mb-4 font-mono">{{ problem }}</p>
          <x-input v-model="userAnswer" type="number" class="mb-4 w-full rounded px-3 py-2 text-center" />
        </template>

        <div class="flex justify-center gap-4">
          <x-button @click="confirm" v-if="isCorrect" intent="danger">{{ __("Yes") }}</x-button>
          <x-button @click="cancel" intent="success">{{ __("No") }}</x-button>
        </div>
      </div>
    </div>
  </Teleport>
</template>
