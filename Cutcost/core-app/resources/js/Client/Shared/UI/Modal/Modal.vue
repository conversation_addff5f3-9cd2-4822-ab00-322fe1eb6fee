<script setup>
import { PhX } from "@phosphor-icons/vue";
import { onUnmounted, ref, useAttrs, watch, computed } from "vue";
import { useHeaderStore } from "@m/Fixed";
import { router } from "@inertiajs/vue3";

const modalOpen = ref(false);
const attrs = useAttrs();

const props = defineProps({
    layer: { type: Number, default: null },
    short: Boolean,
    h: { type: Number, default: 34 },
});

const store = useHeaderStore();
const uid = Symbol("modal");

const computedLayer = computed(() => {
    if (props.layer !== null) return props.layer;
    return store.modalStack.length;
});

watch(modalOpen, (val) => {
    if (val) {
        store.registerModal({
            uid,
            close: () => (modalOpen.value = false),
            layer: computedLayer.value,
        });
        document.body.classList.add("!overflow-hidden");
    } else {
        store.unregisterModal(uid);
    }
});

onUnmounted(() => {
    store.unregisterModal(uid);
});

defineExpose({ modalOpen });

router.on("finish", () => {
    document.body.classList.remove("!overflow-hidden");
});
</script>

<template>
    <Teleport to="body">
        <x-overlay v-if="modalOpen" :layer="computedLayer" @click="modalOpen = false" />
        <Card v-show="modalOpen" v-motion-slide-visible-bottom
            class="fixed inset-0 max-sm:mt-auto h-min w-full overflow-visible sm:rounded-2xl shadow-lg" :class="[
                short
                    ? 'sm:max-w-[70%] md:max-w-[500px] max-h-[60vh] sm:max-h-[55vh]'
                    : 'sm:max-w-[92%] md:max-w-[720px] max-h-[90vh] sm:max-h-[80vh]',
            ]" v-bind="attrs" :style="`z-index: ${50 + computedLayer};`">
            <template #title>
                <div class="flex items-center">
                    <x-button intent="rounded" @click="modalOpen = false">
                        <PhX :size="20" weight="bold" />
                    </x-button>
                </div>
            </template>

            <div class="overflow-y-auto max-h-[calc(90vh-80px)]">
                <slot />
            </div>
        </Card>
    </Teleport>
</template>
