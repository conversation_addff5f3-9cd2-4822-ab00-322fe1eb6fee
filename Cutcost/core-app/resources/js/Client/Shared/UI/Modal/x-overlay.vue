<script setup>
import { computed, onBeforeUnmount, onMounted, Teleport } from 'vue';

const props = defineProps({
  isBody: {
    type: Boolean,
    default: false,
  },
  layer: {
    type: Number,
    default: 0,
  },
});

onMounted(() => {
  if (props.isBody) {
    document.body.classList.add('overflow-hidden');
  }
});

onBeforeUnmount(() => {
  if (props.isBody) {
    document.body.classList.remove('overflow-hidden');
  }
});

const z = computed(() => {
  return props.layer ? 50 + props.layer : 40;
});

</script>

<template>
  <div>
    <div v-if="!isBody" :style="`z-index: ${z};`"
     v-bind="$attrs" class="fixed inset-0  h-full w-full bg-black/20 backdrop-blur-sm">
      <slot />
    </div>

    <div v-else>
      <Teleport to="body">
        <div :style="`z-index: ${z};`"
        class="fixed inset-0  
        h-full w-full bg-black/20 backdrop-blur-sm">
          <slot />
        </div>
      </Teleport>
    </div>
  </div>
</template>
