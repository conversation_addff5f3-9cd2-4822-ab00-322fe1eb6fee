<script setup>
import { <PERSON> } from "@inertiajs/vue3";

const props = defineProps({
    pagination: Object,
});

const stop = (e) => {
    if (props.pagination.last_page === 1) {
        e.preventDefault();
        e.stopPropagation();
    }
};
</script>

<template>
    <nav class="text-text relative flex justify-center gap-1 py-4" v-if="pagination.last_page > 1">
        <template v-for="link in pagination.links" :key="link.label">
            <Link preserve-scroll :href="link.url ?? ''" v-html="link.label" @click="stop"
                class="flex rounded-lg bg-white px-4 py-2 text-sm font-semibold text-black shadow-lg transition-all hover:translate-[1px]"
                :class="{
                    'bg-primary !text-secondary': link.active,
                    'opacity-50': !link.url,
                    'hover:bg-slate-200': link.url,
                }" />
        </template>
    </nav>
</template>
