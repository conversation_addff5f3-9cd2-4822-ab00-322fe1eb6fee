<script setup>
defineProps({
  title: String,
  value: Number,
});
</script>

<template>
  <div
    class="flex items-center justify-between gap-4 rounded-2xl bg-gradient-to-br from-white/90 to-gray-100/80 dark:from-bg-dark dark:to-dark-surface text text-text dark: px-6 py-4 shadow-sm border border-muted  backdrop-blur-sm hover:shadow-md transition-all duration-300 ease-out"
  >
    <div class="flex flex-col">
      <div class="text-sm text-gray-500 dark:text-gray-400 font-medium">
        <slot />
      </div>
      <div class="text-lg font-semibold tracking-tight">
        {{ title }}
      </div>
    </div>

    <div
      class="text-3xl font-semibold text-gray-900 dark:text-gray-100 select-none"
    >
      {{ value ?? 0 }}
    </div>
  </div>
</template>
