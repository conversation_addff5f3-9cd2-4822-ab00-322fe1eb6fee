<script setup>
import { computed, onMounted, ref, inject, watch } from 'vue';
import { useGlobalStore, useHelpers } from "@s";
import { router } from "@inertiajs/vue3";

const route = inject("route");
const { debounce } = useHelpers();

const global = useGlobalStore();
const search = ref('');

onMounted(() => {
  const params = new URLSearchParams(window.location.search);
  search.value = params.get('search') ?? '';
});

const submitSearch = debounce(() => {
  router.get(global.page.url, { search: search.value }, {
    preserveState: true,
    preserveScroll: true,
    replace: true,
  });
}, 500);

// Props
const props = defineProps({
  paginator: {
    type: Object,
    required: true
  },
  recursive: {
    type: Boolean,
    default: false
  },
  translate: {
    type: Boolean,
    default: false
  },
  hidden: {
    type: Array,
    default: () => []
  },
  noSearch: {
    type: Boolean,
    default: false
  },
  nothingText: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  routeName: {
    type: String,
    default: ''
  }
});

const data = ref(props.paginator.data);

const headers = computed(() => {
  if (!data.value.length) return [];

  const locale = global.currentLocale ?? 'en';
  function extractHeaders(obj, prefix = '') {
    const keys = [];
    for (const key in obj) {
      const val = obj[key];
      if (!props.recursive || typeof val !== 'object' || Array.isArray(val)) {
        if (props.translate && typeof val === 'object') {
          keys.push({ key: `${prefix}${key}`, label: key });
        } else {
          keys.push({ key: `${prefix}${key}`, label: key });
        }
      } else {
        keys.push(...extractHeaders(val, `${prefix}${key}.`));
      }
    }
    return keys;
  }

  return extractHeaders(data.value[0]);
});

// Отфильтрованные заголовки (учитывают hidden)
const visibleHeaders = computed(() =>
  headers.value.filter(h => !props.hidden.includes(h.key))
);

function getNestedValue(obj, path) {
  const parts = path.split('.');
  const value = parts.reduce((o, k) => (o && o[k] !== undefined) ? o[k] : '', obj);

  if (props.translate && typeof value === 'object' && value !== null) {
    const locale = global.currentLocale ?? 'en';
    if (value[locale]) return value[locale];
    if (value['en']) return value['en'];

    // Возвращаем первую доступную локаль
    const fallback = Object.values(value).find(v => typeof v === 'string');
    return fallback ?? '';
  }

  if (value === '1' || value === true || value === 'true')
    return '✅';
  if (value === '0' || value === false || value === 'false')
    return '❌';

  return value.toString();
}

const rowClick = (row) => {
  if (props.routeName)
    return router.get(route(props.routeName, row.slug));

}

watch(() => props.paginator.data, (val) => {
  data.value = val;
});
</script>

<template>
  <div class="rounded-lg border-border dark:border-dark-border">
    <div class="my-2" v-if="!noSearch">
      <x-input v-model="search" type="search" @input="submitSearch" :placeholder="__('Search')" class="w-full"
        clearable />
    </div>
    <div v-if="data.length > 0" class="sm:overflow-x-auto sm:max-w-full">
      <table class="w-full border-collapse sm:min-w-[600px]">
        <thead class="bg-zinc-100 dark:bg-zinc-800 border-b border-border dark:border-dark-border">
          <tr>
            <th v-for="(col, idx) in visibleHeaders" :key="col.key"
              class="px-4 py-3 text-left text-zinc-600 dark:text-zinc-300 text-sm font-semibold select-none">
              {{ __(`${col.label?.replace(/_/g, ' ')}`) }}
            </th>
            <th v-if="$slots.default" class="px-4 py-3"></th>
          </tr>
        </thead>

        <tbody>
          <tr v-for="(row, rowIndex) in data" :key="rowIndex" @click="rowClick(row)"
            class="hover:bg-blue-50 dark:hover:bg-zinc-800/50 transition-colors duration-200 cursor-pointer">
            <td v-for="col in visibleHeaders" :key="col.key"
              class="px-4 py-3 text-sm text-zinc-700 dark:text-zinc-200 break-words max-w-[120px] overflow-hidden whitespace-nowrap truncate">
              {{ __(getNestedValue(row, col.key)) }}
            </td>

            <td v-if="$slots.default" class="px-4 py-3">
              <div class="flex gap-2 items-center justify-end">
                <slot :row="row" />
              </div>
            </td>
          </tr>
        </tbody>

      </table>
    </div>


    <h1 v-else class="py-12 text-center h1 !text-muted dark:!text-dark-muted text-xl">
      {{ nothingText ? nothingText : __('Nothing') }}
    </h1>

    <div class="p-4">
      <Paginator :pagination="paginator" />
    </div>
  </div>

</template>
