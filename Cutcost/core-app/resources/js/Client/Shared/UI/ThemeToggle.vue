<script setup>
import { PhM<PERSON>, PhSun } from "@phosphor-icons/vue";
import { ref, onMounted } from "vue";
import { useGlobalStore } from "@s";

const global = useGlobalStore();

const isDark = ref(false);

onMounted(() => {
    const storedTheme = localStorage.getItem("theme");
    if (storedTheme) {
        isDark.value = storedTheme === "dark";
    } else {
        isDark.value = window.matchMedia(
            "(prefers-color-scheme: dark)",
        ).matches;
    }
    document.documentElement.classList.toggle("dark", isDark.value);
});

const toggleTheme = (theme) => {
    isDark.value = theme;
    localStorage.setItem("theme", theme ? "dark" : "light");
    document.documentElement.classList.toggle("dark", theme);
}
</script>

<template>
    <div class="cursor-pointer ">
        <x-button intent="rounded" v-show="isDark" @click="toggleTheme(false)">
            <PhSun :size="20" class="fill-bg" />
        </x-button>
        <x-button intent="rounded" v-show="!isDark" @click="toggleTheme(true)">
            <PhMoon :size="20" class="fill-bg-dark" />
        </x-button>
    </div>
</template>
