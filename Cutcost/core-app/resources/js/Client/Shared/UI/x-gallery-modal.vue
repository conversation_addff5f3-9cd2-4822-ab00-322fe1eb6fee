<script setup>
import { PhX } from "@phosphor-icons/vue";
import { onClickOutside } from "@vueuse/core";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useGlobalStore } from "@s";

const props = defineProps({
    startIndex: {
        type: [Number, null],
        default: null,
    },
    media: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits(["close"]);
const global = useGlobalStore();

const isOpen = ref(false);
const currentIndex = ref(0);
const imagesContainer = ref(null);

onClickOutside(imagesContainer, () => {
    closeExpand();
});

const closeExpand = () => {
    emit("close");
};

watch(
    () => props.startIndex,
    (val) => {
        if (val !== null && val >= 0) {
            currentIndex.value = val;
            isOpen.value = true;

            document.body.style.overflow = "hidden";
        } else {
            isOpen.value = false;
            document.body.style.overflow = "auto";
        }
    },
    { immediate: true }
);

const handleKeydown = (event) => {
    if (event.key === "Escape") {
        closeExpand();
    }
    if (event.key === "ArrowLeft") {
        currentIndex.value = (currentIndex.value - 1 + props.media.length) % props.media.length;
    }
    if (event.key === "ArrowRight") {
        currentIndex.value = (currentIndex.value + 1) % props.media.length;
    }
};

onMounted(() => {
    window.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
    window.removeEventListener("keydown", handleKeydown);
});</script>

<template>
    <Teleport to="body">
        <div v-if="isOpen">
            <x-overlay />

            <div class="fixed inset-0 z-60 flex items-center justify-center">
                <div ref="imagesContainer"
                    class="overflow-hidden rounded-lg shadow-2xl bg-black/20 backdrop-blur-sm relative">
                    <Teleport to="body">
                        <x-button @click="closeExpand" intent="rounded" class="!fixed top-5 right-5 z-100">
                            <PhX :size="28" />
                        </x-button>
                    </Teleport>

                    <x-gallery :media="media" :h="200" :start="currentIndex" max @change="currentIndex = $event" />

                    <div v-if="media.length"
                        class="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-3 rounded-full bg backdrop-blur-sm px-4 py-2 text text-sm shadow-lg border border-muted">
                        <div class="flex items-center justify-center font-semibold">
                            {{ currentIndex + 1 }}<span class="mx-1 text">/</span>{{ media.length }}
                        </div>

                        <div class="h-4 w-px bg-muted"></div>

                        <div class="truncate max-w-[180px]">
                            <a :href="global.assets + media[currentIndex]?.path" target="_blank"
                                class="hover:underline decoration-muted">
                                {{ media[currentIndex]?.created_at }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Teleport>
</template>
