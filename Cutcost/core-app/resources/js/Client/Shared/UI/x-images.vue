<script setup>
import { computed, ref } from "vue";
import { useGlobalStore, useFileDriver } from "@s";

const props = defineProps({
  media: { type: Array, default: () => [] }
});

const startIndex = ref(null);

const global = useGlobalStore();
const { handleImageError } = useFileDriver();

// grid container
const gridContainerStyle = computed(() => {
  const c = props.media?.length || 0;
  return {
    display: "grid",
    width: "100%",
    gridTemplateColumns: c === 1 ? "1fr" : c === 2 ? "1fr 1fr" : "1fr 1fr",
    gridAutoRows: c === 1 ? "auto" : "minmax(120px, 1fr)",
  };
});

// grid items layout
const gridItems = computed(() => {
  const c = props.media?.length || 0;
  if (c === 0) return [];
  if (c === 1) {
    return [
      {
        index: 0,
        style: {
          gridColumn: "1 / -1",
          gridRow: "1 / 2",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        },
      },
    ];
  }
  if (c === 2) {
    return [
      { index: 0, style: { gridColumn: "1 / 2", gridRow: "1 / 2" } },
      { index: 1, style: { gridColumn: "2 / 3", gridRow: "1 / 2" } },
    ];
  }
  if (c === 3) {
    return [
      { index: 0, style: { gridColumn: "1 / 2", gridRow: "1 / 3" } },
      { index: 1, style: { gridColumn: "2 / 3", gridRow: "1 / 2" } },
      { index: 2, style: { gridColumn: "2 / 3", gridRow: "2 / 3" } },
    ];
  }
  const visible = Math.min(c, 4);
  return Array.from({ length: visible }, (_, i) => ({
    index: i,
    style: {
      gridColumn: `${(i % 2) + 1} / ${(i % 2) + 2}`,
      gridRow: `${Math.floor(i / 2) + 1} / ${Math.floor(i / 2) + 2}`,
    },
    overlay: i === 3 && c > 4 ? c - 4 : 0,
  }));
});

// image class logic
const imgClass = (idx) => {
  const c = props.media?.length || 0;
  if (c === 1 && idx === 0) {
    return "max-w-full max-h-[70vh] object-contain transition-transform duration-300 select-none";
  }
  return "object-cover w-full h-full transition-transform duration-300 group-hover:scale-105 select-none";
};
</script>

<template>
  <x-gallery-modal :start-index="startIndex" :media @close="startIndex = null" />

  <div v-bind="$attrs" class="mx-auto max-w-[900px] rounded-lg overflow-hidden" :style="gridContainerStyle" role="list">
    <div v-for="(item, idx) in gridItems" :key="idx" class="group relative cursor-pointer overflow-hidden"
      :style="item.style" role="listitem" :aria-label="`Open image ${item.index + 1} of ${media.length}`">
      <img loading="lazy" :src="global.assets + media[item.index].path"
        :alt="media[item.index].alt || `photo ${item.index + 1}`" :class="imgClass(item.index)" draggable="false"
        @error="handleImageError" @click="startIndex = item.index" />
      <div v-if="item.overlay" @click="startIndex = item.index"
        class="absolute inset-0 flex items-center justify-center bg-black/50 text-2xl font-semibold text-white">
        +{{ item.overlay }}
      </div>
    </div>
  </div>
</template>
