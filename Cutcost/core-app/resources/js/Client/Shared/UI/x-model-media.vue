<script setup>
import { ref, inject, useTemplateRef } from "vue";
import { Ph<PERSON>mage, PhPlusCircle, PhTrash } from "@phosphor-icons/vue";
import { useGlobalStore, useFileDriver } from "@s";

const route = inject("route");
const global = useGlobalStore();
const { handleImageError } = useFileDriver();
const modal = useTemplateRef("modal");
// for expand image
const currentIndex = ref(null);
const askDelete = ref(false);

const props = defineProps({
  url: {
    type: String,
    default: null,
  },
  editable: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  cover: Boolean,
  static: Boolean,
});

const preview = ref(props.url);
const fileInput = ref(null);

const handleClick = () => {

  if (props.static) return;

  if (!props.editable) {
    if (preview.value) {
      currentIndex.value = 0;
    }
  } else {
    if (!global.auth()) return;

    if (preview.value) {
      modal.value.modalOpen = true;
    } else {
      fileInput.value.click();
    }
  }
};

const changeAvatar = async (e) => {
  const file = e.target.files[0];
  if (!file) return;

  preview.value = URL.createObjectURL(file);

  const formData = new FormData();
  formData.append("avatar", file);
  formData.append("action", "change");
  formData.append("column", props.cover ? "cover" : "avatar");
  formData.append("model_type", props.data.model_type);
  formData.append("model_id", props.data.model_id);

  try {
    await axios.post(route("seller.change.avatar"), formData);
  } catch (err) {
    console.error("Upload failed", err);
  } finally {
    modal.value.modalOpen = false;
  }
  global.isReload = true;
};

const deleteAvatar = async () => {
  preview.value = null;

  const formData = new FormData();
  formData.append("action", "delete");
  formData.append("column", props.cover ? "cover" : "avatar");
  formData.append("model_type", props.data.model_type);
  formData.append("model_id", props.data.model_id);

  try {
    await axios.post(route("seller.change.avatar"), formData);
  } catch (err) {
    console.error("Delete failed", err);
  } finally {
    modal.value.modalOpen = false;
    askDelete.value = false;
  }
  global.isReload = true;
};

const showPreview = () => {
  modal.value.modalOpen = false;
  currentIndex.value = 0;
};
</script>

<template>
  <div @click="handleClick" :class="[
    cover
      ? 'relative w-full max-w-4xl h-30 sm:h-48  overflow-hidden shadow-md bg-gray-100'
      : 'relative inline-flex h-24 w-24 items-center justify-center rounded-full overflow-hidden ring-2 ring-dark-text dark:ring-text',
    (preview || editable) ? 'cursor-pointer' : ''
  ]">
    <x-confirmation v-if="askDelete" :message="__('Are you sure')" @confirm="deleteAvatar"
      @cancel="askDelete = false" />
    <template v-if="preview">
      <img @error="handleImageError" :src="preview.includes('http') ? preview : global.assets + preview"
        class="h-full w-full object-cover" alt="avatar" />
    </template>

    <template v-else>
      <div class="flex h-full w-full items-center justify-center bg text">
        <template v-if="$slots.icon">
          <slot name="icon" />
        </template>
        <template v-else>
          <PhImage size="16" />
        </template>
      </div>
    </template>

    <div v-if="editable"
      class="absolute inset-0 flex flex-col items-center justify-center bg-black/60 opacity-0 hover:opacity-100 transition">
      <button class="text-white text-sm flex items-center gap-1 ">
        <PhImage size="16" /> {{ __("Change") }}
      </button>
    </div>

    <Modal ref="modal" :title="!cover ? __('Change avatar') : __('Change cover')">
      <div class="flex flex-col items-center gap-1 justify-center">
        <x-button class="w-full" intent="secondary" @click="showPreview">
          <template #icon>
            <PhImage :size="20" />
          </template>
          {{ __("Preview") }}
        </x-button>

        <x-button class="w-full" intent="secondary" @click="fileInput.click()">
          <template #icon>
            <PhPlusCircle :size="20" />
          </template>
          {{ __("Change") }}
        </x-button>
        <x-button class="w-full" intent="danger" @click="askDelete = true">
          <template #icon>
            <PhTrash :size="20" />
          </template>
          {{ __("Delete") }}
        </x-button>
      </div>
    </Modal>
    <x-gallery-modal :media="[{ path: preview }]" :start-index="currentIndex" @close="currentIndex = null" />

    <input type="file" ref="fileInput" accept="image/png,image/jpeg,image/jpg" class="hidden" @change="changeAvatar" />
  </div>
</template>
