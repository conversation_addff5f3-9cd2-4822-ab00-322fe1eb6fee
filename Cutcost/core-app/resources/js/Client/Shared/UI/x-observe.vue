<script setup>
import { ref, watch, onUnmounted } from 'vue';
import { useIntersectionObserver } from '@vueuse/core';
import { useTemplateRef } from 'vue';

const emit = defineEmits(['intersect']);
const observe = useTemplateRef('observe');

const isVisible = ref(false);
let intervalId = null;

// Intersection Observer: отслеживаем появление элемента
const { stop } = useIntersectionObserver(
  observe,
  ([entry]) => {
    if (entry.isIntersecting && !isVisible.value) {
      // Мгновенное первое срабатывание
      console.log('intersect');
      emit('intersect');
    }
    isVisible.value = entry.isIntersecting;
  },
  { threshold: 0 }
);

// Таймер, который отправляет событие каждую секунду, пока элемент виден
watch(isVisible, (visible) => {
  if (visible) {
    intervalId = setInterval(() => {
      console.log('intersect');
      emit('intersect');
    }, 1000);
  } else {
    clearInterval(intervalId);
    intervalId = null;
  }
});

onUnmounted(() => {
  clearInterval(intervalId);
  stop();
});
</script>

<template>
  <div ref="observe" class="w-full min-h-5">
    <slot />
  </div>
</template>
