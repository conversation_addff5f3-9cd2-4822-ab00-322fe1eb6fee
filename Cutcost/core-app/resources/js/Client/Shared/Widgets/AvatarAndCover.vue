<script setup>

defineProps({
    urls: {
        type: Array,
        default: null,
    },
    editable: {
        type: Boolean,
        default: false,
    },
    data: {
        type: Object,
        default: () => ({}),
    },
})

</script>

<template>
    <div class="relative">
        <x-model-media :url="urls[1]" cover :editable :data />

        <div class="flex -translate-y-1/3 absolute sm:ml-8 ml-3 !justify-start">
            <x-model-media :url="urls[0]" class="!h-20 !w-20 " :editable :data />
        </div>
        <div class="flex mt-2 justify-end gap-1 mr-2">
            <div class="h-10">
            </div>
            <slot />
        </div>
    </div>
</template>