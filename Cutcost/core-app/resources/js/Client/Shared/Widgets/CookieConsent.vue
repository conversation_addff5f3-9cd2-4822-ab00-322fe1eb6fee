<!-- SimpleCookieConsent.vue -->
<script setup>
import { ref, onMounted } from 'vue';

const STORAGE_KEY = 'cookie_consent_v4';
const showBanner = ref(false);

function readConsent() {
    return localStorage.getItem(STORAGE_KEY);
}

function acceptCookies() {
    localStorage.setItem(STORAGE_KEY, 'accepted');
    showBanner.value = false;
}

function rejectCookies() {
    window.location.href = 'https://www.yahoo.com';
}

onMounted(() => {
    if (!readConsent()) {
        showBanner.value = true;
    }
});
</script>

<template>
    <x-overlay v-if="showBanner" :layer="950"/>
    <Card v-if="showBanner"
        class="fixed bottom-5 left-1/2 -translate-x-1/2 lg:-translate-x-1/3 z-1000 max-w-3xl w-[94%] md:w-[720px] rounded-2xl px-5 py-4 backdrop-blur-md  !shadow-xl border "
        role="dialog" aria-live="polite">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
            <div class="flex-1 text-sm md:text-base text-slate-800 dark:text-slate-100">
                {{ __("We use cookies to improve your experience") }}
                {{ __("By using our services, you agree to our") }}
                <a :href="route('privacy')" intent="link" class="underline">{{ __('Privacy policy') }}</a> &
                <a :href="route('terms')" intent="link" class="underline">{{ __('Terms') }}</a>.
            </div>

            <div class="flex items-center gap-2 mt-2 md:mt-0">
                <x-button @click="rejectCookies" intent="danger">
                    {{ __("Cancel") }}
                </x-button>
                <x-button @click="acceptCookies" intent="success">
                    {{ __("Accept") }}
                </x-button>
            </div>
        </div>
    </Card>
</template>
