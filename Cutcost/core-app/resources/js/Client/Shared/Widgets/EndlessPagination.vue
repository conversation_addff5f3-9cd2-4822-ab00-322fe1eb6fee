<script setup>
import { usePagination, BottomText, useCache } from '@s';
import { onMounted, onUnmounted } from 'vue';

const { paginationData, loadNext, loadByKey, textNeeded, bottomText } = usePagination();
const { set, get } = useCache();

let loadedOneMore = false;

const props = defineProps({
    url: String,
    // type || 
    params: Object,
    noBottomText: Boolean,
});

const replace = async () => {
    paginationData.data = [];
    paginationData.cursor = null;
    loadedOneMore = false;

    await load();
};

onMounted(() => {
    if (!props.params?.noCache) {
        loadByKey(window.location.href + props.params?.type);
        const key = 'scroll' + window.location.href;

        if (get(key))
            window.scrollTo(0, get(key));

        addEventListener('scroll', () => {
            set(key, window.scrollY);
        });
    }
});

onUnmounted(() => {
    removeEventListener('scroll', () => {
        set(key, window.scrollY);
    });
});

const load = async () => {

    if (textNeeded.value) {
        if (loadedOneMore) return;
        loadedOneMore = true;
    }

    await loadNext(props.url, props.params)
};



defineExpose({
    replace,
});

</script>

<template>
    <slot name="filters" />
    <div v-bind="$attrs" v-motion-slide-top>
        <slot v-for="item in paginationData.data" :item="item" />
    </div>
    <x-observe @intersect="load" :class="{ 'my-40': bottomText.toLowerCase() === 'nothing', 'mb-12': $slots.filters }">
        <BottomText v-if="textNeeded && !noBottomText" :message="__(bottomText)" />
    </x-observe>
</template>