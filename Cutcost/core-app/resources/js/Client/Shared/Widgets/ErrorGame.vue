<template>
    <div class="w-full max-w-2xl relative">
        <header class="mb-2 flex flex-col items-center justify-between">
            <div class="my-2">
                <h1 class="format-accent text-2xl text-secondary">{{ __("Whac A Corp") }}</h1>
            </div>

            <div class="flex items-center  gap-3">
                <div class="px-3 py-1 bg rounded-xl shadow text-center">
                    <div class="text-xs text">{{ __("Score") }}</div>
                    <div class="text-lg font-bold text">{{ score }}</div>
                </div>

                <div class="px-3 py-1 bg rounded-xl shadow text-center">
                    <div class="text-xs text">{{ __("Record") }}</div>
                    <div class="text-lg font-bold text">{{ maxScore }}</div>
                </div>

                <div class="px-3 py-1 bg rounded-xl shadow text-center">
                    <div class="text-xs text">{{ __("Misses") }}</div>
                    <div class="text-lg font-bold text">{{ misses }} / {{ maxMisses }}</div>
                </div>
            </div>
        </header>

        <main class="p-3 rounded-xl shadow-md bg-transparent" role="application" aria-label="Whac A Fox">
            <div class="grid grid-cols-3 gap-3">
                <div v-for="(hole, idx) in holes" :key="hole.id" class="relative w-full aspect-square">
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-11/12 h-11/12 rounded-md flex items-center justify-center transition-all duration-150"
                            :class="hole.active ? 'bg-gradient-to-br from-orange-400 to-warning ring-2 ring-warning' : 'bg'
                                ">
                            <!-- Fox (appears when active and not yet hit) -->
                            <FoxLawyer :visible="hole.active" :hit="hole.hit" @whack="() => handleHit(idx)" />

                            <!-- Splat overlay — shows briefly when hit -->
                            <Transition enter-active-class="transition-opacity duration-200"
                                enter-from-class="opacity-0" enter-to-class="opacity-100"
                                leave-active-class="transition-opacity duration-300" leave-from-class="opacity-100"
                                leave-to-class="opacity-0">
                                <img v-if="hole.splat" :src="splatSrc" alt="splat"
                                    class="absolute w-3/4 max-w-[130px] pointer-events-none select-none" />
                            </Transition>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div v-if="gameOver" class="absolute inset-0 z-10 flex items-center justify-center bg-black/20">
            <div class="bg rounded-2xl p-6 w-11/12 max-w-md text-center shadow-2xl">
                <h2 class="text-2xl font-bold text-secondary mb-2">{{ __("Game Over") }}</h2>
                <p class="text-sm text mb-4">{{ __("Your score") }}: <span class="font-semibold text-secondary">{{ score
                        }}</span></p>
                <p class="text-sm text mb-6">{{ __("Record") }}: <span class="font-semibold text-secondary">{{ maxScore
                        }}</span></p>
                <x-button class="mx-auto" @click="restartFromOverlay">{{ __("Restart") }}</x-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onUnmounted, h, Transition, computed, onMounted } from 'vue';
import { useGlobalStore } from '../Stores/use-global-store';

const global = useGlobalStore();

const BASE_INTERVAL = 1000;
const MIN_INTERVAL = 250;
const maxMisses = 4;

const holes = ref(
    Array.from({ length: 9 }, (_, i) => ({ id: i, active: false, hit: false, splat: false }))
);
const score = ref(0);
const running = ref(false);
const misses = ref(0);
const gameOver = ref(false);

const STORAGE_KEY = 'whac_a_corp_max_score';
const maxScore = ref(Number(localStorage.getItem(STORAGE_KEY) || 0));

let spawnTimer = null;
const activeTimeouts = new Map();
const splatTimeouts = new Map();

function difficultyInterval() {
    // каждые 5 очков уменьшаем интервал
    const extra = Math.floor(score.value / 5) * 40;
    return Math.max(MIN_INTERVAL, BASE_INTERVAL - extra);
}

function scheduleNext() {
    if (!running.value || gameOver.value) return;
    spawnTimer = setTimeout(() => {
        if (!holes.value.some(h => h.active)) spawnOne();
        scheduleNext();
    }, difficultyInterval());
}

function startGame() {
    if (running.value) return;
    running.value = true;
    gameOver.value = false;
    scheduleNext();
}

function stopGame() {
    running.value = false;
    if (spawnTimer) { clearTimeout(spawnTimer); spawnTimer = null; }
}

function restartGame() {
    stopGame();
    activeTimeouts.forEach(t => clearTimeout(t));
    activeTimeouts.clear();
    splatTimeouts.forEach(t => clearTimeout(t));
    splatTimeouts.clear();
    score.value = 0;
    misses.value = 0;
    holes.value.forEach(h => { h.active = false; h.hit = false; h.splat = false; });
    gameOver.value = false;
    startGame();
}

function restartFromOverlay() {
    updateMaxScore();
    restartGame();
}

function spawnOne() {
    const free = holes.value.map((h, i) => (h.active ? -1 : i)).filter(i => i >= 0);
    if (free.length === 0) return;
    const idx = free[Math.floor(Math.random() * free.length)];
    showHole(idx);
}

function randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function showHole(idx) {
    const duration = randomBetween(600, 1100);
    holes.value[idx].active = true;
    holes.value[idx].hit = false;
    holes.value[idx].splat = false;

    const t = setTimeout(() => {
        if (!holes.value[idx].active) { activeTimeouts.delete(idx); return; }
        // лиса убежала — промах
        holes.value[idx].active = false;
        holes.value[idx].hit = false;
        activeTimeouts.delete(idx);
        misses.value += 1;
        if (misses.value >= maxMisses) {
            endGame();
        }
    }, duration);

    activeTimeouts.set(idx, t);
}

function handleHit(idx) {
    const hole = holes.value[idx];
    if (!hole.active || hole.hit || gameOver.value) return;

    hole.hit = true;
    hole.active = false;
    score.value += 1;

    const prev = activeTimeouts.get(idx);
    if (prev) { clearTimeout(prev); activeTimeouts.delete(idx); }

    // показать размаженную картинку (splat) на короткое время
    hole.splat = true;
    const st = setTimeout(() => {
        hole.splat = false;
        splatTimeouts.delete(idx);
    }, 200); // длительность отображения splat — 800ms
    splatTimeouts.set(idx, st);
}

function endGame() {
    stopGame();
    updateMaxScore();
    gameOver.value = true;
}

function updateMaxScore() {
    if (score.value > maxScore.value) {
        maxScore.value = score.value;
        try { localStorage.setItem(STORAGE_KEY, String(maxScore.value)); } catch (e) { /* ignore */ }
    }
}

onUnmounted(() => {
    stopGame();
    activeTimeouts.forEach(t => clearTimeout(t));
    activeTimeouts.clear();
    splatTimeouts.forEach(t => clearTimeout(t));
    splatTimeouts.clear();
});

onMounted(() => {
    startGame();
});

// Путь к картинкам (fox и splat)
const foxSrc = computed(() => {
    try {
        if (global && global.appUrl) {
            return new URL(global.appUrl + 'assets/images/fox.png', import.meta.url).href;
        }
    } catch (e) { }
    return new URL('assets/images/fox.png', import.meta.url).href;
});

const splatSrc = computed(() => {
    try {
        if (global && global.appUrl) {
            return new URL(global.appUrl + 'assets/images/splat.png', import.meta.url).href;
        }
    } catch (e) { }
    return new URL('assets/images/splat.png', import.meta.url).href;
});

// Вложенный компонент — отображает кнопку с лисой и анимацией появления через Transition
const FoxLawyer = {
    name: 'FoxLawyer',
    props: { visible: { type: Boolean, default: false }, hit: { type: Boolean, default: false } },
    emits: ['whack'],
    setup(props, { emit }) {
        return () => h(Transition, {
            enterActiveClass: 'transition-all duration-160 ease-out',
            enterFromClass: 'transform scale-75 opacity-0',
            enterToClass: 'transform scale-100 opacity-100',
            leaveActiveClass: 'transition-all duration-120 ease-in',
            leaveFromClass: 'opacity-100 scale-100',
            leaveToClass: 'opacity-80 opacity-0'
        }, {
            default: () => props.visible ? h('button', {
                class: 'flex items-center justify-center p-0 bg-transparent rounded focus:outline-none',
                onClick: (e) => { e.stopPropagation(); emit('whack'); },
                'aria-label': 'Ударь лису'
            }, [h('img', { src: foxSrc.value, alt: 'Corp', class: ['w-3/5 max-w-[110px] pointer-events-none'] })]) : null
        });
    }
};
</script>
