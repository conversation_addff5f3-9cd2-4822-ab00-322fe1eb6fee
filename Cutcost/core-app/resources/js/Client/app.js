import "./bootstrap";
import "../../css/app.css";
import "./settings";
import Layout from "./Shared/Layouts/Layout.vue";
import { createApp, h } from "vue";
import { createInertiaApp, Head, Link } from "@inertiajs/vue3";
import { createPinia } from "pinia";
import UI from "./Shared/UI";
import translationPlugin from "./Shared/Plugins/translations-plugin";
import { plugin as VueTippy } from "vue-tippy";
import "tippy.js/dist/tippy.css";
import "./console";
import { ZiggyVue } from "ziggy-js";
import { Ziggy } from "../ziggy.js";
import { MotionPlugin } from "@vueuse/motion";

createInertiaApp({
  title: (title) => `${title} ${title ? " | " : ""} CutCost`,
  resolve: async (name) => {
    const pages = {
      ...import.meta.glob("./Modules/*/Pages/**/*.vue"),
      ...import.meta.glob("./Modules/*/Pages/**/**/*.vue"),
      ...import.meta.glob("./Shared/Pages/**/*.vue"),
    };

    const path = Object.keys(pages).find((key) =>
      key.endsWith(`/Pages/${name}.vue`),
    );

    if (!path) throw new Error(`Page "${name}" not found`);

    const page = await pages[path]();

    page.default.layout = page.default.layout || Layout;

    return page;
  },
  setup({ el, App, props, plugin }) {
    const pinia = createPinia();

    const app = createApp({ render: () => h(App, props) })
      .use(pinia)
      .use(plugin)
      .use(VueTippy, {
        defaultProps: { theme: "cutcost" },
      })
      .use(translationPlugin)
      .use(MotionPlugin)
      .component("Head", Head)
      .use(ZiggyVue, Ziggy)
      .component("Link", Link);

    Object.entries(UI).forEach(([name, component]) => {
      app.component(name, component);
    });
    app.config.compilerOptions.comments = false;
    app.mount(el);
  },
});
