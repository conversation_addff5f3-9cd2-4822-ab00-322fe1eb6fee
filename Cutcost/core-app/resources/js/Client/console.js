(() => {
    setTimeout(() => {
        // console.clear();
        const card =
            "color: yellow; background: black; font-size: 14px; padding: 2px; border: 2px solid yellow; border-radius: 6px;";

        console.log(
            "%c % Welcome to the CutCost Console! %",
            "color: white; background-color: #d0021b; font-size: 20px; padding: 12px 16px; border-radius: 6px; font-weight: bold;",
        );
        console.log(
            "%c Serving code hotter than our discounts 🥵",
            "color: #d0021b; background-color: white; font-size: 14px; padding: 8px 12px; border-left: 4px solid #d0021b; margin-top: 4px;",
        );

        // Self-XSS Warning
        console.log(
            "%c⚠️ Warning: Do not paste code you don't understand here!",
            card,
        );

        console.log(
            "%cThis console is a developer tool. If someone told you to paste something here to get free cola or partner access, it's a scam!",
            card,
        );

        console.log(
            "%cPasting code could give attackers access to your account or private data.",
            card,
        );

    //     // game
    //     console.log(
    //         "%c🕹️ : Type %c hack('guess the password')%c in the console and guess the password!",
    //         "color: lime; background: black; font-size: 14px; padding: 8px;",
    //         "color: cyan; font-weight: bold;",
    //         "color: lime;",
    //     );

    //     // Did you think it would be easy?
    //     const L = "WW91ciBuZXR3b3JrIGlzIHlvdXIgbmV0IHdvcnRoLg==";

    //     window.hack = (apple) => {
    //         if (apple === atob(L)) {
    //             console.log(
    //                 "%c💻 Access Granted! Welcome, Seller!",
    //                 "color: black; background-color: lime; font-size: 14px; padding: 8px; border-radius: 4px;",
    //             );
    //         } else {
    //             console.log(
    //                 "%c❌ Access Denied. Try again.",
    //                 "color: red; font-size: 14px; padding: 4px;",
    //             );
    //         }
    //     };
    }, 500);
})();
