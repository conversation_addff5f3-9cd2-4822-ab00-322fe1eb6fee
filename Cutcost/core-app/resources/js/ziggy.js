const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"debugbar.openhandler":{"uri":"_debugbar\/open","methods":["GET","HEAD"]},"debugbar.clockwork":{"uri":"_debugbar\/clockwork\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"debugbar.assets.css":{"uri":"_debugbar\/assets\/stylesheets","methods":["GET","HEAD"]},"debugbar.assets.js":{"uri":"_debugbar\/assets\/javascript","methods":["GET","HEAD"]},"debugbar.cache.delete":{"uri":"_debugbar\/cache\/{key}\/{tags?}","methods":["DELETE"],"parameters":["key","tags"]},"debugbar.queries.explain":{"uri":"_debugbar\/queries\/explain","methods":["POST"]},"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"auth":{"uri":"auth","methods":["GET","HEAD"]},"email.auth":{"uri":"auth\/email","methods":["GET","HEAD"]},"provider":{"uri":"auth\/with\/{providerId}","methods":["GET","HEAD"],"parameters":["providerId"]},"provider.callback":{"uri":"auth\/provider\/callback","methods":["GET","HEAD"]},"register":{"uri":"auth\/register","methods":["POST"]},"login":{"uri":"auth\/login","methods":["POST"]},"logout":{"uri":"auth\/logout","methods":["POST"]},"rate":{"uri":"view\/{model}\/{id}\/rate","methods":["PATCH"],"parameters":["model","id"]},"bookmark":{"uri":"view\/{model}\/{id}\/bookmark","methods":["PATCH"],"parameters":["model","id"]},"report":{"uri":"report\/send","methods":["POST"]},"media.upload":{"uri":"media\/upload-temp","methods":["POST"]},"media.destroy":{"uri":"media\/delete","methods":["DELETE"]},"media.sort":{"uri":"media\/sort","methods":["POST"]},"redirect":{"uri":"alert\/redirect","methods":["GET","HEAD"]},"set.locale":{"uri":"set\/locale\/city\/{locale}","methods":["POST"],"parameters":["locale"]},"partner.coupons.create":{"uri":"partner\/coupons\/{company}\/create","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.coupons.edit":{"uri":"partner\/coupons\/{coupon}\/edit\/{company}","methods":["GET","HEAD"],"parameters":["coupon","company"],"bindings":{"coupon":"slug","company":"slug"}},"partner.coupons.destroy":{"uri":"partner\/coupons\/{coupon}\/delete","methods":["DELETE"],"parameters":["coupon"],"bindings":{"coupon":"slug"}},"partner.coupons.update":{"uri":"partner\/coupons\/{coupon}\/update","methods":["PUT"],"parameters":["coupon"],"bindings":{"coupon":"slug"}},"partner.coupons.store":{"uri":"partner\/coupons\/{company}\/store","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.coupon.update.locations":{"uri":"partner\/coupon\/update\/locations\/{coupon}","methods":["PATCH"],"parameters":["coupon"],"bindings":{"coupon":"slug"}},"partner.locations.index":{"uri":"partner\/locations\/{company}\/all","methods":["GET","HEAD"],"parameters":["company"]},"partner.locations.create":{"uri":"partner\/locations\/{company}\/create","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.locations.store":{"uri":"partner\/locations\/{company}\/store","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.locations.edit":{"uri":"partner\/locations\/{location}\/edit\/{company}","methods":["GET","HEAD"],"parameters":["location","company"],"bindings":{"location":"slug","company":"slug"}},"partner.locations.update":{"uri":"partner\/locations\/{location}","methods":["PUT"],"parameters":["location"],"bindings":{"location":"slug"}},"partner.locations.destroy":{"uri":"partner\/locations\/{location}","methods":["DELETE"],"parameters":["location"],"bindings":{"location":"slug"}},"partner.location.update.settings":{"uri":"partner\/location\/settings\/update\/{location}","methods":["PATCH"],"parameters":["location"],"bindings":{"location":"slug"}},"partner.location.update.coupons":{"uri":"partner\/location\/update\/coupons\/{location}","methods":["PATCH"],"parameters":["location"],"bindings":{"location":"slug"}},"partner.companies.create":{"uri":"partner\/companies\/create","methods":["GET","HEAD"]},"partner.companies.store":{"uri":"partner\/companies","methods":["POST"]},"partner.companies.edit":{"uri":"partner\/companies\/{company}\/edit","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.companies.update":{"uri":"partner\/companies\/{company}","methods":["PUT","PATCH"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.companies.destroy":{"uri":"partner\/companies\/{company}","methods":["DELETE"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.business.dashboard":{"uri":"partner\/business\/dashboard","methods":["GET","HEAD"]},"partner.referral.dashboard":{"uri":"partner\/referral\/dashboard","methods":["GET","HEAD"]},"partner.statistic.index":{"uri":"partner\/business\/statistic","methods":["GET","HEAD"]},"partner.scan.index":{"uri":"partner\/scan\/{company}","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.scan.check":{"uri":"partner\/scan\/check\/{company}","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.scan.accept":{"uri":"partner\/scan\/accept\/{company}","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.articles.index":{"uri":"partner\/companies\/{company}\/articles","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.articles.create":{"uri":"partner\/companies\/{company}\/articles\/create","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.articles.edit":{"uri":"partner\/companies\/{company}\/articles\/{article}\/edit","methods":["GET","HEAD"],"parameters":["company","article"],"bindings":{"company":"slug","article":"slug"}},"partner.articles.store":{"uri":"partner\/companies\/{company}\/articles","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.articles.update":{"uri":"partner\/companies\/articles\/{article}","methods":["PUT"],"parameters":["article"],"bindings":{"article":"slug"}},"partner.articles.destroy":{"uri":"partner\/companies\/articles\/{article}","methods":["DELETE"],"parameters":["article"],"bindings":{"article":"slug"}},"partner.bot.index":{"uri":"partner\/bot\/{company?}","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"partner.bot.update":{"uri":"partner\/bot\/{company?}","methods":["PUT"],"parameters":["company"],"bindings":{"company":"slug"}},"coupon.view":{"uri":"view\/{coupon}","methods":["GET","HEAD"],"parameters":["coupon"],"bindings":{"coupon":"slug"}},"location.show":{"uri":"view\/location\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"profile":{"uri":"profile","methods":["GET","HEAD"]},"plans":{"uri":"plans","methods":["GET","HEAD"]},"show.post":{"uri":"view\/post\/{post}","methods":["GET","HEAD"],"parameters":["post"],"bindings":{"post":"slug"}},"company.show":{"uri":"company\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"map.index":{"uri":"locations","methods":["GET","HEAD"]},"news":{"uri":"news","methods":["GET","HEAD"]},"news.show":{"uri":"news\/{article}","methods":["GET","HEAD"],"parameters":["article"],"bindings":{"article":"slug"}},"feed":{"uri":"feed","methods":["GET","HEAD"]},"help.index":{"uri":"help","methods":["GET","HEAD"]},"help.faq":{"uri":"help\/faq","methods":["GET","HEAD"]},"help.contact":{"uri":"help\/contact","methods":["GET","HEAD"]},"help.search":{"uri":"help\/search","methods":["GET","HEAD"]},"help.article":{"uri":"help\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"advanced.search":{"uri":"search\/advanced","methods":["GET","HEAD"]},"search.city":{"uri":"search\/city","methods":["POST"]},"search.users":{"uri":"header\/search\/users","methods":["POST"]},"search.coupons":{"uri":"header\/search\/coupons","methods":["POST"]},"load.paginated":{"uri":"load\/paginated","methods":["GET","HEAD"]},"load.coupons":{"uri":"load\/coupons","methods":["GET","HEAD"]},"terms":{"uri":"terms","methods":["GET","HEAD"]},"privacy":{"uri":"privacy","methods":["GET","HEAD"]},"seller.settings":{"uri":"settings","methods":["GET","HEAD"]},"seller.settings.details":{"uri":"settings\/update\/personal","methods":["PUT"]},"seller.settings.privacy-preferences":{"uri":"settings\/update\/privacy-preferences","methods":["PUT"]},"seller.settings.lang-and-region":{"uri":"settings\/update\/lang-and-region","methods":["PUT"]},"seller.connections":{"uri":"user\/{user}\/s\/{type}","methods":["GET","HEAD"],"parameters":["user","type"],"bindings":{"user":"nickname"}},"seller.businesses":{"uri":"user\/{user}\/businesses","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"nickname"}},"seller.auth.session.destroy":{"uri":"session\/{sessionId}","methods":["DELETE"],"parameters":["sessionId"]},"seller.auth.user.destroy":{"uri":"delete","methods":["DELETE"]},"seller.send.friend.request":{"uri":"friend\/{user}","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"seller.accept.friend.request":{"uri":"friend\/{user}\/accept","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"seller.delete.friend":{"uri":"friend\/{user}\/delete","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"seller.cancel.request":{"uri":"friend\/{user}\/cancel\/request","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"seller.post.create":{"uri":"profile\/post\/create-post","methods":["GET","HEAD"]},"seller.post.edit":{"uri":"profile\/post\/edit\/{post}","methods":["GET","HEAD"],"parameters":["post"],"bindings":{"post":"slug"}},"seller.post.store":{"uri":"profile\/post\/post-post","methods":["POST"]},"seller.post.update":{"uri":"profile\/post\/upate-post\/{post}","methods":["PUT"],"parameters":["post"],"bindings":{"post":"slug"}},"seller.post.destroy":{"uri":"profile\/post\/delete-post\/{post}","methods":["DELETE"],"parameters":["post"],"bindings":{"post":"slug"}},"seller.users.list":{"uri":"users\/list\/{type}\/{model}{id}","methods":["GET","HEAD"],"parameters":["type","model","id"]},"seller.my-coupons":{"uri":"my-coupons","methods":["GET","HEAD"]},"seller.saved":{"uri":"saved","methods":["GET","HEAD"]},"seller.companies.index":{"uri":"my-companies","methods":["GET","HEAD"]},"seller.notifications":{"uri":"notifications","methods":["GET","HEAD"]},"seller.notifications.destroy":{"uri":"notifications\/{id}","methods":["DELETE"],"parameters":["id"]},"seller.coupon.scan":{"uri":"view\/{coupon}\/scan","methods":["GET","HEAD"],"parameters":["coupon"],"bindings":{"coupon":"slug"}},"seller.company.subscribe":{"uri":"company\/subscribe\/{company}","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"seller.company.unsubscribe":{"uri":"company\/unsubscribe\/{company}","methods":["POST"],"parameters":["company"],"bindings":{"company":"slug"}},"seller.partner.become-a-partner":{"uri":"p\/become-a-partner","methods":["GET","HEAD"]},"seller.partner.apply":{"uri":"p\/apply","methods":["POST"]},"seller.partner.subscribe":{"uri":"p\/subscribe","methods":["POST"]},"seller.change.avatar":{"uri":"change\/avatar","methods":["POST"]},"seller.comment.index":{"uri":"comment\/{class}\/{id}","methods":["GET","HEAD"],"parameters":["class","id"]},"seller.comment.store":{"uri":"comment\/store\/{class}\/{id}","methods":["POST"],"parameters":["class","id"]},"seller.comment.show":{"uri":"comment\/{comment}","methods":["GET","HEAD"],"parameters":["comment"]},"seller.comment.update":{"uri":"comment\/{comment}","methods":["PUT"],"parameters":["comment"],"bindings":{"comment":"id"}},"seller.comment.destroy":{"uri":"comment\/{comment}","methods":["DELETE"],"parameters":["comment"],"bindings":{"comment":"id"}},"game.costsaber":{"uri":"costsaber\/costsaber","methods":["GET","HEAD"]},"messenger.index":{"uri":"cuttalk","methods":["GET","HEAD"]},"messenger.chat":{"uri":"cuttalk\/c.htm","methods":["GET","HEAD"]},"messenger.groups.create":{"uri":"cuttalk\/groups\/create","methods":["POST"]},"messenger.groups.update.group.name":{"uri":"cuttalk\/groups\/update\/group\/{chat}\/name","methods":["PUT"],"parameters":["chat"],"bindings":{"chat":"id"}},"messenger.groups.add.user":{"uri":"cuttalk\/groups\/add\/group\/{chat}","methods":["PATCH"],"parameters":["chat"],"bindings":{"chat":"id"}},"messenger.groups.remove.user":{"uri":"cuttalk\/groups\/delete\/group\/{chat}\/{user}","methods":["DELETE"],"parameters":["chat","user"],"bindings":{"chat":"id","user":"id"}},"messenger.api.chats":{"uri":"cuttalk\/api\/chats\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"messenger.api.chat.paginate":{"uri":"cuttalk\/api\/chats\/paginate\/{chat}\/{cursor}","methods":["POST"],"parameters":["chat","cursor"],"bindings":{"chat":"id"}},"messenger.api.chat.block":{"uri":"cuttalk\/api\/chat\/{chat}\/block","methods":["PATCH"],"parameters":["chat"],"bindings":{"chat":"id"}},"messenger.api.chat.unblock":{"uri":"cuttalk\/api\/chat\/{chat}\/unblock","methods":["PATCH"],"parameters":["chat"],"bindings":{"chat":"id"}},"messenger.api.chat.quit":{"uri":"cuttalk\/api\/chat\/{chat}\/quit","methods":["DELETE"],"parameters":["chat"],"bindings":{"chat":"id"}},"messenger.api.message.send":{"uri":"cuttalk\/message\/{chat}\/send","methods":["POST"],"parameters":["chat"],"bindings":{"chat":"id"}},"messenger.api.message.destroy":{"uri":"cuttalk\/message\/{chat}\/{message}\/delete","methods":["DELETE"],"parameters":["chat","message"],"bindings":{"chat":"id","message":"id"}},"messenger.api.message.update":{"uri":"cuttalk\/message\/{chat}\/{message}\/update","methods":["PUT"],"parameters":["chat","message"],"bindings":{"chat":"id","message":"id"}},"messenger.api.messages.read":{"uri":"cuttalk\/message\/read\/{message}","methods":["PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"messenger.api.company.info":{"uri":"cuttalk\/company\/{company}\/info","methods":["GET","HEAD"],"parameters":["company"],"bindings":{"company":"slug"}},"wallet.cuts":{"uri":"cuts","methods":["GET","HEAD"]},"wallet.webhook.stripe":{"uri":"webhook\/stripe","methods":["POST"]},"wallet.index":{"uri":"wallet","methods":["GET","HEAD"]},"wallet.plans":{"uri":"wallet\/plans","methods":["GET","HEAD"]},"wallet.plans.purchase":{"uri":"wallet\/plans\/{plan}\/purchase","methods":["POST"],"parameters":["plan"],"bindings":{"plan":"id"}},"wallet.verification":{"uri":"wallet\/verification","methods":["GET","HEAD"]},"wallet.verification.process":{"uri":"wallet\/verification\/process","methods":["POST"]},"wallet.verification.return":{"uri":"wallet\/verification\/return","methods":["GET","HEAD"]},"wallet.transactions":{"uri":"wallet\/transactions","methods":["GET","HEAD"]},"wallet.setup-intent":{"uri":"wallet\/setup-intent","methods":["POST"]},"wallet.payment-methods.add":{"uri":"wallet\/payment-methods","methods":["POST"]},"wallet.payment-methods.remove":{"uri":"wallet\/payment-methods\/{paymentMethod}","methods":["DELETE"],"parameters":["paymentMethod"],"bindings":{"paymentMethod":"id"}},"wallet.payment-methods.default":{"uri":"wallet\/payment-methods\/{paymentMethod}\/default","methods":["PATCH"],"parameters":["paymentMethod"],"bindings":{"paymentMethod":"id"}},"wallet.subscription.cancel":{"uri":"wallet\/subscription","methods":["DELETE"]},"wallet.purchase.return":{"uri":"wallet\/purchase\/return","methods":["GET","HEAD"]},"wallet.api.balance":{"uri":"api\/wallet\/balance","methods":["GET","HEAD"]},"wallet.api.spend-cuts":{"uri":"api\/wallet\/spend-cuts","methods":["POST"]},"api.post.get":{"uri":"api\/post\/{postSlug}","methods":["GET","HEAD"],"parameters":["postSlug"]},"api.city.get":{"uri":"api\/city\/{cityId}","methods":["GET","HEAD"],"parameters":["cityId"]},"api.translate":{"uri":"api\/translate","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
