{"before_scan": "<PERSON><PERSON> dem Scannen", "after_scan": "Nach dem <PERSON>annen", "custom_event": "Benutzerdefiniertes <PERSON>ignis", "valid_until": "Gültig bis", "user_scan_limits": "Scan-Limits für den Benutzer", "users_scan_limits": "Scan-Limits für alle Benutzer", "social_media_subscription": "Sozialen Medien Abonnement", "custom_condition": "Benutzerdefinierte Bedingung", "product_gift": "Produktgeschenk", "descriptions": {"user_scan_limits": "Der Benutzer kann den Gutschein nur X Mal scannen", "valid_until": "Der Gutschein ist bis zu diesem Datum gültig", "custom_condition": "Benutzerdefinierte Bedingung", "users_scan_limits": "Alle Benutzer können den Gutschein nur X Mal scannen", "social_media_subscription": "Der Benutzer muss Ihnen in den sozialen Medien folgen.", "product_gift": "Der Benutzer erhält ein Produkt als Geschenk", "before_scan": "<PERSON><PERSON> dem Scannen", "after_scan": "Nach dem <PERSON>annen", "custom_event": "Benutzerdefiniertes <PERSON>ignis"}, "validation": {"invalid_date": "Ungültiges Datum", "date_must_be_after_today": "Das Datum muss nach dem heutigen Tag liegen", "required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalid_number": "Ungültige Zahl", "invalid_url": "Ungültige URL", "no_conditions": "<PERSON>s muss mindestens eine Bedingung hinzugefügt werden.", "numeric": "<PERSON><PERSON> muss eine Zahl und nicht negativ sein", "url": "<PERSON><PERSON> muss eine gültige URL enthalten", "no_blueprint_data": "<PERSON><PERSON>", "no_value_for": "<PERSON><PERSON>", "no_events": "Es muss mindestens ein Ereignis hinzugefügt werden.", "only_one_event": "<PERSON><PERSON> ein <PERSON><PERSON><PERSON> erlaubt"}}