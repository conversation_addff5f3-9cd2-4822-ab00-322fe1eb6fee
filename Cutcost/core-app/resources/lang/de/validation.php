<?php

return [
    "accepted" => "Sie müssen :attribute akzeptieren.",
    "accepted_if" => "Sie müssen :attribute akzeptieren, wenn :other den Wert :value enthält.",
    "active_url" => "Der Wert des Feldes :attribute muss eine gültige URL sein.",
    "after" => "Der Wert des Feldes :attribute muss ein Datum nach :date sein.",
    "after_or_equal" => "Der Wert des Feldes :attribute muss ein Datum nach oder gleich :date sein.",
    "alpha" => "Der Wert des Feldes :attribute darf nur Buchstaben enthalten.",
    "alpha_dash" => "Der Wert des Feldes :attribute darf nur Buchstaben, Zahlen, Bindestriche und Unterstriche enthalten.",
    "alpha_num" => "Der Wert des Feldes :attribute darf nur Buchstaben und Zahlen enthalten.",
    "any_of" => "Der Wert des Feldes :attribute wurde in der Liste der erlaubten Werte nicht gefunden.",
    "array" => "Der Wert des Feldes :attribute muss ein Array sein.",
    "ascii" => "Der Wert des Feldes :attribute darf nur einbytige alphanumerische Zeichen enthalten.",
    "attached" => "Der Inhalt des Feldes :attribute ist bereits angehängt.",
    "before" => "Der Wert des Feldes :attribute muss ein Datum vor :date sein.",
    "before_or_equal" => "Der Wert des Feldes :attribute muss ein Datum vor oder gleich :date sein.",
    "between" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss zwischen :min und :max liegen.",
        "file" => "Die Dateigröße im Feld :attribute muss zwischen :min und :max KB liegen.",
        "numeric" => "Der Wert des Feldes :attribute muss zwischen :min und :max liegen.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss zwischen :min und :max liegen.",
    ],
    "boolean" => "Der Wert des Feldes :attribute muss ein boolescher Typ sein.",
    "can" => "Der Wert des Feldes :attribute muss autorisiert sein.",
    "confirmed" => "Der Wert des Feldes :attribute stimmt nicht mit der Bestätigung überein.",
    "contains" => "Im Feld :attribute fehlt ein erforderlicher Wert.",
    "current_password" => "Falsches Passwort.",
    "date" => "Der Wert des Feldes :attribute muss ein gültiges Datum sein.",
    "date_equals" => "Der Wert des Feldes :attribute muss ein Datum sein, das gleich :date ist.",
    "date_format" => "Der Wert des Feldes :attribute muss dem Datumsformat :format entsprechen.",
    "decimal" => "Der Wert des Feldes :attribute muss :decimal Dezimalstellen enthalten.",
    "declined" => "Der Wert des Feldes :attribute muss abgelehnt werden.",
    "declined_if" => "Der Wert des Feldes :attribute muss abgelehnt werden, wenn :other den Wert :value enthält.",
    "different" => "Die Werte der Felder :attribute und :other müssen unterschiedlich sein.",
    "digits" => "Die Anzahl der Zeichen im Feld :attribute muss :digits betragen.",
    "digits_between" => "Die Anzahl der Zeichen im Feld :attribute muss zwischen :min und :max liegen.",
    "dimensions" => "Das im Feld :attribute angegebene Bild hat ungültige Abmessungen.",
    "distinct" => "Die Elemente im Wert des Feldes :attribute dürfen sich nicht wiederholen.",
    "doesnt_end_with" => "Der Wert des Feldes :attribute darf nicht mit einem der folgenden Werte enden: :values.",
    "doesnt_start_with" => "Der Wert des Feldes :attribute darf nicht mit einem der folgenden Werte beginnen: :values.",
    "email" => "Der Wert des Feldes :attribute muss eine gültige E-Mail-Adresse sein.",
    "ends_with" => "Der Wert des Feldes :attribute muss mit einem der folgenden Werte enden: :values.",
    "enum" => "Der Wert des Feldes :attribute fehlt in der Liste der erlaubten Werte.",
    "exists" => "Der Wert des Feldes :attribute existiert nicht.",
    "extensions" => "Die Datei im Feld :attribute muss eine der folgenden Erweiterungen haben: :values.",
    "failed" => "Falscher Benutzername oder Passwort.",
    "file" => "Im Feld :attribute muss eine Datei angegeben werden.",
    "filled" => "Der Wert des Feldes :attribute ist erforderlich.",
    "gt" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss größer als :value sein.",
        "file" => "Die Dateigröße im Feld :attribute muss größer als :value KB sein.",
        "numeric" => "Der Wert des Feldes :attribute muss größer als :value sein.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss größer als :value sein.",
    ],
    "gte" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss :value oder mehr betragen.",
        "file" => "Die Dateigröße im Feld :attribute muss :value KB oder mehr betragen.",
        "numeric" => "Der Wert des Feldes :attribute muss :value oder größer sein.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss :value oder mehr betragen.",
    ],
    "hex_color" => "Der Wert des Feldes :attribute muss ein gültiger HEX-Farbcode sein.",
    "image" => "Die im Feld :attribute angegebene Datei muss ein Bild sein.",
    "in" => "Der Wert des Feldes :attribute fehlt in der Liste der erlaubten Werte.",
    "in_array" => "Der Wert des Feldes :attribute muss im Feld :other angegeben sein.",
    "in_array_keys" => "Das Array im Wert des Feldes :attribute muss mindestens einen der folgenden Schlüssel enthalten: :values.",
    "integer" => "Der Wert des Feldes :attribute muss eine Ganzzahl sein.",
    "ip" => "Der Wert des Feldes :attribute muss eine gültige IP-Adresse sein.",
    "ipv4" => "Der Wert des Feldes :attribute muss eine gültige IPv4-Adresse sein.",
    "ipv6" => "Der Wert des Feldes :attribute muss eine gültige IPv6-Adresse sein.",
    "json" => "Der Wert des Feldes :attribute muss eine JSON-Zeichenkette sein.",
    "list" => "Der Wert des Feldes :attribute muss eine Liste sein.",
    "lowercase" => "Der Wert des Feldes :attribute muss in Kleinbuchstaben sein.",
    "lt" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss kleiner als :value sein.",
        "file" => "Die Dateigröße im Feld :attribute muss kleiner als :value KB sein.",
        "numeric" => "Der Wert des Feldes :attribute muss kleiner als :value sein.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss kleiner als :value sein.",
    ],
    "lte" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss :value oder weniger betragen.",
        "file" => "Die Dateigröße im Feld :attribute muss :value KB oder weniger betragen.",
        "numeric" => "Der Wert des Feldes :attribute muss gleich oder kleiner als :value sein.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss :value oder weniger betragen.",
    ],
    "mac_address" => "Der Wert des Feldes :attribute muss eine gültige MAC-Adresse sein.",
    "max" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute darf :max nicht überschreiten.",
        "file" => "Die Dateigröße im Feld :attribute darf :max KB nicht überschreiten.",
        "numeric" => "Der Wert des Feldes :attribute darf :max nicht überschreiten.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute darf :max nicht überschreiten.",
    ],
    "max_digits" => "Der Wert des Feldes :attribute darf nicht mehr als :max Ziffern enthalten.",
    "mimes" => "Die Datei im Feld :attribute muss einer der folgenden Typen sein: :values.",
    "mimetypes" => "Die Datei im Feld :attribute muss einer der folgenden Typen sein: :values.",
    "min" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss mindestens :min betragen.",
        "file" => "Die Dateigröße im Feld :attribute muss mindestens :min KB betragen.",
        "numeric" => "Der Wert des Feldes :attribute muss mindestens :min sein.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss mindestens :min betragen.",
    ],
    "min_digits" => "Der Wert des Feldes :attribute muss mindestens :min Ziffern enthalten.",
    "missing" => "Der Wert des Feldes :attribute muss fehlen.",
    "missing_if" => "Der Wert des Feldes :attribute muss fehlen, wenn :other den Wert :value enthält.",
    "missing_unless" => "Der Wert des Feldes :attribute muss fehlen, wenn :other nicht den Wert :value enthält.",
    "missing_with" => "Der Wert des Feldes :attribute muss fehlen, wenn :values angegeben ist.",
    "missing_with_all" => "Der Wert des Feldes :attribute muss fehlen, wenn alle :values angegeben sind.",
    "multiple_of" => "Der Wert des Feldes :attribute muss ein Vielfaches von :value sein.",
    "next" => "Weiter &raquo;",
    "not_in" => "Der Wert des Feldes :attribute befindet sich in der Liste der verbotenen Werte.",
    "not_regex" => "Der Wert des Feldes :attribute hat ein ungültiges Format.",
    "numeric" => "Der Wert des Feldes :attribute muss eine Zahl sein.",
    "password" => "Falsches Passwort.",
    "password" => [
        "letters" => "Der Wert des Feldes :attribute muss mindestens einen Buchstaben enthalten.",
        "mixed" => "Der Wert des Feldes :attribute muss mindestens einen Groß- und einen Kleinbuchstaben enthalten.",
        "numbers" => "Der Wert des Feldes :attribute muss mindestens eine Zahl enthalten.",
        "symbols" => "Der Wert des Feldes :attribute muss mindestens ein Symbol enthalten.",
        "uncompromised" => "Der Wert des Feldes :attribute wurde in geleakten Daten gefunden. Bitte wählen Sie einen anderen Wert für :attribute.",
    ],
    "present" => "Der Wert des Feldes :attribute muss vorhanden sein.",
    "present_if" => "Der Wert des Feldes :attribute muss vorhanden sein, wenn :other den Wert :value enthält.",
    "present_unless" => "Der Wert des Feldes :attribute muss vorhanden sein, es sei denn, :other enthält nicht den Wert :value.",
    "present_with" => "Der Wert des Feldes :attribute muss vorhanden sein, wenn eines der :values vorhanden ist.",
    "present_with_all" => "Der Wert des Feldes :attribute muss vorhanden sein, wenn alle :values vorhanden sind.",
    "previous" => "&laquo; Zurück",
    "prohibited" => "Der Wert des Feldes :attribute ist verboten.",
    "prohibited_if" => "Der Wert des Feldes :attribute ist verboten, wenn :other den Wert :value enthält.",
    "prohibited_if_accepted" => "Der Wert des Feldes :attribute ist verboten, wenn :other akzeptiert ist.",
    "prohibited_if_declined" => "Der Wert des Feldes :attribute ist verboten, wenn :other abgelehnt ist.",
    "prohibited_unless" => "Der Wert des Feldes :attribute ist verboten, es sei denn, :other ist in :values enthalten.",
    "prohibits" => "Der Wert des Feldes :attribute verbietet die Anwesenheit von :other.",
    "regex" => "Der Wert des Feldes :attribute hat ein ungültiges Format.",
    "relatable" => "Der Wert des Feldes :attribute kann nicht mit dieser Ressource verknüpft werden.",
    "required" => "Das Feld :attribute ist erforderlich.",
    "required_array_keys" => "Das Array im Feld :attribute muss zwingend die Schlüssel :values enthalten.",
    "required_if" => "Das Feld :attribute ist erforderlich, wenn :other den Wert :value enthält.",
    "required_if_accepted" => "Das Feld :attribute ist erforderlich, wenn :other akzeptiert ist.",
    "required_if_declined" => "Das Feld :attribute ist erforderlich, wenn :other abgelehnt ist.",
    "required_unless" => "Das Feld :attribute ist erforderlich, es sei denn, :other enthält nicht :values.",
    "required_with" => "Das Feld :attribute ist erforderlich, wenn :values angegeben ist.",
    "required_with_all" => "Das Feld :attribute ist erforderlich, wenn alle :values angegeben sind.",
    "required_without" => "Das Feld :attribute ist erforderlich, wenn :values nicht angegeben ist.",
    "required_without_all" => "Das Feld :attribute ist erforderlich, wenn keines der :values angegeben ist.",
    "reset" => "Ihr Passwort wurde zurückgesetzt.",
    "same" => "Die Werte der Felder :attribute und :other müssen übereinstimmen.",
    "sent" => "Der Link zum Zurücksetzen des Passworts wurde gesendet.",
    "size" => [
        "array" => "Die Anzahl der Elemente im Feld :attribute muss :size betragen.",
        "file" => "Die Dateigröße im Feld :attribute muss :size KB betragen.",
        "numeric" => "Der Wert des Feldes :attribute muss :size sein.",
        "string" => "Die Anzahl der Zeichen im Feld :attribute muss :size betragen.",
    ],
    "starts_with" => "Das Feld :attribute muss mit einem der folgenden Werte beginnen: :values.",
    "string" => "Der Wert des Feldes :attribute muss eine Zeichenkette sein.",
    "throttle" => "Zu viele Anmeldeversuche. Bitte versuchen Sie es in :seconds Sekunden erneut.",
    "throttled" => "Bitte warten Sie, bevor Sie es erneut versuchen.",
    "timezone" => "Der Wert des Feldes :attribute muss eine gültige Zeitzone sein.",
    "token" => "Ungültiger Code zum Zurücksetzen des Passworts.",
    "ulid" => "Der Wert des Feldes :attribute muss ein gültiger ULID sein.",
    "unique" => "Der Wert des Feldes :attribute existiert bereits.",
    "uploaded" => "Das Hochladen der Datei aus dem Feld :attribute ist fehlgeschlagen.",
    "uppercase" => "Der Wert des Feldes :attribute muss in Großbuchstaben sein.",
    "url" => "Der Wert des Feldes :attribute ist keine gültige URL oder hat ein ungültiges Format.",
    "user" => "Es konnte kein Benutzer mit der angegebenen E-Mail-Adresse gefunden werden.",
    "uuid" => "Der Wert des Feldes :attribute muss ein gültiger UUID sein.",

    'custom' => [
        'discount' => [
            'invalid_percentage' => 'Der Rabatt in Prozent muss zwischen 0 und 100 liegen.',
            'invalid_amount' => 'Der Rabattbetrag darf den Preis im Geschäft nicht überschreiten.',
        ],
        'name' => [
            'regex' => 'Der Name muss mit einem Buchstaben beginnen und darf nur Buchstaben, Zahlen und Leerzeichen enthalten.',
        ],
        'working_hours' => [
            'required_full_week' => 'Füllen Sie den Zeitplan für alle Tage der Woche aus.',
            'invalid_format' => 'Ungültiges Format für den Zeitplan für :day.',
            'missing_field' => 'Das Feld :field fehlt oder ist für :day ungültig.',
            'invalid_field' => 'Das Feld :field ist für :day ungültig.',
            'time_required' => 'Geben Sie die Anfangs- und Endzeit für :day an.',
            'invalid_range' => 'Die Endzeit muss nach der Startzeit für :day liegen.',
            'invalid_breaks' => 'Das Feld breaks muss ein Array für :day sein.',
        ],
        'socials' => [
            'regex' => 'Der Social-Media-Link muss im Format sein: facebook.com, twitter.com, instagram.com, linkedin.com, youtube.com, tiktok.com, snapchat.com, pinterest.com, reddit.com oder whatsapp.com.',
        ],
    ],
    'attributes' => [
        'name' => 'Name',
        'working_hours' => 'Arbeitszeiten',
        'working_hours_day' => 'Arbeitszeiten für :day',
    ],
];
