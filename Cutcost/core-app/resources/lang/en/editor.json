{"before_scan": "Before scanning", "after_scan": "After scanning", "custom_event": "Custom event", "valid_until": "Valid until", "user_scan_limits": "User scan limits", "users_scan_limits": "Scan limits for all users", "social_media_subscription": "Social media subscription", "custom_condition": "Custom condition", "product_gift": "Product gift", "descriptions": {"user_scan_limits": "A user can scan the coupon only X times", "valid_until": "The coupon is valid until this date", "custom_condition": "Custom condition", "users_scan_limits": "All users can scan the coupon only X times", "social_media_subscription": "The user must follow you on social media.", "product_gift": "The user will receive a product as a gift", "before_scan": "Before scanning", "after_scan": "After scanning", "custom_event": "Custom event"}, "validation": {"invalid_date": "Invalid date", "date_must_be_after_today": "Date must be after today", "required": "This field is required", "invalid_number": "Invalid number", "invalid_url": "Invalid URL", "no_conditions": "At least one condition is required.", "numeric": "This field must be a number and non-negative", "url": "This field must contain a valid URL", "no_blueprint_data": "No blueprint data", "no_value_for": "No value for", "no_events": "At least one event is required.", "only_one_event": "Only one event is allowed"}}