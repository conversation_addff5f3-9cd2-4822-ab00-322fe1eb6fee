{"before_scan": "Antes del escaneo", "after_scan": "Después del escaneo", "custom_event": "Evento personalizado", "valid_until": "<PERSON><PERSON><PERSON><PERSON> hasta", "user_scan_limits": "Límites de escaneo por usuario", "users_scan_limits": "Límites de escaneo para todos los usuarios", "social_media_subscription": "Suscripción en redes sociales", "custom_condition": "Condición personalizada", "product_gift": "Regalo - producto", "descriptions": {"user_scan_limits": "El usuario solo puede escanear el cupón X veces", "valid_until": "El cupón es válido hasta esta fecha", "custom_condition": "Condición personalizada", "users_scan_limits": "Todos los usuarios pueden escanear el cupón solo X veces", "social_media_subscription": "El usuario debe estar suscrito a ti en la red social.", "product_gift": "El usuario recibirá un producto de regalo", "before_scan": "Antes del escaneo", "after_scan": "Después del escaneo", "custom_event": "Evento personalizado"}, "validation": {"invalid_date": "<PERSON>cha no válida", "date_must_be_after_today": "La fecha debe ser posterior a la de hoy", "required": "Este campo es obligatorio", "invalid_number": "Número no válido", "invalid_url": "URL no válida", "no_conditions": "Debe añadirse al menos una condición.", "numeric": "Este campo debe ser un número no negativo", "url": "Este campo debe contener una URL válida", "no_blueprint_data": "No hay datos de la plantilla", "no_value_for": "No hay valor para", "no_events": "Debe añadirse al menos un evento.", "only_one_event": "Solo se permite un evento"}}