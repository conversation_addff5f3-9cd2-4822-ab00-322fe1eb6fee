<?php

return [
    "accepted" => "Debe aceptar :attribute.",
    "accepted_if" => "Debe aceptar :attribute cuando :other contiene :value.",
    "active_url" => "El campo :attribute debe ser una URL válida.",
    "after" => "El campo :attribute debe ser una fecha posterior a :date.",
    "after_or_equal" => "El campo :attribute debe ser una fecha posterior o igual a :date.",
    "alpha" => "El campo :attribute solo puede contener letras.",
    "alpha_dash" => "El campo :attribute solo puede contener letras, números, guiones y guion bajo.",
    "alpha_num" => "El campo :attribute solo puede contener letras y números.",
    "any_of" => "El valor del campo :attribute no se encuentra en la lista permitida.",
    "array" => "El campo :attribute debe ser un arreglo.",
    "ascii" => "El campo :attribute debe contener únicamente caracteres alfanuméricos de un solo byte.",
    "attached" => "El contenido del campo :attribute ya está adjunto.",
    "before" => "El campo :attribute debe ser una fecha anterior a :date.",
    "before_or_equal" => "El campo :attribute debe ser una fecha anterior o igual a :date.",
    "between" => [
        "array" => "El número de elementos en el campo :attribute debe estar entre :min y :max.",
        "file" => "El tamaño del archivo en el campo :attribute debe estar entre :min y :max KB.",
        "numeric" => "El valor de :attribute debe estar entre :min y :max.",
        "string" => "El número de caracteres en el campo :attribute debe estar entre :min y :max.",
    ],
    "boolean" => "El campo :attribute debe ser un valor booleano.",
    "can" => "El campo :attribute debe estar autorizado.",
    "confirmed" => "El campo :attribute no coincide con su confirmación.",
    "contains" => "El campo :attribute no contiene el valor requerido.",
    "current_password" => "Contraseña incorrecta.",
    "date" => "El campo :attribute debe ser una fecha válida.",
    "date_equals" => "El campo :attribute debe ser una fecha igual a :date.",
    "date_format" => "El campo :attribute debe coincidir con el formato de fecha :format.",
    "decimal" => "El campo :attribute debe tener :decimal decimales.",
    "declined" => "El campo :attribute debe ser rechazado.",
    "declined_if" => "El campo :attribute debe ser rechazado cuando :other contiene :value.",
    "different" => "Los valores de :attribute y :other deben ser distintos.",
    "digits" => "El campo :attribute debe tener :digits dígitos.",
    "digits_between" => "El campo :attribute debe tener entre :min y :max dígitos.",
    "dimensions" => "La imagen indicada en :attribute tiene dimensiones no válidas.",
    "distinct" => "Los elementos del campo :attribute no deben repetirse.",
    "doesnt_end_with" => "El campo :attribute no debe terminar con uno de los siguientes: :values.",
    "doesnt_start_with" => "El campo :attribute no debe comenzar con uno de los siguientes: :values.",
    "email" => "El campo :attribute debe ser una dirección de correo electrónico válida.",
    "ends_with" => "El campo :attribute debe terminar con uno de los siguientes valores: :values.",
    "enum" => "El valor del campo :attribute no está en la lista de valores permitidos.",
    "exists" => "El valor seleccionado de :attribute no existe.",
    "extensions" => "El archivo en :attribute debe tener una de las siguientes extensiones: :values.",
    "failed" => "Nombre de usuario o contraseña incorrectos.",
    "file" => "El campo :attribute debe ser un archivo.",
    "filled" => "El campo :attribute debe contener un valor.",
    "gt" => [
        "array" => "El número de elementos en el campo :attribute debe ser mayor que :value.",
        "file" => "El tamaño del archivo en el campo :attribute debe ser mayor que :value KB.",
        "numeric" => "El valor de :attribute debe ser mayor que :value.",
        "string" => "El número de caracteres en el campo :attribute debe ser mayor que :value.",
    ],
    "gte" => [
        "array" => "El número de elementos en el campo :attribute debe ser :value o más.",
        "file" => "El tamaño del archivo en el campo :attribute debe ser :value KB o más.",
        "numeric" => "El valor de :attribute debe ser :value o mayor.",
        "string" => "El número de caracteres en el campo :attribute debe ser :value o más.",
    ],
    "hex_color" => "El campo :attribute debe ser un color HEX válido.",
    "image" => "El archivo en :attribute debe ser una imagen.",
    "in" => "El valor seleccionado de :attribute no está permitido.",
    "in_array" => "El valor de :attribute debe estar presente en :other.",
    "in_array_keys" => "El arreglo en :attribute debe contener al menos una de las siguientes claves: :values.",
    "integer" => "El campo :attribute debe ser un número entero.",
    "ip" => "El campo :attribute debe ser una dirección IP válida.",
    "ipv4" => "El campo :attribute debe ser una dirección IPv4 válida.",
    "ipv6" => "El campo :attribute debe ser una dirección IPv6 válida.",
    "json" => "El campo :attribute debe ser una cadena JSON válida.",
    "list" => "El campo :attribute debe ser una lista.",
    "lowercase" => "El campo :attribute debe estar en minúsculas.",
    "lt" => [
        "array" => "El número de elementos en el campo :attribute debe ser menor que :value.",
        "file" => "El tamaño del archivo en el campo :attribute debe ser menor que :value KB.",
        "numeric" => "El valor de :attribute debe ser menor que :value.",
        "string" => "El número de caracteres en el campo :attribute debe ser menor que :value.",
    ],
    "lte" => [
        "array" => "El número de elementos en el campo :attribute debe ser :value o menos.",
        "file" => "El tamaño del archivo en el campo :attribute debe ser :value KB o menos.",
        "numeric" => "El valor de :attribute debe ser :value o menor.",
        "string" => "El número de caracteres en el campo :attribute debe ser :value o menos.",
    ],
    "mac_address" => "El campo :attribute debe ser una dirección MAC válida.",
    "max" => [
        "array" => "El número de elementos en el campo :attribute no puede ser mayor que :max.",
        "file" => "El tamaño del archivo en el campo :attribute no puede ser mayor que :max KB.",
        "numeric" => "El valor de :attribute no puede ser mayor que :max.",
        "string" => "El número de caracteres en el campo :attribute no puede exceder de :max.",
    ],
    "max_digits" => "El campo :attribute no debe contener más de :max dígitos.",
    "mimes" => "El archivo en :attribute debe ser de uno de los siguientes tipos: :values.",
    "mimetypes" => "El archivo en :attribute debe ser de uno de los siguientes tipos: :values.",
    "min" => [
        "array" => "El número de elementos en el campo :attribute debe ser al menos :min.",
        "file" => "El tamaño del archivo en el campo :attribute debe ser al menos :min KB.",
        "numeric" => "El valor de :attribute debe ser al menos :min.",
        "string" => "El número de caracteres en el campo :attribute debe ser al menos :min.",
    ],
    "min_digits" => "El campo :attribute debe contener al menos :min dígitos.",
    "missing" => "El campo :attribute debe estar ausente.",
    "missing_if" => "El campo :attribute debe estar ausente cuando :other contiene :value.",
    "missing_unless" => "El campo :attribute debe estar ausente a menos que :other contenga :value.",
    "missing_with" => "El campo :attribute debe estar ausente si :values está presente.",
    "missing_with_all" => "El campo :attribute debe estar ausente cuando todos :values están presentes.",
    "multiple_of" => "El campo :attribute debe ser múltiplo de :value.",
    "next" => "Siguiente &raquo;",
    "not_in" => "El valor seleccionado de :attribute está prohibido.",
    "not_regex" => "El formato del campo :attribute no es válido.",
    "numeric" => "El campo :attribute debe ser numérico.",
    "password" => "Contraseña incorrecta.",
    "password" => [
        "letters" => "El campo :attribute debe contener al menos una letra.",
        "mixed" => "El campo :attribute debe contener al menos una letra mayúscula y una letra minúscula.",
        "numbers" => "El campo :attribute debe contener al menos un número.",
        "symbols" => "El campo :attribute debe contener al menos un símbolo.",
        "uncompromised" => "El valor de :attribute se ha encontrado en datos filtrados. Por favor, elija otro :attribute.",
    ],
    "present" => "El campo :attribute debe estar presente.",
    "present_if" => "El campo :attribute debe estar presente cuando :other contiene :value.",
    "present_unless" => "El campo :attribute debe estar presente, a menos que :other contenga :value.",
    "present_with" => "El campo :attribute debe estar presente cuando alguno de :values esté presente.",
    "present_with_all" => "El campo :attribute debe estar presente cuando todos los valores :values estén presentes.",
    "previous" => "&laquo; Anterior",
    "prohibited" => "El campo :attribute está prohibido.",
    "prohibited_if" => "El campo :attribute está prohibido cuando :other contiene :value.",
    "prohibited_if_accepted" => "El campo :attribute está prohibido si :other está aceptado.",
    "prohibited_if_declined" => "El campo :attribute está prohibido cuando :other está rechazado.",
    "prohibited_unless" => "El campo :attribute está prohibido a menos que :other esté en :values.",
    "prohibits" => "El valor de :attribute prohíbe la presencia de :other.",
    "regex" => "El formato del campo :attribute no es válido.",
    "relatable" => "El valor de :attribute no se puede relacionar con este recurso.",
    "required" => "El campo :attribute es obligatorio.",
    "required_array_keys" => "El arreglo en :attribute debe contener las claves: :values",
    "required_if" => "El campo :attribute es obligatorio cuando :other contiene :value.",
    "required_if_accepted" => "El campo :attribute es obligatorio cuando :other está aceptado.",
    "required_if_declined" => "El campo :attribute es obligatorio cuando :other está rechazado.",
    "required_unless" => "El campo :attribute es obligatorio cuando :other no contiene :values.",
    "required_with" => "El campo :attribute es obligatorio cuando :values está presente.",
    "required_with_all" => "El campo :attribute es obligatorio cuando :values están presentes.",
    "required_without" => "El campo :attribute es obligatorio cuando :values no está presente.",
    "required_without_all" => "El campo :attribute es obligatorio cuando ninguno de :values está presente.",
    "reset" => "Su contraseña ha sido restablecida.",
    "same" => "Los valores de :attribute y :other deben coincidir.",
    "sent" => "El enlace para restablecer la contraseña ha sido enviado.",
    "size" => [
        "array" => "El número de elementos en el campo :attribute debe ser :size.",
        "file" => "El tamaño del archivo en el campo :attribute debe ser :size KB.",
        "numeric" => "El valor de :attribute debe ser :size.",
        "string" => "El número de caracteres en el campo :attribute debe ser :size.",
    ],
    "starts_with" => "El campo :attribute debe comenzar con uno de los siguientes valores: :values",
    "string" => "El campo :attribute debe ser una cadena de texto.",
    "throttle" => "Demasiados intentos de acceso. Por favor, inténtelo de nuevo en :seconds segundos.",
    "throttled" => "Por favor, espere antes de volver a intentarlo.",
    "timezone" => "El campo :attribute debe ser una zona horaria válida.",
    "token" => "El token de restablecimiento de contraseña es inválido.",
    "ulid" => "El campo :attribute debe ser un ULID válido.",
    "unique" => "El valor de :attribute ya existe.",
    "uploaded" => "La subida del archivo en :attribute ha fallado.",
    "uppercase" => "El campo :attribute debe estar en mayúsculas.",
    "url" => "El campo :attribute no es una URL válida o tiene un formato incorrecto.",
    "user" => "No se pudo encontrar un usuario con esa dirección de correo electrónico.",
    "uuid" => "El campo :attribute debe ser un UUID válido.",

    'custom' => [
        'discount' => [
            'invalid_percentage' => 'El porcentaje de descuento debe estar entre 0 y 100.',
            'invalid_amount' => 'El monto del descuento no debe exceder el precio en la tienda.',
        ],
        'name' => [
            'regex' => 'El nombre debe comenzar con una letra y solo puede contener letras, números y espacios.',
        ],
        'working_hours' => [
            'required_full_week' => 'Complete el horario para todos los días de la semana.',
            'invalid_format' => 'Formato de horario inválido para :day.',
            'missing_field' => 'El campo :field falta o es inválido en :day.',
            'invalid_field' => 'El campo :field es inválido en :day.',
            'time_required' => 'Indique la hora de inicio y fin en :day.',
            'invalid_range' => 'La hora de finalización debe ser posterior a la hora de inicio en :day.',
            'invalid_breaks' => 'El campo breaks debe ser un arreglo en :day.',
        ],
        'socials' => [
            'regex' => 'El enlace social debe tener el formato: facebook.com, twitter.com, instagram.com, linkedin.com, youtube.com, tiktok.com, snapchat.com, pinterest.com, reddit.com o whatsapp.com.',
        ],
    ],
    'attributes' => [
        'name' => 'nombre',
        'working_hours' => 'horario laboral',
        'working_hours_day' => 'horario en :day',
    ],
];
