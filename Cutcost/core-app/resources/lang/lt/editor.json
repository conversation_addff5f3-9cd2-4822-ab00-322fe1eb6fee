{"before_scan": "<PERSON><PERSON><PERSON>", "after_scan": "Po skenavimo", "custom_event": "<PERSON><PERSON><PERSON><PERSON><PERSON> įvykis", "valid_until": "<PERSON><PERSON><PERSON> iki", "user_scan_limits": "Vartotojo skenavimo limitai", "users_scan_limits": "<PERSON><PERSON><PERSON> var<PERSON>jų skenavimo limitai", "social_media_subscription": "Prenumerata socialiniuose tinkluose", "custom_condition": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "product_gift": "Dovana – produktas", "descriptions": {"user_scan_limits": "Vartotojas gali nuskenuoti kuponą tik X kartų", "valid_until": "Kuponas galioja iki š<PERSON> datos", "custom_condition": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "users_scan_limits": "Visi vartotojai gali nuskenuoti kuponą tik X kartų", "social_media_subscription": "Vartotojas turi sekti jus socialiniuose tinkluose.", "product_gift": "Vartotojas gaus produktą dovanų", "before_scan": "<PERSON><PERSON><PERSON>", "after_scan": "Po skenavimo", "custom_event": "<PERSON><PERSON><PERSON><PERSON><PERSON> įvykis"}, "validation": {"invalid_date": "Neteisinga data", "date_must_be_after_today": "Data turi būti vėlesnė nei šiandien", "required": "<PERSON><PERSON> la<PERSON> yra privalomas", "invalid_number": "<PERSON><PERSON><PERSON><PERSON>", "invalid_url": "Neteisingas URL", "no_conditions": "<PERSON><PERSON><PERSON> p<PERSON> bent vieną sąlygą.", "numeric": "<PERSON><PERSON> lauka<PERSON> turi bū<PERSON> s<PERSON> ir ne<PERSON>s", "url": "<PERSON><PERSON><PERSON> lauke turi b<PERSON><PERSON> URL", "no_blueprint_data": "Nėra šablono duomenų", "no_value_for": "<PERSON><PERSON><PERSON>", "no_events": "<PERSON><PERSON><PERSON> p<PERSON> bent vieną įvykį.", "only_one_event": "Leidžiamas tik vienas įvykis"}}