<?php

return [
    'accepted' => 'Laukas :attribute turi būti priimtas.',
    'accepted_if' => 'Laukas :attribute turi būti priimtas kai :other yra :value.',
    'active_url' => 'Laukas :attribute nėra galiojantis internetinis adresas.',
    'after' => 'Lauko :attribute reikšmė turi būti po :date datos.',
    'after_or_equal' => 'Lauko :attribute reikšmė privalo būti data lygi arba vėlesnė negu :date.',
    'alpha' => 'Laukas :attribute gali turėti tik raides.',
    'alpha_dash' => 'Laukas :attribute gali turėti tik raides, skaičius ir brūkšnelius.',
    'alpha_num' => 'Laukas :attribute gali turėti tik raides ir skaičius.',
    'any_of' => ':attribute laukų neteisingas.',
    'array' => 'Laukas :attribute turi būti masyvas.',
    'ascii' => ':Attribute turi būti tik vieno baito raidiniai ir skaitmeniniai simboliai ir simboliai.',
    'attached' => 'Šis :attribute jau yra pridėtas.',
    'before' => 'Laukas :attribute turi būti data prieš :date.',
    'before_or_equal' => 'Lauko :attribute reikšmė privalo būti data lygi arba ankstesnė negu :date.',

    'between' => [
        'array' => 'Elementų skaičius lauke :attribute turi turėti nuo :min iki :max.',
        'file' => 'Failo dydis lauke :attribute turi būti tarp :min ir :max kilobaitų.',
        'numeric' => 'Lauko :attribute reikšmė turi būti tarp :min ir :max.',
        'string' => 'Simbolių skaičius lauke :attribute turi būti tarp :min ir :max.',
    ],

    'boolean' => 'Lauko reikšmė :attribute turi būti \'taip\' arba \'ne\'.',
    'can' => ':Attribute lauke yra neleistina reikšmė.',
    'confirmed' => 'Lauko :attribute patvirtinimas nesutampa.',
    'contains' => ':attribute lauke trūksta būtinos vertės.',
    'current_password' => 'Slaptažodis yra neteisingas.',
    'date' => 'Lauko :attribute reikšmė nėra galiojanti data.',
    'date_equals' => 'Lauko :attribute reikšmė turi būti data lygi :date.',
    'date_format' => 'Lauko :attribute reikšmė neatitinka formato :format.',
    'decimal' => ':Attribute turi būti :decimal skaitmenų po kablelio.',
    'declined' => 'Laukas :attribute turi būti atmestas.',
    'declined_if' => 'Laukas :attribute turi būti atmestas kai :other yra :value.',
    'different' => 'Laukų :attribute ir :other reikšmės turi skirtis.',
    'digits' => 'Laukas :attribute turi būti sudarytas iš :digits skaitmenų.',
    'digits_between' => 'Laukas :attribute turi turėti nuo :min iki :max skaitmenų.',
    'dimensions' => 'Lauke :attribute įkeltas paveiksliukas neatitinka išmatavimų reikalavimo.',
    'distinct' => 'Laukas :attribute pasikartoja.',
    'doesnt_end_with' => 'Laukas :attribute negali baigtis viena iš šių reikšmių: :values.',
    'doesnt_start_with' => 'Laukas :attribute negali prasidėti viena iš šių reikšmių: :values.',
    'email' => 'Lauko :attribute reikšmė turi būti galiojantis el. pašto adresas.',
    'ends_with' => 'Laukas :attribute turi baigtis vienu iš: :values',
    'enum' => 'Pasirinkta negaliojanti :attribute reikšmė.',
    'exists' => 'Pasirinkta negaliojanti :attribute reikšmė.',
    'extensions' => ':attribute lauke turi būti vienas iš šių plėtinių: :values.',
    'failed' => 'Prisijungimo duomenys neatitinka.',
    'file' => ':Attribute turi būti failas.',
    'filled' => 'Laukas :attribute turi būti užpildytas.',

    'gt' => [
        'array' => 'Laukas :attribute turi turėti daugiau nei :value elementus.',
        'file' => 'Failas lauke :attribute turi būti didesnis negu :value kilobaitai.',
        'numeric' => 'Lauko :attribute reikšmė turi būti didesnė negu :value.',
        'string' => 'Lauko :attribute reikšmė turi būti didesnė negu :value simboliai.',
    ],

    'gte' => [
        'array' => 'Laukas :attribute turi turėti :value elementus arba daugiau.',
        'file' => 'Failas lauke :attribute turi būti didesnis arba lygus :value kilobaitams.',
        'numeric' => 'Lauko :attribute reikšmė turi būti didesnė arba lygi :value.',
        'string' => 'Lauko :attribute reikšmė turi būti didesnė arba lygi :value simboliams.',
    ],

    'hex_color' => ':attribute laukas turi būti tinkama šešioliktainė spalva.',
    'image' => 'Lauko :attribute reikšmė turi būti paveikslėlis.',
    'in' => 'Pasirinkta negaliojanti :attribute reikšmė.',
    'in_array' => 'Laukas :attribute neegzistuoja :other lauke.',
    'in_array_keys' => ':attribute lauke turi būti bent vienas iš šių raktų: :values.',
    'integer' => 'Lauko :attribute reikšmė turi būti sveikasis skaičius.',
    'ip' => 'Lauko :attribute reikšmė turi būti galiojantis IP adresas.',
    'ipv4' => 'Lauko :attribute reikšmė turi būti galiojantis IPv4 adresas.',
    'ipv6' => 'Lauko :attribute reikšmė turi būti galiojantis IPv6 adresas.',
    'json' => 'Lauko :attribute reikšmė turi būti JSON tekstas.',
    'list' => ':attribute laukas turi būti sąrašas.',
    'lowercase' => ':Attribute turi būti mažosios raidės.',

    'lt' => [
        'array' => 'Laukas :attribute turi turėti mažiau negu :value elementus.',
        'file' => 'Failas lauke :attribute turi būti mažesnis negu :value kilobaitai.',
        'numeric' => 'Lauko :attribute reikšmė turi būti mažesnė negu :value.',
        'string' => 'Lauko :attribute reikšmė turi būti mažesnė negu :value simboliai.',
    ],

    'lte' => [
        'array' => 'Laukas :attribute turi turėti mažiau arba lygiai :value elementus.',
        'file' => 'Failas lauke :attribute turi būti mažesnis arba lygus :value kilobaitams.',
        'numeric' => 'Lauko :attribute reikšmė turi būti mažesnė arba lygi :value.',
        'string' => 'Lauko :attribute reikšmė turi būti mažesnė arba lygi :value simboliams.',
    ],

    'mac_address' => 'Lauko :attribute reikšmė turi būti galiojantis MAC adresas.',

    'max' => [
        'array' => 'Elementų kiekis lauke :attribute negali turėti daugiau nei :max elementų.',
        'file' => 'Failo dydis lauke :attribute negali būti didesnis nei :max kilobaitų.',
        'numeric' => 'Lauko :attribute reikšmė negali būti didesnė nei :max.',
        'string' => 'Simbolių kiekis lauke :attribute reikšmė negali būti didesnė nei :max simbolių.',
    ],

    'max_digits' => ':Attribute neturi būti daugiau nei :max skaitmenų.',
    'mimes' => 'Lauko reikšmė :attribute turi būti failas vieno iš sekančių tipų: :values.',
    'mimetypes' => 'Lauko reikšmė :attribute turi būti failas vieno iš sekančių tipų: :values.',

    'min' => [
        'array' => 'Elementų kiekis lauke :attribute turi būti ne mažiau nei :min.',
        'file' => 'Failo dydis lauke :attribute turi būti ne mažesnis nei :min kilobaitų.',
        'numeric' => 'Lauko :attribute reikšmė turi būti ne mažesnė nei :min.',
        'string' => 'Simbolių kiekis lauke :attribute turi būti ne mažiau nei :min.',
    ],

    'min_digits' => ':Attribute turi sudaryti bent :min skaitmenų.',
    'missing' => 'Turi būti trūkstamas :attribute laukas.',
    'missing_if' => 'Lauko :attribute turi trūkti, kai :other yra :value.',
    'missing_unless' => 'Lauko :attribute turi trūkti, nebent :other yra :value.',
    'missing_with' => 'Kai yra :values, lauko :attribute turi trūkti.',
    'missing_with_all' => 'Lauko :attribute turi trūkti, kai yra :values.',
    'multiple_of' => 'Laukas :attribute turi būti :value kartotinis.',
    'next' => 'Kitas &raquo;',
    'not_in' => 'Pasirinkta negaliojanti reikšmė :attribute.',
    'not_regex' => 'Lauko :attribute formatas yra neteisingas.',
    'numeric' => 'Lauko :attribute reikšmė turi būti skaičius.',
    'password' => 'Slaptažodis neteisingas.',

    'passwords' => [
        'letters' => 'Laukas :attribute privalo turėti bent vieną raidę.',
        'mixed' => 'Laukas :attribute privalo turėti bent vieną didžiąją ir mažąją reides.',
        'numbers' => 'Laukas :attribute privalo turėti bent vieną skaičių.',
        'symbols' => 'Laukas :attribute privalo turėti bent vieną simbolį.',
        'uncompromised' => 'Lauko :attribute reikšmė pastebėta duomenų nutekėjimo metu. Prašome pasirinkti kitą :attribute reikšmę.',
    ],

    'present' => 'Laukas :attribute turi egzistuoti.',
    'present_if' => 'Laukas :attribute turi būti rodomas, kai :other yra :value.',
    'present_unless' => 'Laukas :attribute turi būti pateiktas, nebent :other yra :value.',
    'present_with' => 'Laukas :attribute turi būti rodomas, kai yra :values.',
    'present_with_all' => ':attribute laukas turi būti pateiktas, kai yra :values.',
    'previous' => '&laquo; Ankstesnis',
    'prohibited' => ':Attribute laukas draudžiamas.',
    'prohibited_if' => ':Attribute laukas draudžiamas, kai :other yra :value.',
    'prohibited_if_accepted' => ':attribute laukas draudžiamas, kai priimamas :other.',
    'prohibited_if_declined' => ':attribute laukų draudžiama, kai :other atsisakoma.',
    'prohibited_unless' => ':Attribute laukas draudžiamas, nebent :other yra :values.',
    'prohibits' => 'Laukas :attribute draudžia :other egzistuoti.',
    'regex' => 'Negaliojantis lauko :attribute formatas.',
    'relatable' => 'Šis :attribute gali būti nesusijęs su šiuo šaltiniu.',
    'required' => 'Privaloma užpildyti lauką :attribute.',
    'required_array_keys' => 'Lauke :attribute privalo būti įrašai apie: :values.',
    'required_if' => 'Privaloma užpildyti lauką :attribute, kai :other yra :value.',
    'required_if_accepted' => ':Attribute laukelis būtinas, kai priimamas :other.',
    'required_if_declined' => ':attribute laukas būtinas, kai atmetamas :other.',
    'required_unless' => 'Laukas :attribute yra privalomas, nebent :other yra tarp :values reikšmių.',
    'required_with' => 'Privaloma užpildyti lauką :attribute, kai pateikta :values.',
    'required_with_all' => 'Privaloma užpildyti lauką :attribute, kai pateikta :values.',
    'required_without' => 'Privaloma užpildyti lauką :attribute, kai nepateikta :values.',
    'required_without_all' => 'Privaloma užpildyti lauką :attribute, kai nepateikta nei viena iš reikšmių :values.',
    'reset' => 'Nustatytas naujas slaptažodis!',
    'same' => 'Laukai :attribute ir :other turi sutapti.',
    'sent' => 'Naujo slaptažodžio nustatymo nuoroda išsiųsta',

    'size' => [
        'array' => 'Elementų kiekis lauke :attribute turi būti :size.',
        'file' => 'Failo dydis lauke :attribute turi būti :size kilobaitai.',
        'numeric' => 'Lauko :attribute reikšmė turi būti :size.',
        'string' => 'Simbolių skaičius lauke :attribute turi būti :size.',
    ],

    'starts_with' => 'Laukas :attribute turi prasidėti vienu iš: :values',
    'string' => 'Laukas :attribute turi būti tekstinis.',
    'throttle' => 'Per daug bandymų prisijungti. Bandykite po :seconds sec.',
    'throttled' => 'Palaukite prieš tęsdami.',
    'timezone' => 'Lauko :attribute reikšmė turi būti galiojanti laiko zona.',
    'token' => 'Šis slaptažodžio raktas yra neteisingas.',
    'ulid' => ':Attribute turi būti galiojantis ULID.',
    'unique' => 'Tokia :attribute reikšmė jau pasirinkta.',
    'uploaded' => 'Nepavyko įkelti :attribute lauko.',
    'uppercase' => ':Attribute turi būti didžiosios raidės.',
    'url' => 'Negaliojantis lauko :attribute formatas.',
    'user' => 'Vartotojas su tokiu el. paštu nerastas.',
    'uuid' => 'Lauko :attribute reikšmė turi būti galiojantis UUID.',

    'custom' => [
        'discount' => [
            'invalid_percentage' => 'Nuolaida procentais turi būti nuo 0 iki 100.',
            'invalid_amount' => 'Nuolaidos suma neturi viršyti kainos parduotuvėje.',
        ],
        'name' => [
            'regex' => 'Vardas turi prasidėti raide ir gali turėti tik raides, skaičius bei tarpelius.',
        ],
        'working_hours' => [
            'required_full_week' => 'Užpildykite darbo grafiką visoms savaitės dienoms.',
            'invalid_format' => 'Neteisingas darbo grafiko formatas dienai :day.',
            'missing_field' => 'Laukas :field trūksta arba neteisingas dienai :day.',
            'invalid_field' => 'Laukas :field neteisingas dienai :day.',
            'time_required' => 'Nurodykite darbo pradžios ir pabaigos laiką dienai :day.',
            'invalid_range' => 'Pabaigos laikas turi būti vėlesnis už pradžios laiką dienai :day.',
            'invalid_breaks' => 'Laukas breaks turi būti masyvas dienai :day.',
        ],
        'socials' => [
            'regex' => 'Socialinė nuoroda turi būti formatu: facebook.com, twitter.com, instagram.com, linkedin.com, youtube.com, tiktok.com, snapchat.com, pinterest.com, reddit.com arba whatsapp.com.',
        ],
    ],
    'attributes' => [
        'name' => 'vardas',
        'working_hours' => 'darbo valandos',
        'working_hours_day' => 'darbo laikas :day',
    ],

];
