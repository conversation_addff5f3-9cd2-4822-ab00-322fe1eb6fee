{"before_scan": "Przed skanowaniem", "after_scan": "Po zeskanowaniu", "custom_event": "<PERSON><PERSON><PERSON><PERSON><PERSON>rdo<PERSON>", "valid_until": "Ważny do", "user_scan_limits": "Limity skanowania dla użytkownika", "users_scan_limits": "Limity skanowania dla wszystkich użytkowników", "social_media_subscription": "Subskrypcja w mediach społecznościowych", "custom_condition": "Warunek niestandardowy", "product_gift": "Prezent - produkt", "descriptions": {"user_scan_limits": "Użytkownik może zeskanować kupon tylko X razy", "valid_until": "<PERSON><PERSON><PERSON> jest ważny do tej daty", "custom_condition": "Warunek niestandardowy", "users_scan_limits": "Wszyscy użytkownicy mogą zeskanować kupon tylko X razy", "social_media_subscription": "Użytkownik musi być subskrybentem w mediach społecznościowych.", "product_gift": "Użytkownik otrzyma produkt jako prezent", "before_scan": "Przed skanowaniem", "after_scan": "Po zeskanowaniu", "custom_event": "<PERSON><PERSON><PERSON><PERSON><PERSON>rdo<PERSON>"}, "validation": {"invalid_date": "Nieprawidłowa data", "date_must_be_after_today": "Data musi być późniejsza niż dzisiaj", "required": "To pole jest wymagane", "invalid_number": "Nieprawidłowa liczba", "invalid_url": "Nieprawidłowy URL", "no_conditions": "<PERSON><PERSON><PERSON><PERSON> dodać co najmniej jeden warunek.", "numeric": "To pole musi być licz<PERSON> i nieujemną", "url": "To pole musi zawierać poprawny URL", "no_blueprint_data": "<PERSON><PERSON> da<PERSON><PERSON> s<PERSON>", "no_value_for": "<PERSON><PERSON> war<PERSON>ś<PERSON> dla", "no_events": "Na<PERSON><PERSON><PERSON> dodać co najmniej jedno wydarzenie.", "only_one_event": "Doz<PERSON>lone jest tylko jedno wydarzenie"}}