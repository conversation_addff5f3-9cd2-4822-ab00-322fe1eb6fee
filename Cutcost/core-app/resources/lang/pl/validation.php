<?php

return [
    "accepted" => "<PERSON><PERSON><PERSON> zaakceptować :attribute.",
    "accepted_if" => "<PERSON><PERSON><PERSON> zaakceptować :attribute, gdy :other zawiera :value.",
    "active_url" => "Wartość pola :attribute musi być prawidłowym adresem URL.",
    "after" => "Wartość pola :attribute musi być datą po :date.",
    "after_or_equal" => "Wartość pola :attribute musi być datą po lub równą :date.",
    "alpha" => "Wartość pola :attribute może zawierać tylko litery.",
    "alpha_dash" => "Wartość pola :attribute może zawierać tylko litery, cyfry, myślnik i podkreślenie.",
    "alpha_num" => "Wartość pola :attribute może zawierać tylko litery i cyfry.",
    "any_of" => "Wartość pola :attribute nie została znaleziona na liście dozwolonych.",
    "array" => "Wartość pola :attribute musi być tablicą.",
    "ascii" => "Wartość pola :attribute musi zawierać tylko jednobajtowe alfanumeryczne znaki.",
    "attached" => "Zawartość pola :attribute jest już dołączona.",
    "before" => "Wartość pola :attribute musi być datą przed :date.",
    "before_or_equal" => "Wartość pola :attribute musi być datą przed lub równą :date.",
    "between" => [
        "array" => "Liczba elementów w polu :attribute musi wynosić od :min do :max.",
        "file" => "Rozmiar pliku w polu :attribute musi wynosić od :min do :max KB.",
        "numeric" => "Wartość pola :attribute musi wynosić od :min do :max.",
        "string" => "Liczba znaków w polu :attribute musi wynosić od :min do :max.",
    ],
    "boolean" => "Wartość pola :attribute musi być typu logicznego.",
    "can" => "Wartość pola :attribute musi być autoryzowana.",
    "confirmed" => "Wartość pola :attribute nie zgadza się z potwierdzeniem.",
    "contains" => "W polu :attribute brakuje wymaganej wartości.",
    "current_password" => "Nieprawidłowe hasło.",
    "date" => "Wartość pola :attribute musi być poprawną datą.",
    "date_equals" => "Wartość pola :attribute musi być datą równą :date.",
    "date_format" => "Wartość pola :attribute musi odpowiadać formatowi daty: :format.",
    "decimal" => "Wartość pola :attribute musi zawierać :decimal cyfr dziesiętnych.",
    "declined" => "Wartość pola :attribute musi być odrzucona.",
    "declined_if" => "Wartość pola :attribute musi być odrzucona, gdy :other zawiera :value.",
    "different" => "Wartości pól :attribute i :other muszą się różnić.",
    "digits" => "Liczba znaków w polu :attribute musi wynosić :digits.",
    "digits_between" => "Liczba znaków w polu :attribute musi wynosić od :min do :max.",
    "dimensions" => "Obraz w polu :attribute ma niedozwolone wymiary.",
    "distinct" => "Elementy w wartości pola :attribute nie mogą się powtarzać.",
    "doesnt_end_with" => "Wartość pola :attribute nie może kończyć się jednym z następujących: :values.",
    "doesnt_start_with" => "Wartość pola :attribute nie może zaczynać się od jednego z następujących: :values.",
    "email" => "Wartość pola :attribute musi być prawidłowym adresem e-mail.",
    "ends_with" => "Wartość pola :attribute musi kończyć się jednym z następujących: :values",
    "enum" => "Wartość pola :attribute nie znajduje się na liście dozwolonych.",
    "exists" => "Wartość pola :attribute nie istnieje.",
    "extensions" => "Plik w polu :attribute musi mieć jedno z następujących rozszerzeń: :values.",
    "failed" => "Nieprawidłowa nazwa użytkownika lub hasło.",
    "file" => "W polu :attribute musi być wskazany plik.",
    "filled" => "Wartość pola :attribute jest wymagana.",
    "gt" => [
        "array" => "Liczba elementów w polu :attribute musi być większa niż :value.",
        "file" => "Rozmiar pliku w polu :attribute musi być większy niż :value KB.",
        "numeric" => "Wartość pola :attribute musi być większa niż :value.",
        "string" => "Liczba znaków w polu :attribute musi być większa niż :value.",
    ],
    "gte" => [
        "array" => "Liczba elementów w polu :attribute musi wynosić :value lub więcej.",
        "file" => "Rozmiar pliku w polu :attribute musi wynosić :value KB lub więcej.",
        "numeric" => "Wartość pola :attribute musi wynosić :value lub więcej.",
        "string" => "Liczba znaków w polu :attribute musi wynosić :value lub więcej.",
    ],
    "hex_color" => "Wartość pola :attribute musi być poprawnym kolorem w formacie HEX.",
    "image" => "Plik w polu :attribute musi być obrazem.",
    "in" => "Wartość pola :attribute nie znajduje się na liście dozwolonych.",
    "in_array" => "Wartość pola :attribute musi być wskazana w polu :other.",
    "in_array_keys" => "Tablica w wartości pola :attribute musi mieć co najmniej jeden z następujących kluczy: :values.",
    "integer" => "Wartość pola :attribute musi być liczbą całkowitą.",
    "ip" => "Wartość pola :attribute musi być prawidłowym adresem IP.",
    "ipv4" => "Wartość pola :attribute musi być prawidłowym adresem IPv4.",
    "ipv6" => "Wartość pola :attribute musi być prawidłowym adresem IPv6.",
    "json" => "Wartość pola :attribute musi być ciągiem JSON.",
    "list" => "Wartość pola :attribute musi być listą.",
    "lowercase" => "Wartość pola :attribute musi być zapisana małymi literami.",
    "lt" => [
        "array" => "Liczba elementów w polu :attribute musi być mniejsza niż :value.",
        "file" => "Rozmiar pliku w polu :attribute musi być mniejszy niż :value KB.",
        "numeric" => "Wartość pola :attribute musi być mniejsza niż :value.",
        "string" => "Liczba znaków w polu :attribute musi być mniejsza niż :value.",
    ],
    "lte" => [
        "array" => "Liczba elementów w polu :attribute musi wynosić :value lub mniej.",
        "file" => "Rozmiar pliku w polu :attribute musi wynosić :value KB lub mniej.",
        "numeric" => "Wartość pola :attribute musi być równa lub mniejsza niż :value.",
        "string" => "Liczba znaków w polu :attribute musi wynosić :value lub mniej.",
    ],
    "mac_address" => "Wartość pola :attribute musi być poprawnym adresem MAC.",
    "max" => [
        "array" => "Liczba elementów w polu :attribute nie może przekraczać :max.",
        "file" => "Rozmiar pliku w polu :attribute nie może być większy niż :max KB.",
        "numeric" => "Wartość pola :attribute nie może być większa niż :max.",
        "string" => "Liczba znaków w wartości pola :attribute nie może przekraczać :max.",
    ],
    "max_digits" => "Wartość pola :attribute nie powinna zawierać więcej niż :max cyfr.",
    "mimes" => "Plik w polu :attribute musi być jednego z następujących typów: :values.",
    "mimetypes" => "Plik w polu :attribute musi być jednego z następujących typów: :values.",
    "min" => [
        "array" => "Liczba elementów w polu :attribute musi wynosić co najmniej :min.",
        "file" => "Rozmiar pliku w polu :attribute musi wynosić co najmniej :min KB.",
        "numeric" => "Wartość pola :attribute musi wynosić co najmniej :min.",
        "string" => "Liczba znaków w polu :attribute musi wynosić co najmniej :min.",
    ],
    "min_digits" => "Wartość pola :attribute musi zawierać co najmniej :min cyfr.",
    "missing" => "Wartość pola :attribute musi być nieobecna.",
    "missing_if" => "Wartość pola :attribute musi być nieobecna, gdy :other zawiera :value.",
    "missing_unless" => "Wartość pola :attribute musi być nieobecna, gdy :other nie zawiera :value.",
    "missing_with" => "Wartość pola :attribute musi być nieobecna, jeśli wskazano :values.",
    "missing_with_all" => "Wartość pola :attribute musi być nieobecna, gdy wskazane są wszystkie :values.",
    "multiple_of" => "Wartość pola :attribute musi być wielokrotnością :value",
    "next" => "Dalej &raquo;",
    "not_in" => "Wartość pola :attribute znajduje się na liście zakazanych.",
    "not_regex" => "Wartość pola :attribute ma niepoprawny format.",
    "numeric" => "Wartość pola :attribute musi być liczbą.",
    "password" => "Nieprawidłowe hasło.",
    "password" => [
        "letters" => "Wartość pola :attribute musi zawierać co najmniej jedną literę.",
        "mixed" => "Wartość pola :attribute musi zawierać co najmniej jedną wielką i jedną małą literę.",
        "numbers" => "Wartość pola :attribute musi zawierać co najmniej jedną cyfrę.",
        "symbols" => "Wartość pola :attribute musi zawierać co najmniej jeden symbol.",
        "uncompromised" => "Wartość pola :attribute została znaleziona w wyciekłych danych. Proszę wybrać inną wartość dla :attribute.",
    ],
    "present" => "Wartość pola :attribute musi być obecna.",
    "present_if" => "Wartość pola :attribute musi być obecna, gdy :other zawiera :value.",
    "present_unless" => "Wartość pola :attribute musi być obecna, jeśli tylko :other nie zawiera :value.",
    "present_with" => "Wartość pola :attribute musi być obecna, gdy jedno z :values jest obecne.",
    "present_with_all" => "Wartość pola :attribute musi być obecna, gdy wszystkie wartości są obecne: :values.",
    "previous" => "&laquo; Wstecz",
    "prohibited" => "Wartość pola :attribute jest zabroniona.",
    "prohibited_if" => "Wartość pola :attribute jest zabroniona, gdy :other zawiera :value.",
    "prohibited_if_accepted" => "Wartość pola :attribute jest zabroniona, jeśli zaakceptowano :other.",
    "prohibited_if_declined" => "Wartość pola :attribute jest zabroniona przy odrzuceniu :other.",
    "prohibited_unless" => "Wartość pola :attribute jest zabroniona, jeśli :other nie znajduje się w :values.",
    "prohibits" => "Wartość pola :attribute zabrania obecności :other.",
    "regex" => "Wartość pola :attribute ma niepoprawny format.",
    "relatable" => "Wartość pola :attribute nie może być powiązana z tym zasobem.",
    "required" => "Pole :attribute jest wymagane.",
    "required_array_keys" => "Tablica w polu :attribute musi zawierać klucze: :values",
    "required_if" => "Pole :attribute jest wymagane, gdy :other zawiera :value.",
    "required_if_accepted" => "Pole :attribute jest wymagane, gdy :other jest zaakceptowane.",
    "required_if_declined" => "Pole :attribute jest wymagane, gdy :other jest odrzucone.",
    "required_unless" => "Pole :attribute jest wymagane, gdy :other nie zawiera :values.",
    "required_with" => "Pole :attribute jest wymagane, gdy wskazano :values.",
    "required_with_all" => "Pole :attribute jest wymagane, gdy wskazano :values.",
    "required_without" => "Pole :attribute jest wymagane, gdy nie wskazano :values.",
    "required_without_all" => "Pole :attribute jest wymagane, gdy nie wskazano żadnego z :values.",
    "reset" => "Twoje hasło zostało zresetowane.",
    "same" => "Wartości pól :attribute i :other muszą się zgadzać.",
    "sent" => "Link do resetowania hasła został wysłany.",
    "size" => [
        "array" => "Liczba elementów w polu :attribute musi wynosić :size.",
        "file" => "Rozmiar pliku w polu :attribute musi wynosić :size KB.",
        "numeric" => "Wartość pola :attribute musi wynosić :size.",
        "string" => "Liczba znaków w polu :attribute musi wynosić :size.",
    ],
    "starts_with" => "Pole :attribute musi zaczynać się od jednej z następujących wartości: :values",
    "string" => "Wartość pola :attribute musi być ciągiem znaków.",
    "throttle" => "Zbyt wiele prób logowania. Proszę spróbować ponownie za :seconds sekund.",
    "throttled" => "Proszę poczekać przed ponowną próbą.",
    "timezone" => "Wartość pola :attribute musi być prawidłową strefą czasową.",
    "token" => "Błędny kod resetowania hasła.",
    "ulid" => "Wartość pola :attribute musi być poprawnym ULID.",
    "unique" => "Taka wartość pola :attribute już istnieje.",
    "uploaded" => "Przesyłanie pliku z pola :attribute nie powiodło się.",
    "uppercase" => "Wartość pola :attribute musi być zapisana wielkimi literami.",
    "url" => "Wartość pola :attribute nie jest linkiem lub ma niepoprawny format.",
    "user" => "Nie udało się znaleźć użytkownika z podanym adresem e-mail.",
    "uuid" => "Wartość pola :attribute musi być poprawnym UUID.",

    'custom' => [
        'discount' => [
            'invalid_percentage' => 'Zniżka w procentach musi wynosić od 0 do 100.',
            'invalid_amount' => 'Wielkość zniżki nie powinna przekraczać ceny w sklepie.',
        ],
        'name' => [
            'regex' => 'Imię musi zaczynać się od litery i może zawierać tylko litery, cyfry i spacje.',
        ],
        'working_hours' => [
            'required_full_week' => 'Wypełnij harmonogram na wszystkie dni tygodnia.',
            'invalid_format' => 'Nieprawidłowy format harmonogramu dla :day.',
            'missing_field' => 'Pole :field jest nieobecne lub nieprawidłowe w :day.',
            'invalid_field' => 'Pole :field jest nieprawidłowe w :day.',
            'time_required' => 'Podaj czas rozpoczęcia i zakończenia pracy w :day.',
            'invalid_range' => 'Czas zakończenia musi być późniejszy niż rozpoczęcia w :day.',
            'invalid_breaks' => 'Pole breaks musi być tablicą w :day.',
        ],
        'socials' => [
            'regex' => 'Link społecznościowy musi być w formacie: facebook.com, twitter.com, instagram.com, linkedin.com, youtube.com, tiktok.com, snapchat.com, pinterest.com, reddit.com lub whatsapp.com.',
        ],
    ],
    'attributes' => [
        'name' => 'imię',
        'working_hours' => 'godziny pracy',
        'working_hours_day' => 'czas pracy w :day',
    ],
];
