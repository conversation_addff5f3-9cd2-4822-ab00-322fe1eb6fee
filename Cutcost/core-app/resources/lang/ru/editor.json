{"before_scan": "Перед сканированием", "after_scan": "После сканирования", "custom_event": "Пользовательское событие", "valid_until": "Действительно до", "user_scan_limits": "Лимиты сканирования для пользователя", "users_scan_limits": "Лимиты сканирования для всех пользователей", "social_media_subscription": "Подписка в соцсетях", "custom_condition": "Пользовательское условие", "product_gift": "Подарок - продукт", "descriptions": {"user_scan_limits": "Пользователь может сканировать купон только X раз", "valid_until": "Купон действителен до этой даты", "custom_condition": "Пользовательское условие", "users_scan_limits": "Все пользователи могут сканировать купон только X раз", "social_media_subscription": "Пользователь должен быть подписан на вас в соцсети.", "product_gift": "Пользователь получит продукт в подарок", "before_scan": "Перед сканированием", "after_scan": "После сканирования", "custom_event": "Пользовательское событие"}, "validation": {"invalid_date": "Неверная дата", "date_must_be_after_today": "Дата должна быть позже сегодняшней", "required": "Это поле обязательно для заполнения", "invalid_number": "Неверное число", "invalid_url": "Неверный URL", "no_conditions": "Необходимо добавить хотя бы одно условие.", "numeric": "Это поле должно быть числом и неотрицательным", "url": "Это поле должно содержать корректный URL", "no_blueprint_data": "Нет данных шаблона", "no_value_for": "Нет значения для", "no_events": "Необходимо добавить хотя бы одно событие.", "only_one_event": "Разрешено только одно событие"}}