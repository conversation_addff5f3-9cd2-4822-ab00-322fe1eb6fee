<div class="loader-overlay" id="loader-overlay">
    <h1 class="loader-text">CutC%st</h1>
</div>

<style>
    .loader-overlay {
        position: fixed;
        inset: 0;
        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), rgba(17, 17, 17, 0.9));
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 1;
        transition: opacity 0.4s ease-out, transform 0.5s ease-out;
    }

    .loader-overlay.fade-out {
        opacity: 0;
        transform: translateY(20px);
    }

    .loader-text {
        font-family: 'Orbitron', sans-serif;
        font-size: 64px;
        font-weight: 700;
        color: #60a5fa;
        user-select: none;
        /* blue-400 */
        text-transform: uppercase;
        letter-spacing: 2px;
        animation: text-glow 1s ease-in-out infinite;
    }

    @keyframes text-glow {

        0%,
        100% {
            color: #60a5fa;
            filter: drop-shadow(0 0 8px rgba(96, 165, 250, 0.5));
        }

        50% {
            color: #93c5fd;
            /* Slightly lighter blue (blue-300) for subtle animation */
            filter: drop-shadow(0 0 12px rgba(96, 165, 250, 0.7));
        }
    }

    @media (prefers-reduced-motion: reduce) {
        .loader-text {
            animation: none;
            color: #60a5fa;
            filter: none;
        }

        .loader-overlay {
            transition: none;
        }
    }

    @media (max-width: 768px) {
        .loader-text {
            font-size: 36px;
        }
    }
</style>

<script>
    window.addEventListener('load', () => {
        const loader = document.getElementById('loader-overlay');
        if (loader) {
            setTimeout(() => {
                loader.classList.add('fade-out');
                loader.addEventListener('transitionend', () => loader.remove(), {
                    once: true
                });
            }, 100);
        }
    });
</script>
