<?php

use App\Modules\Public\Http\Controllers\CityController;
use App\Modules\Seller\Http\Controllers\PostController;
use App\Shared\Http\Controllers\Helpers\TranslationController;
use Illuminate\Support\Facades\Route;

Route::get('/post/{postSlug}', [PostController::class, 'getPost'])->name('post.get');
Route::get('city/{cityId}', [CityController::class, 'getCity'])->name('city.get');
Route::post('/translate', [TranslationController::class, 'translate'])->name('translate');