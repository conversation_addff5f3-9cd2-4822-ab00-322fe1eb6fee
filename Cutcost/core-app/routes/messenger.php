<?php

use App\Modules\Messenger\Http\Controllers\Api\ChatApiController;
use App\Modules\Messenger\Http\Controllers\Api\ChatsApiController;
use App\Modules\Messenger\Http\Controllers\Api\CompanyMessengerApiController;
use App\Modules\Messenger\Http\Controllers\Api\GroupApiController;
use App\Modules\Messenger\Http\Controllers\Api\MessageApiController;
use App\Modules\Messenger\Http\Controllers\ChatController;
use App\Modules\Messenger\Http\Controllers\GroupController;
use Illuminate\Support\Facades\Route;

Route::controller(ChatController::class)->group(function () {
    // for chat list
    Route::get('/',  'index')->name('index');
    Route::get('/c.htm',  'chat')->name('chat');
});

// API

Route::name('groups.')
    ->prefix('groups')->group(function () {

        Route::post('/create', [GroupController::class, 'createGroup'])->name('create');
        Route::put('/update/group/{chat}/name', [GroupApiController::class, 'updateGroupName'])->name('update.group.name');
        Route::patch('/add/group/{chat}', [GroupApiController::class, 'addUser'])
            ->name('add.user');

        Route::delete('/delete/group/{chat}/{user}', [GroupApiController::class, 'removeUser'])->name('remove.user');
    });

Route::controller(ChatsApiController::class)->group(function () {
    Route::get('/api/chats/{user}',  'getChats')->name('api.chats');
});

Route::controller(ChatApiController::class)->name('api.')->group(function () {
    Route::post('/api/chats/paginate/{chat}/{cursor}',  'paginateMessages')
        ->name('chat.paginate');

    Route::patch('/api/chat/{chat}/block',  'blockChat')
        ->name('chat.block');

    Route::patch('/api/chat/{chat}/unblock',  'unBlockChat')
        ->name('chat.unblock');

    Route::delete('/api/chat/{chat}/quit',  'quitChat')
        ->name('chat.quit');
});

Route::controller(MessageApiController::class)->name('api.')->group(function () {
    Route::post('/message/{chat}/send',  'sendMessage')
        ->name('message.send');

    Route::delete('/message/{chat}/{message}/delete',  'destroyMessage')
        ->name('message.destroy');

    Route::put('/message/{chat}/{message}/update',  'updateMessage')
        ->name('message.update');

    Route::patch('/message/read/{message}',  'readMessage')
        ->name('messages.read');
});

Route::controller(CompanyMessengerApiController::class)->name('api.')->group(function () {
    Route::get('/company/{company:slug}/info',  'getCompanyInfo')
        ->name('company.info');
});
