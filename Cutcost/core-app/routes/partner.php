<?php

use App\Modules\Partner\Http\Controllers\BotController;
use App\Modules\Partner\Http\Controllers\CompanyController;
use App\Modules\Partner\Http\Controllers\CouponController;
use App\Modules\Partner\Http\Controllers\DashboardController;
use App\Modules\Partner\Http\Controllers\LocationController;
use App\Modules\Partner\Http\Controllers\ScanCouponController;
use App\Modules\Partner\Http\Controllers\StatisticController;
use App\Modules\Partner\Http\Middleware\CompanyMiddleware;
use Illuminate\Support\Facades\Route;

// coupon
Route::get('coupons/{company}/create', [CouponController::class, 'create'])->name('coupons.create');
Route::get('coupons/{coupon}/edit/{company}', [CouponController::class, 'edit'])->name('coupons.edit');
Route::delete('coupons/{coupon}/delete', [CouponController::class, 'destroy'])->name('coupons.destroy');
Route::put('coupons/{coupon}/update', [CouponController::class, 'update'])->name('coupons.update');
Route::post('coupons/{company}/store', [CouponController::class, 'store'])->name('coupons.store');
Route::patch('/coupon/update/locations/{coupon:slug}', [CouponController::class, 'updateCouponLocations'])->name('coupon.update.locations');

//locations
Route::get('/locations/{company}/all', [LocationController::class, 'index'])->name('locations.index');
Route::get('/locations/{company}/create', [LocationController::class, 'create'])->name('locations.create');
Route::post('/locations/{company}/store', [LocationController::class, 'store'])->name('locations.store');
Route::get('/locations/{location}/edit/{company}', [LocationController::class, 'edit'])->name('locations.edit');
Route::put('/locations/{location}', [LocationController::class, 'update'])->name('locations.update');
Route::delete('/locations/{location}', [LocationController::class, 'destroy'])->name('locations.destroy');
Route::patch('/location/settings/update/{location:slug}', [LocationController::class, 'updateSettings'])->name('location.update.settings');
Route::patch('/location/update/coupons/{location:slug}', [LocationController::class, 'updateCoupons'])->name('location.update.coupons');

// Companies
Route::resource('companies', CompanyController::class)->only(['create', 'store', 'update', 'edit'])
    ->withoutMiddleware(CompanyMiddleware::class);
Route::delete('/companies/{company}', [CompanyController::class, 'destroy'])->name('companies.destroy');

Route::get('/business/dashboard', [DashboardController::class, 'business'])->name('business.dashboard');
Route::get('/referral/dashboard', [DashboardController::class, 'referral'])->name('referral.dashboard');
Route::get('/business/statistic', [StatisticController::class, 'index'])->name('statistic.index');

//scan 
Route::get('/scan/{company}', [ScanCouponController::class, 'index'])->name('scan.index');
Route::post('/scan/check/{company}', [ScanCouponController::class, 'check'])->name('scan.check');
Route::post('/scan/accept/{company}', [ScanCouponController::class, 'accept'])->name('scan.accept');

Route::get('/bot/{company?}', [BotController::class, 'index'])->name('bot.index');
Route::put('/bot/{company?}', [BotController::class, 'update'])->name('bot.update');
