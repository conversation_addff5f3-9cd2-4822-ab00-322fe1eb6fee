<?php

use App\Modules\Partner\Http\Controllers\BillingController;
use App\Modules\Partner\Http\Controllers\LocationController;
use App\Modules\Public\Http\Controllers\FeedController;
use App\Modules\Public\Http\Controllers\HelpController;
use App\Modules\Public\Http\Controllers\MapController;
use App\Modules\Public\Http\Controllers\NewsController;
use App\Modules\Public\Http\Controllers\PaginateController;
use App\Modules\Public\Http\Controllers\SearchController;
use App\Modules\Public\Http\Controllers\ShowCompanyController;
use App\Modules\Public\Http\Controllers\ShowCouponController;
use App\Modules\Seller\Http\Controllers\PostController;
use App\Modules\Seller\Http\Controllers\ProfileController;
use App\Shared\Http\Controllers\DocumentController;
use App\Shared\Http\Middleware\AuthMiddleware;
use Illuminate\Support\Facades\Route;

Route::get('/view/{coupon:slug}', [ShowCouponController::class, 'index'])->name('coupon.view');
Route::get('/view/location/{slug}', [LocationController::class, 'show'])->name('location.show');
Route::get('/profile', [ProfileController::class, 'profile'])->name('profile');
Route::get('plans', [BillingController::class, 'index'])->name('plans');

Route::get('/view/post/{post:slug}', [PostController::class, 'index'])
    ->name('show.post');

Route::get('/company/{slug}', [ShowCompanyController::class, 'show'])->name('company.show');

// show map 
Route::get('/locations', [MapController::class, 'index'])->name('map.index');

Route::get('/news', [NewsController::class, 'index'])->name('news');
Route::get('/news/{article:slug}', [NewsController::class, 'show'])->name('news.show');

Route::get('/feed', [FeedController::class, 'coupons'])->name('feed');

// Help Center routes
Route::get('/help', [HelpController::class, 'index'])->name('help.index');
Route::get('/help/faq', [HelpController::class, 'faq'])->name('help.faq');
Route::get('/help/contact', [HelpController::class, 'contact'])->name('help.contact');
Route::get('/help/search', [HelpController::class, 'search'])->name('help.search');
Route::get('/help/{slug}', [HelpController::class, 'article'])->name('help.article');

Route::permanentRedirect('/', '/feed');

Route::get('/search/advanced', [SearchController::class, 'index'])
    ->name('advanced.search')
    ->middleware(AuthMiddleware::class);

//search
Route::post('/search/city', [SearchController::class, 'citySearch'])->name('search.city');
Route::post('/header/search/users', [SearchController::class, 'searchUsers'])->name('search.users');
Route::post('/header/search/coupons', [SearchController::class, 'searchCoupons'])->name('search.coupons');

// load posts
Route::get('/load/paginated', [PaginateController::class, 'load'])->name('load.paginated');

// load coupons
Route::get('/load/coupons', [PaginateController::class, 'loadCoupons'])->name('load.coupons');

//aside footer
Route::get('/terms', [DocumentController::class, 'terms'])->name('terms');
Route::get('/privacy', [DocumentController::class, 'privacy'])->name('privacy');

// Redirect old routes to help system
Route::permanentRedirect('/about', '/help');
Route::permanentRedirect('/contact-us', '/help/contact');
