<?php

use App\Modules\Partner\Http\Controllers\CompanyController;
use App\Modules\Partner\Http\Controllers\PartnerController;
use App\Modules\Seller\Http\Controllers\BookmarkController;
use App\Modules\Seller\Http\Controllers\MyCouponsController;
use App\Modules\Seller\Http\Controllers\NotificationsController;
use App\Modules\Seller\Http\Controllers\SettingsController;
use Illuminate\Support\Facades\Route;
use App\Modules\Public\Http\Controllers\ShowCouponQrController;
use App\Modules\Seller\Http\Controllers\CompanySubscriptionController;
use App\Modules\Seller\Http\Controllers\FriendController;
use App\Modules\Seller\Http\Controllers\PostController;
use App\Modules\Seller\Http\Controllers\ProfileController;
use App\Modules\Seller\Http\Controllers\ShowUsersController;
use App\Shared\Http\Controllers\Auth\AuthController;
use App\Shared\Http\Controllers\Helpers\ModelMediaController;
use App\Shared\Http\Controllers\User\CommentController;

Route::prefix('settings')->controller(SettingsController::class)->group(function () {
    Route::get('/', 'index')->name('settings');
    Route::put('/update/personal', 'updateProfileDetails')->name('settings.details');
    Route::put('/update/privacy-preferences', 'updateProfilePrivacyPreferences')->name('settings.privacy-preferences');
    Route::put('/update/lang-and-region', 'updateProfileLangAndRegion')->name('settings.lang-and-region');
});
Route::get('/user/{user:nickname}/s/{type}', [ProfileController::class, 'connections'])->name('connections');
Route::get('/user/{user:nickname}/businesses', [ProfileController::class, 'businesses'])->name('businesses');

Route::delete('/session/{sessionId}', [AuthController::class, 'destroySession'])->name('auth.session.destroy');
Route::delete('/delete', [AuthController::class, 'destroyUser'])->name('auth.user.destroy');

Route::prefix('friend')->controller(FriendController::class)->group(function () {
    Route::post('/{user}', 'sendFriendRequest')->name('send.friend.request');
    Route::post('/{user}/accept', 'acceptFriendRequest')->name('accept.friend.request');
    Route::post('/{user}/delete', 'deleteFriend')->name('delete.friend');
    Route::post('/{user}/cancel/request', 'cancelRequest')->name('cancel.request');
});

Route::prefix('profile/post/')->controller(PostController::class)->group(function () {
    Route::get('/create-post', 'create')->name('post.create');
    Route::get('/edit/{post}', 'edit')->name('post.edit');
    Route::post('/post-post', 'store')->name('post.store');
    Route::put('/upate-post/{post}', 'update')->name('post.update');
    Route::delete('/delete-post/{post}', 'destroy')->name('post.destroy');
});

Route::get('/users/list/{type}/{model}{id}', [ShowUsersController::class, 'index'])->name('users.list');

Route::get('/my-coupons', [MyCouponsController::class, 'index'])->name('my-coupons');

Route::get('/saved', [BookmarkController::class, 'Saved'])->name('saved');

Route::get('/my-companies', [CompanyController::class, 'index'])
    ->name('companies.index');

Route::get('/notifications', [NotificationsController::class, 'index'])->name('notifications');
Route::delete('/notifications/{id}', [NotificationsController::class, 'destroy'])->name('notifications.destroy');

Route::get('/view/{coupon:slug}/scan', [ShowCouponQrController::class, 'show'])->name('coupon.scan');


Route::post('/company/subscribe/{company:slug}', [CompanySubscriptionController::class, 'subscribe'])->name('company.subscribe');
Route::post('/company/unsubscribe/{company:slug}', [CompanySubscriptionController::class, 'unsubscribe'])->name('company.unsubscribe');

Route::group(['prefix' => 'p'], function () {

    Route::get('/become-a-partner', [PartnerController::class, 'index'])->name('partner.become-a-partner');
    Route::post('/apply', [PartnerController::class, 'apply'])->name('partner.apply');
    Route::post('/subscribe', [PartnerController::class, 'subscribe'])->name('partner.subscribe');
});

Route::post('/change/avatar', [ModelMediaController::class, 'change'])->name('change.avatar');

Route::group(['prefix' => 'comment', 'controller' => CommentController::class], function () {
    Route::get('/{class}/{id}', 'index')->name('comment.index')->withoutMiddleware('auth');
    Route::post('/store/{class}/{id}', 'store')->name('comment.store');
    Route::get('/{comment}', 'show')->name('comment.show');
    Route::put('/{comment}', 'update')->name('comment.update');
    Route::delete('/{comment}', 'destroy')->name('comment.destroy');
});
