<?php

use App\Modules\Game\Http\Controllers\cutsController;
use App\Shared\Http\Controllers\WalletController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Wallet Routes
|--------------------------------------------------------------------------
|
| Here are the routes for the wallet system including cuts management,
| payment processing, subscriptions, and Stripe integration.
|
*/

// Legacy cuts route
Route::get('/cuts', [cutsController::class, 'index'])->name('cuts');

// Public webhook route (no auth required)
Route::post('/webhook/stripe', [WalletController::class, 'webhook'])->name('webhook.stripe');

// Authenticated wallet routes
Route::middleware(['auth', 'verified'])->group(function () {

    // Main wallet dashboard
    Route::get('/wallet', [WalletController::class, 'index'])->name('index');

    // Cuts plans
    Route::get('/wallet/plans', [WalletController::class, 'plans'])->name('plans');
    Route::post('/wallet/plans/{plan}/purchase', [WalletController::class, 'purchasePlan'])->name('plans.purchase');

    // Payment verification
    Route::get('/wallet/verification', [WalletController::class, 'verification'])->name('verification');
    Route::post('/wallet/verification/process', [WalletController::class, 'processVerification'])->name('verification.process');
    Route::get('/wallet/verification/return', [WalletController::class, 'verificationReturn'])->name('verification.return');

    // Transaction history
    Route::get('/wallet/transactions', [WalletController::class, 'transactions'])->name('transactions');

    // Payment methods
    Route::post('/wallet/setup-intent', [WalletController::class, 'createSetupIntent'])->name('setup-intent');
    Route::post('/wallet/payment-methods', [WalletController::class, 'addPaymentMethod'])->name('payment-methods.add');
    Route::delete('/wallet/payment-methods/{paymentMethod}', [WalletController::class, 'removePaymentMethod'])->name('payment-methods.remove');
    Route::patch('/wallet/payment-methods/{paymentMethod}/default', [WalletController::class, 'setDefaultPaymentMethod'])->name('payment-methods.default');

    // Subscriptions
    Route::delete('/wallet/subscription', [WalletController::class, 'cancelSubscription'])->name('subscription.cancel');

    // Return pages
    Route::get('/wallet/purchase/return', [WalletController::class, 'purchaseReturn'])->name('purchase.return');

    // API endpoints
    Route::prefix('api/wallet')->group(function () {
        Route::get('/balance', [WalletController::class, 'balance'])->name('api.balance');
        Route::post('/spend-cuts', [WalletController::class, 'spendCuts'])->name('api.spend-cuts');
    });
});
