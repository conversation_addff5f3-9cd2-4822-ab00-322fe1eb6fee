<?php

use App\Modules\Game\Http\Controllers\cutsController;
use App\Shared\Http\Controllers\WalletController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Wallet Routes
|--------------------------------------------------------------------------
|
| Here are the routes for the wallet system including cuts management,
| payment processing, subscriptions, and Stripe integration.
|
*/

// Legacy cuts route
Route::get('/cuts', [cutsController::class, 'index'])->name('cuts');

// Public webhook route (no auth required)
Route::post('/webhook/stripe', [WalletController::class, 'webhook'])->name('wallet.webhook.stripe');

// Authenticated wallet routes
Route::middleware(['auth', 'verified'])->group(function () {

    // Main wallet dashboard
    Route::get('/wallet', [WalletController::class, 'index'])->name('wallet.index');

    // Cuts plans
    Route::get('/wallet/plans', [WalletController::class, 'plans'])->name('wallet.plans');
    Route::post('/wallet/plans/{plan}/purchase', [WalletController::class, 'purchasePlan'])->name('wallet.plans.purchase');

    // Payment verification
    Route::get('/wallet/verification', [WalletController::class, 'verification'])->name('wallet.verification');
    Route::post('/wallet/verification/process', [WalletController::class, 'processVerification'])->name('wallet.verification.process');
    Route::get('/wallet/verification/return', [WalletController::class, 'verificationReturn'])->name('wallet.verification.return');

    // Transaction history
    Route::get('/wallet/transactions', [WalletController::class, 'transactions'])->name('wallet.transactions');

    // Payment methods
    Route::post('/wallet/setup-intent', [WalletController::class, 'createSetupIntent'])->name('wallet.setup-intent');
    Route::post('/wallet/payment-methods', [WalletController::class, 'addPaymentMethod'])->name('wallet.payment-methods.add');
    Route::delete('/wallet/payment-methods/{paymentMethod}', [WalletController::class, 'removePaymentMethod'])->name('wallet.payment-methods.remove');
    Route::patch('/wallet/payment-methods/{paymentMethod}/default', [WalletController::class, 'setDefaultPaymentMethod'])->name('wallet.payment-methods.default');

    // Subscriptions
    Route::delete('/wallet/subscription', [WalletController::class, 'cancelSubscription'])->name('wallet.subscription.cancel');

    // Return pages
    Route::get('/wallet/purchase/return', [WalletController::class, 'purchaseReturn'])->name('wallet.purchase.return');

    // API endpoints
    Route::prefix('api/wallet')->group(function () {
        Route::get('/balance', [WalletController::class, 'balance'])->name('api.wallet.balance');
        Route::post('/spend-cuts', [WalletController::class, 'spendCuts'])->name('api.wallet.spend-cuts');
    });
});
