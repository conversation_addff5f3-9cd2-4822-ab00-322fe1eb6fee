<?php

use App\Shared\Http\Controllers\Actions\ReportAction;
use App\Shared\Http\Controllers\Actions\ReportController;
use App\Shared\Http\Controllers\Actions\SetLocale;
use App\Shared\Http\Controllers\Auth\AuthController;
use App\Shared\Http\Controllers\Auth\WorkOSAuthController;
use App\Shared\Http\Controllers\Helpers\CacheController;
use App\Shared\Http\Controllers\Helpers\InteractionController;
use App\Shared\Http\Controllers\Helpers\MediaController;
use App\Shared\Http\Controllers\Helpers\RedirectController;
use Illuminate\Support\Facades\Route;

//---------------------------GUEST---------------------------

Route::get('/authenticate', [WorkOSAuthController::class, 'handleCallback']);

Route::prefix('auth')->group(function () {
    Route::get('/', [AuthController::class, 'auth'])->name('auth')->middleware('guest');
    Route::get('/email', [AuthController::class, 'emailAuth'])->name('email.auth')->middleware('guest');
    Route::get('/with/{providerId}', [WorkOSAuthController::class, 'redirectToProvider'])->name('provider')->middleware('guest');
    Route::get('/provider/callback', [WorkOSAuthController::class, 'callback'])->name('provider.callback')->middleware('guest');
    Route::post('/register', [AuthController::class, 'register'])->name('register')->middleware('guest');
    Route::post('/login', [AuthController::class, 'login'])->name('login')->middleware('guest');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');
});

//Shared auth actions
Route::middleware('auth')->group(function () {

    Route::patch('/view/{model}/{id}/rate', [InteractionController::class, 'like'])->name('rate');
    Route::patch('/view/{model}/{id}/bookmark', [InteractionController::class, 'bookmark'])->name('bookmark');

    Route::post('/report/send', [ReportController::class, 'send'])->name('report');

    Route::prefix('media')->group(function () {
        Route::post('/upload-temp', [MediaController::class, 'upload'])
            ->name('media.upload');

        Route::delete('/delete', [MediaController::class, 'delete'])
            ->name('media.destroy');

        Route::post('/sort', [MediaController::class, 'sort'])
            ->name('media.sort');
    });
});

Route::get('/alert/redirect', [RedirectController::class, 'index'])->name('redirect');
//Actions 
// ???
Route::post('/set/locale/city/{locale}', SetLocale::class)->name('set.locale');


Route::get('/cache/keys', [CacheController::class, 'requestKeys'])->withoutMiddleware(['web']);
Route::delete('/cache/clear', [CacheController::class, 'clear'])->withoutMiddleware(['web']);
Route::post('/cache/set', [CacheController::class, 'set'])->withoutMiddleware(['web']);
