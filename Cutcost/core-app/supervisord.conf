[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:octane]
command=php artisan octane:start --server=frankenphp --host=0.0.0.0 --port=8000
directory=/var/www
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/log/supervisor/octane.log

[program:reverb]
command=php artisan reverb:start --host=0.0.0.0 --port=9000
directory=/var/www
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/log/supervisor/reverb.log

[program:queue]
command=php artisan queue:work --sleep=3 --tries=3
directory=/var/www
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/log/supervisor/queue.log

[program:ziggy]
command=/bin/bash -c "php artisan ziggy:generate && npm run build"
directory=/var/www
autostart=true
autorestart=false
user=www-data
redirect_stderr=true
stdout_logfile=/var/log/supervisor/ziggy.log
