<?php

use App\Modules\Messenger\Models\Chat;
use App\Modules\Partner\Models\Company;
use App\Modules\Messenger\Services\BotService;
use App\Modules\Messenger\Services\MessageService;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    Cache::flush();
    $this->chat = Chat::factory()->create();
    $this->company = Company::factory()->create();

    $this->service = app(BotService::class);
});

it('does nothing if company has no bot', function () {
    $msg = Mockery::mock(MessageService::class);
    $msg->shouldNotReceive('send');

    app()->instance(MessageService::class, $msg);

    $this->service->handleReplyMessage($this->chat, $this->company);
    $this->service->handleFirstMessage($this->chat, $this->company);
});

it('sends reply_message if bot exists', function () {
    $this->company->update([
        'bot' => [
            'builder' => [
                'reply_message' => 'Hello reply!',
            ]
        ],
    ]);

    $msg = Mockery::mock(MessageService::class);
    $msg->shouldReceive('send')
        ->once()
        ->withArgs(function ($chat, $user, $validated, $media) {
            expect($validated['content'])->toBe('Hello reply!');
            expect($validated['type'])->toBe('text');
            return true;
        });

    app()->instance(MessageService::class, $msg);

    $this->service->handleReplyMessage($this->chat, $this->company);
});

it('sends chat_created_message if bot exists', function () {
    $this->company->update([
        'bot' => [
            'builder' => [
                'chat_created_message' => 'Welcome message!',
            ]
        ],
    ]);

    $msg = Mockery::mock(MessageService::class);
    $msg->shouldReceive('send')
        ->once()
        ->withArgs(function ($chat, $user, $validated, $media) {
            expect($validated['content'])->toBe('Welcome message!');
            return true;
        });

    app()->instance(MessageService::class, $msg);

    $this->service->handleFirstMessage($this->chat, $this->company);
});

it('does not send if cache already has flag', function () {
    $this->company->update([
        'bot' => [
            'builder' => [
                'reply_message' => 'Cached hello',
            ]
        ],
    ]);

    Cache::forever('bot_reply_message_' . $this->chat->id, true);

    $msg = Mockery::mock(MessageService::class);
    $msg->shouldNotReceive('send');

    app()->instance(MessageService::class, $msg);

    $this->service->handleReplyMessage($this->chat, $this->company);
});
