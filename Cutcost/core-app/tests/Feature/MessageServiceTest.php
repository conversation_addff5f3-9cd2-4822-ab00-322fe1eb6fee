<?php

use App\Modules\Messenger\Events\MessageDeleted;
use App\Modules\Messenger\Events\MessageSent;
use App\Modules\Messenger\Events\MessageUpdated;
use App\Modules\Messenger\Events\MessagesSeen;
use App\Modules\Messenger\Models\Chat;
use App\Modules\Messenger\Models\Message;
use App\Modules\Messenger\Services\MessageService;
use App\Shared\Models\User;
use App\Shared\Services\MediaService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    Event::fake();
    $this->service = app(MessageService::class);
    $this->chat = Chat::factory()->create();
    $this->user = User::factory()->create();
});

it('can send message', function () {
    $validated = ['content' => 'hello world', 'type' => 'text'];

    $this->service->send($this->chat, $this->user, $validated, null);

    $message = $this->chat->messages()->first();

    expect($message)->not->toBeNull()
        ->and($message->content)->toBe('hello world')
        ->and($message->user_id)->toBe($this->user->id);

    Event::assertDispatched(MessageSent::class);
});

it('can update message', function () {
    $message = Message::factory()->for($this->chat)->for($this->user)->create([
        'content' => 'old',
    ]);

    $mediaService = Mockery::mock(MediaService::class);
    $mediaService->shouldReceive('updateRequest')->once();

    $this->service->update($message, $this->chat, ['content' => 'new', 'upload' => []], $mediaService);

    $message->refresh();

    expect($message->content)->toBe('new')
        ->and($message->edited)->toBeTrue();

    Event::assertDispatched(MessageUpdated::class);
});

it('can delete message', function () {
    $message = Message::factory()->for($this->chat)->for($this->user)->create();

    $this->service->destroy($message, $this->chat);

    expect(Message::find($message->id))->toBeNull();

    Event::assertDispatched(MessageDeleted::class);
});

it('marks messages as seen', function () {
    $friend = User::factory()->create();

    $m1 = Message::factory()->for($this->chat)->for($friend)->create(['status' => 'unread']);
    $m2 = Message::factory()->for($this->chat)->for($this->user)->create(['status' => 'unread']); // сообщение текущего юзера
    $messages = new Collection([$m1, $m2]);

    $result = $this->service->read($this->chat, $this->user, $messages);

    $m1->refresh();

    expect($m1->status)->toBe('seen');
    expect($m2->status)->toBe('unread'); // свои не меняются

    Event::assertDispatched(MessagesSeen::class);
});
