<?php

namespace Tests\Feature\Partner;

use App\Modules\Partner\Services\BusinessHoursService;
use App\Modules\Partner\Services\DaySchedule;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class BusinessHoursServiceTest extends TestCase
{
    protected BusinessHoursService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new BusinessHoursService();
        Cache::flush();
    }

    public function testDayScheduleIsOffDay()
    {
        $schedule = new DaySchedule(DaySchedule::TYPE_OFF, null, null);
        $this->assertTrue($schedule->isOffDay());
    }

    public function testDayScheduleIsOpen24()
    {
        $schedule = new DaySchedule(DaySchedule::TYPE_OPEN24, null, null);
        $this->assertTrue($schedule->isOpen24());
    }

    public function testCurrentlyOpenWithoutBreaks()
    {
        $now = CarbonImmutable::parse('2025-07-28 10:00:00', 'Europe/Moscow');
        CarbonImmutable::setTestNow($now);

        $schedule = new DaySchedule(DaySchedule::TYPE_WORK, '09:00', '18:00');
        $this->assertTrue($schedule->isCurrentlyOpen($now));
    }

    public function testClosedBeforeWorkTime()
    {
        $now = CarbonImmutable::parse('2025-07-28 08:00:00', 'Europe/Moscow');
        CarbonImmutable::setTestNow($now);

        $schedule = new DaySchedule(DaySchedule::TYPE_WORK, '09:00', '18:00');
        $this->assertFalse($schedule->isCurrentlyOpen($now));
    }

    public function testClosedDuringBreak()
    {
        $now = CarbonImmutable::parse('2025-07-28 13:00:00', 'Europe/Moscow');
        CarbonImmutable::setTestNow($now);

        $breaks = [['start' => '12:00', 'end' => '14:00']];
        $schedule = new DaySchedule(DaySchedule::TYPE_WORK, '09:00', '18:00', $breaks);
        $this->assertFalse($schedule->isCurrentlyOpen($now));
        $this->assertSame('closed_by_break', $schedule->closureReason($now));
    }

    public function testNextCloseTime()
    {
        $now = CarbonImmutable::parse('2025-07-28 10:00:00', 'Europe/Moscow');
        CarbonImmutable::setTestNow($now);

        $schedule = new DaySchedule(DaySchedule::TYPE_WORK, '09:00', '18:00');
        $this->assertEquals('18:00:00', $schedule->nextCloseTime($now)->format('H:i:s'));
    }

    public function testNextOpenAfterBreak()
    {
        $now = CarbonImmutable::parse('2025-07-28 12:30:00', 'Europe/Moscow');
        CarbonImmutable::setTestNow($now);

        $breaks = [['start' => '12:00', 'end' => '13:00']];
        $schedule = new DaySchedule(DaySchedule::TYPE_WORK, '09:00', '18:00', $breaks);

        $nextOpen = $schedule->nextOpenAfterBreak($now);
        $this->assertEquals('13:00:00', $nextOpen->format('H:i:s'));
    }

    public function testIsOpenNowClosedManually()
    {
        $location = (object)[
            'id' => 1,
            'is_open' => false,
            'is_visible' => true,
            'rent_expires_at' => null,
            'working_hours' => [],
            'timezone' => 'Europe/Moscow',
        ];

        $result = $this->service->isOpenNow($location);
        $this->assertFalse($result['open']);
        $this->assertEquals(__('app.open_hours.closed_manually'), $result['reason']);
    }

    public function testIsOpenNowClosedNotVisible()
    {
        $location = (object)[
            'id' => 2,
            'is_open' => true,
            'is_visible' => false,
            'rent_expires_at' => null,
            'working_hours' => [],
            'timezone' => 'Europe/Moscow',
        ];

        $result = $this->service->isOpenNow($location);
        $this->assertFalse($result['open']);
    }

    public function testIsOpenNowRentExpired()
    {
        $location = (object)[
            'id' => 3,
            'is_open' => true,
            'is_visible' => true,
            'rent_expires_at' => now()->subDay(),
            'working_hours' => [],
            'timezone' => 'Europe/Moscow',
        ];

        $result = $this->service->isOpenNow($location);
        $this->assertFalse($result['open']);
    }

    public function testIsOpenNowOpen24()
    {
        $location = (object)[
            'id' => 2,
            'slug' => '24d95e93-ea3f-470c-a638-5618aa192ffa',
            'name' => [
                'en' => 'Kaunas Spa Center',
                'lt' => 'Kauno SPA Centras',
                'ru' => 'Каунас СПА Центр',
            ],
            'description' => [
                'en' => 'Relax and rejuvenate at Kaunas Spa Center.',
                'lt' => 'Atsipalaiduokite Kauno SPA centre.',
                'ru' => 'Расслабьте и омолодитесь в Kaunas Spa Center.',
            ],
            'address' => 'Laisvės al. 10, Kaunas, Lithuania',
            'lnglat' => null,
            'working_hours' => [
                [
                    'i' => 0,
                    'name' => 'Monday',
                    'type' => 'work_day',
                    'open24' => true,
                    'timeStart' => null,
                    'timeEnd' => null,
                    'breaks' => [],
                ],
                [
                    'i' => 1,
                    'name' => 'Tuesday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 2,
                    'name' => 'Wednesday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 3,
                    'name' => 'Thursday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 4,
                    'name' => 'Friday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 5,
                    'name' => 'Saturday',
                    'type' => 'off_day',
                    'open24' => false,
                    'timeStart' => null,
                    'timeEnd' => null,
                    'breaks' => [],
                ],
                [
                    'i' => 6,
                    'name' => 'Sunday',
                    'type' => 'off_day',
                    'open24' => false,
                    'timeStart' => null,
                    'timeEnd' => null,
                    'breaks' => [],
                ],
            ],
            'is_open' => true,
            'is_visible' => true,
            'type' => 'offline',
            'company_id' => 4,
            'user_id' => 1,
            'city_id' => 20656,
            'created_at' => '2025-08-04 11:13:17',
            'updated_at' => '2025-08-04 13:45:56',
            'timezone' => 'Europe/Moscow',
        ];

        CarbonImmutable::setTestNow(CarbonImmutable::parse('2025-07-28 10:00:00', 'Europe/Moscow'));

        $result = $this->service->isOpenNow($location);
        $this->assertTrue($result['open']);
        $this->assertSame('24/7', $result['reason']);
    }

    public function testIsOpenNowDuringWorkDay()
    {
        $location = (object)[
            'id' => 2,
            'slug' => '24d95e93-ea3f-470c-a638-5618aa192ffa',
            'name' => [
                'en' => 'Kaunas Spa Center',
                'lt' => 'Kauno SPA Centras',
                'ru' => 'Каунас СПА Центр',
            ],
            'description' => [
                'en' => 'Relax and rejuvenate at Kaunas Spa Center.',
                'lt' => 'Atsipalaiduokite Kauno SPA centre.',
                'ru' => 'Расслабьте и омолодитесь в Kaunas Spa Center.',
            ],
            'address' => 'Laisvės al. 10, Kaunas, Lithuania',
            'lnglat' => null,
            'working_hours' => [
                [
                    'i' => 0,
                    'name' => 'Monday',
                    'type' => 'work_day',
                    'open24' => true,
                    'timeStart' => null,
                    'timeEnd' => null,
                    'breaks' => [],
                ],
                [
                    'i' => 1,
                    'name' => 'Tuesday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 2,
                    'name' => 'Wednesday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 3,
                    'name' => 'Thursday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 4,
                    'name' => 'Friday',
                    'type' => 'work_day',
                    'open24' => false,
                    'timeStart' => '09:00',
                    'timeEnd' => '18:00',
                    'breaks' => [],
                ],
                [
                    'i' => 5,
                    'name' => 'Saturday',
                    'type' => 'off_day',
                    'open24' => false,
                    'timeStart' => null,
                    'timeEnd' => null,
                    'breaks' => [],
                ],
                [
                    'i' => 6,
                    'name' => 'Sunday',
                    'type' => 'off_day',
                    'open24' => false,
                    'timeStart' => null,
                    'timeEnd' => null,
                    'breaks' => [],
                ],
            ],
            'is_open' => true,
            'is_visible' => true,
            'type' => 'offline',
            'company_id' => 4,
            'user_id' => 1,
            'city_id' => 20656,
            'created_at' => '2025-08-04 11:13:17',
            'updated_at' => '2025-08-04 13:45:56',
            'timezone' => 'Europe/Moscow',
        ];

        CarbonImmutable::setTestNow(CarbonImmutable::parse('2025-07-28 10:00:00', 'Europe/Moscow'));

        $result = $this->service->isOpenNow($location);

        $this->assertSame(true, $result['open']);
        $this->assertNull($result['reason'], 'Ожидалась причина = null при открытом состоянии');
    }
}
