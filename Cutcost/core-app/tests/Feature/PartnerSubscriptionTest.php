<?php

use App\Modules\Partner\Models\Billing\Plan;
use App\Modules\Partner\Models\Billing\Subscription;
use App\Modules\Partner\Models\Billing\Partner;
use App\Modules\Partner\Models\Billing\PartnerApplication;
use App\Shared\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use App\Modules\Partner\Contracts\PaymentResult;
use App\Modules\Partner\Contracts\PaymentContract;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->actingAs($this->user);

    $this->plan = Plan::create([
        'code' => 'pro',
        'name' => 'Про',
        'description' => 'Расширенный доступ',
        'price' => 990,
        'interval' => 'month',
        'is_active' => true,
    ]);

    App::bind(PaymentContract::class, FakePaymentGatewayForTest::class);
});

class FakePaymentGatewayForTest implements PaymentContract
{
    public function createSubscription(User $user, Plan $plan): PaymentResult
    {
        return new PaymentResult(true, 'fake', 'sub_123456');
    }
}

class FakeFailPaymentGateway implements PaymentContract
{
    public function createSubscription(User $user, Plan $plan): PaymentResult
    {
        return new PaymentResult(false, 'fake', null);
    }
}

test('user can subscribe and become a partner', function () {
    $response = $this->postJson(route('seller.partner.subscribe'), [
        'plan_id' => $this->plan->id,
        'full_name' => 'Иван Иванов',
    ]);

    $response->assertStatus(200)
        ->assertJson(['message' => 'Вы стали партнёром!']);

    expect(Subscription::where('user_id', $this->user->id)->exists())->toBeTrue();
    expect(Partner::where('user_id', $this->user->id)->exists())->toBeTrue();
});

test('user can apply for partnership', function () {
    $response = $this->postJson(route('seller.partner.apply'), [
        'full_name' => 'Иван Иванов',
    ]);

    $response->assertStatus(200)
        ->assertJson(['message' => 'Заявка отправлена. Ожидайте одобрения.']);

    $this->assertDatabaseHas('partner_applications', [
        'user_id' => $this->user->id,
        'status' => 'pending',
    ]);
});

test('user can get partner status with no data', function () {
    $response = $this->getJson(route('seller.partner.become-a-partner'));

    $response->assertStatus(200)
        ->assertJson([
            'application_status' => null,
            'subscription_status' => null,
            'is_partner' => false,
        ]);
});

test('user can get partner status with data', function () {
    PartnerApplication::create([
        'user_id' => $this->user->id,
        'status' => 'approved',
        'approved_at' => now(),
    ]);

    Subscription::create([
        'user_id' => $this->user->id,
        'plan_id' => $this->plan->id,
        'provider' => 'fake',
        'subscription_id' => 'sub_123',
        'status' => 'active',
        'starts_at' => now(),
        'renews_at' => now()->addMonth(),
    ]);

    Partner::create([
        'user_id' => $this->user->id,
        'full_name' => 'Иван Иванов',
        'joined_at' => now(),
    ]);

    $response = $this->getJson(route('seller.partner.become-a-partner'));

    $response->assertStatus(200)
        ->assertJson([
            'application_status' => 'approved',
            'subscription_status' => 'active',
            'is_partner' => true,
        ]);
});



test('subscription fails when payment gateway returns failure', function () {
    App::bind(PaymentContract::class, FakeFailPaymentGateway::class);

    $response = $this->postJson(route('seller.partner.subscribe'), [
        'plan_id' => $this->plan->id,
        'full_name' => 'Иван Иванов',
    ]);

    $response->assertStatus(422)
        ->assertJson(['error' => 'Не удалось оформить подписку']);
});

test('subscribe validation fails with missing fields', function () {
    $response = $this->postJson(route('seller.partner.subscribe'), []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['plan_id', 'full_name']);
});

test('apply validation fails with missing full_name', function () {
    $response = $this->postJson(route('seller.partner.apply'), []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['full_name']);
});
