import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import PaginatedList from '../../resources/js/Client/Shared/Features/PaginatedList.vue'

// Mock the usePagination composable
const mockPaginationData = {
  data: [],
  cursor: null,
  status: false
}

const mockLoadNext = vi.fn()
const mockTextNeeded = { value: false }

vi.mock('@s', () => ({
  usePagination: () => ({
    paginationData: mockPaginationData,
    loadNext: mockLoadNext,
    textNeeded: mockTextNeeded
  })
}))

// Mock the route injection
const mockRoute = vi.fn((routeName) => {
  if (routeName === 'load.paginated') {
    return '/api/load-paginated'
  }
  return `/${routeName}`
})

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn()
  }
}))

describe('PaginatedList.vue', () => {
  let wrapper

  const defaultProps = {
    type: 'users',
    required: false,
    filters: {}
  }

  const createWrapper = (props = {}, modelValue = null) => {
    return mount(PaginatedList, {
      props: {
        ...defaultProps,
        ...props,
        modelValue
      },
      global: {
        provide: {
          route: mockRoute
        },
        stubs: {
          'x-select': {
            template: '<div data-testid="x-select"><slot /></div>',
            props: ['modelValue', 'data', 'required'],
            emits: ['update:modelValue', 'search', 'intersect']
          }
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockPaginationData.data = []
    mockPaginationData.cursor = null
    mockPaginationData.status = false
    mockTextNeeded.value = false
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Component Initialization', () => {
    it('renders without crashing', () => {
      wrapper = createWrapper()
      expect(wrapper.exists()).toBe(true)
    })

    it('shows loading skeleton when loading is true', async () => {
      wrapper = createWrapper({}, [1, 2, 3])
      
      // Initially loading should be true when model has value
      expect(wrapper.find('[data-testid="x-select"]').exists()).toBe(false)
      expect(wrapper.find('.animate-pulse').exists()).toBe(true)
    })

    it('shows x-select when not loading', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="x-select"]').exists()).toBe(true)
      expect(wrapper.find('.animate-pulse').exists()).toBe(false)
    })

    it('passes correct props to x-select', () => {
      wrapper = createWrapper({ required: true })
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      
      expect(xSelect.props('required')).toBe(true)
      expect(xSelect.props('data')).toEqual([])
    })
  })

  describe('Search Functionality', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('handles search input correctly', async () => {
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      
      await xSelect.vm.$emit('search', 'test query')
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: 'test query', 
          ids: null, 
          country_id: null 
        },
        noCache: true,
        type: 'users'
      })
    })

    it('converts search query to lowercase', async () => {
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      
      await xSelect.vm.$emit('search', 'TEST QUERY')
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: 'test query', 
          ids: null, 
          country_id: null 
        },
        noCache: true,
        type: 'users'
      })
    })

    it('handles empty search query', async () => {
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      
      await xSelect.vm.$emit('search', '')
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: '', 
          ids: null, 
          country_id: null 
        },
        noCache: true,
        type: 'users'
      })
    })

    it('resets pagination data on search', async () => {
      mockPaginationData.data = [{ id: 1, name: 'existing' }]
      mockPaginationData.cursor = 'some-cursor'
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      await xSelect.vm.$emit('search', 'new search')
      
      expect(mockPaginationData.data).toEqual([])
      expect(mockPaginationData.cursor).toBeNull()
    })
  })

  describe('Data Sorting', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('sorts data correctly with selected items first', async () => {
      // Set up test data
      mockPaginationData.data = [
        { id: 1, name: 'Apple' },
        { id: 2, name: 'Banana' },
        { id: 3, name: 'Cherry' }
      ]
      
      // Set model value to select item with id 2
      await wrapper.setProps({ modelValue: [2] })
      await nextTick()
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      const sortedData = xSelect.props('data')
      
      // Selected item (id: 2) should be first
      expect(sortedData[0].id).toBe(2)
      expect(sortedData[0].name).toBe('Banana')
    })

    it('handles search filtering correctly', async () => {
      mockPaginationData.data = [
        { id: 1, name: 'Apple' },
        { id: 2, name: 'Banana' },
        { id: 3, name: 'Cherry' }
      ]
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      
      // Trigger search
      await xSelect.vm.$emit('search', 'app')
      await nextTick()
      
      // Should filter items containing 'app' (case insensitive)
      const sortedData = xSelect.props('data')
      expect(sortedData.some(item => item.name.toLowerCase().includes('app'))).toBe(true)
    })

    it('handles single model value correctly', async () => {
      mockPaginationData.data = [
        { id: 1, name: 'Apple' },
        { id: 2, name: 'Banana' }
      ]
      
      await wrapper.setProps({ modelValue: 1 })
      await nextTick()
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      const sortedData = xSelect.props('data')
      
      expect(sortedData[0].id).toBe(1)
    })
  })

  describe('Intersection Observer', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('calls load on intersect when conditions are met', async () => {
      mockTextNeeded.value = false
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      await xSelect.vm.$emit('intersect')
      
      expect(mockLoadNext).toHaveBeenCalled()
    })

    it('does not call load on intersect when textNeeded is true', async () => {
      mockTextNeeded.value = true
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      await xSelect.vm.$emit('intersect')
      
      expect(mockLoadNext).not.toHaveBeenCalled()
    })

    it('does not call load on intersect when search query exists', async () => {
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      
      // First set a search query
      await xSelect.vm.$emit('search', 'test')
      vi.clearAllMocks()
      
      // Then try to intersect
      await xSelect.vm.$emit('intersect')
      
      expect(mockLoadNext).not.toHaveBeenCalled()
    })
  })

  describe('Model Value Handling', () => {
    it('loads data on mount when model has value', async () => {
      wrapper = createWrapper({}, [1, 2])
      
      // Wait for onMounted to complete
      await nextTick()
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: '', 
          ids: [1, 2], 
          country_id: null 
        },
        noCache: true,
        type: 'users'
      })
    })

    it('handles single model value on mount', async () => {
      wrapper = createWrapper({}, 1)
      
      await nextTick()
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: '', 
          ids: [1], 
          country_id: null 
        },
        noCache: true,
        type: 'users'
      })
    })

    it('does not load data on mount when model is empty', async () => {
      wrapper = createWrapper()
      
      await nextTick()
      
      expect(mockLoadNext).not.toHaveBeenCalled()
    })
  })

  describe('Props Handling', () => {
    it('passes country_id filter when provided', async () => {
      wrapper = createWrapper({ country_id: 123 })
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      await xSelect.vm.$emit('search', 'test')
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: 'test', 
          ids: null, 
          country_id: 123 
        },
        noCache: true,
        type: 'users'
      })
    })

    it('uses correct type prop', async () => {
      wrapper = createWrapper({ type: 'companies' })
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      await xSelect.vm.$emit('search', 'test')
      
      expect(mockLoadNext).toHaveBeenCalledWith('/api/load-paginated', {
        filters: { 
          search_query: 'test', 
          ids: null, 
          country_id: null 
        },
        noCache: true,
        type: 'companies'
      })
    })
  })

  describe('Slot Rendering', () => {
    it('passes item data to default slot', () => {
      mockPaginationData.data = [{ id: 1, name: 'Test Item' }]
      
      wrapper = createWrapper({}, null, {
        slots: {
          default: '<div data-testid="slot-content">{{ item.name }}</div>'
        }
      })
      
      const xSelect = wrapper.findComponent({ name: 'x-select' })
      expect(xSelect.exists()).toBe(true)
    })
  })
})
