# Vue Component Tests

This directory contains comprehensive tests for Vue components and composables in the Cutcost application.

## Setup

Before running the tests, you need to install the required testing dependencies:

```bash
npm install --save-dev vitest @vue/test-utils jsdom @vitest/ui happy-dom
```

## Test Files

### 1. PaginatedList.test.js
Unit tests for the `PaginatedList.vue` component covering:
- Component initialization and rendering
- Search functionality
- Data sorting and filtering
- Intersection observer behavior
- Model value handling
- Props validation
- Slot rendering

### 2. PaginatedList.integration.test.js
Integration tests for real-world scenarios:
- Complete user selection workflows
- Error handling (API failures, malformed responses)
- Performance with large datasets
- Accessibility and UX considerations
- Data consistency during multiple operations
- Memory management

### 3. usePagination.test.js
Tests for the `usePagination` composable:
- Initial state management
- Status management (loading, noMore, nothing)
- Cache operations (load/save)
- Data loading and API integration
- Loading state management
- Debouncing functionality

## Running Tests

### Run all tests
```bash
npm run test
```

### Run tests in watch mode
```bash
npm run test
```

### Run tests once (CI mode)
```bash
npm run test:run
```

### Run tests with UI
```bash
npm run test:ui
```

### Run specific test file
```bash
npx vitest PaginatedList.test.js
```

### Run tests with coverage
```bash
npx vitest --coverage
```

## Test Structure

Each test file follows this structure:

1. **Imports and Mocks**: Import necessary testing utilities and mock external dependencies
2. **Setup/Teardown**: Configure test environment before/after each test
3. **Test Suites**: Organized by functionality (e.g., "Search Functionality", "Data Sorting")
4. **Individual Tests**: Specific test cases with descriptive names

## Mocking Strategy

### External Dependencies
- `@s` module (Shared components and composables)
- `axios` for HTTP requests
- `route` helper function
- Global objects like `window.location`

### Component Stubs
- `x-select` component is stubbed to focus on PaginatedList logic
- Provides necessary props and emits for interaction testing

## Key Testing Patterns

### 1. Component Testing
```javascript
const wrapper = mount(PaginatedList, {
  props: { type: 'users', required: false },
  global: {
    provide: { route: mockRoute },
    stubs: { 'x-select': StubComponent }
  }
})
```

### 2. Async Testing
```javascript
await xSelect.vm.$emit('search', 'query')
await nextTick()
expect(mockLoadNext).toHaveBeenCalled()
```

### 3. Mock Verification
```javascript
expect(mockLoadNext).toHaveBeenCalledWith('/api/endpoint', {
  filters: { search_query: 'query' },
  noCache: true,
  type: 'users'
})
```

## Coverage Goals

The tests aim to achieve:
- **Line Coverage**: >90%
- **Branch Coverage**: >85%
- **Function Coverage**: 100%

## Common Issues and Solutions

### 1. Mock Not Working
Ensure mocks are defined before importing the component:
```javascript
vi.mock('@s', () => ({ /* mock implementation */ }))
// Import after mocking
import PaginatedList from '../../path/to/component'
```

### 2. Async Operations
Always use `await nextTick()` after triggering events that cause reactive updates.

### 3. Console Warnings
Mock console methods in setup.js to avoid noise in test output:
```javascript
global.console = { ...console, warn: vi.fn() }
```

## Best Practices

1. **Descriptive Test Names**: Use clear, descriptive names that explain what is being tested
2. **Arrange-Act-Assert**: Structure tests with clear setup, action, and verification phases
3. **Isolated Tests**: Each test should be independent and not rely on others
4. **Mock External Dependencies**: Mock all external dependencies to focus on unit behavior
5. **Test Edge Cases**: Include tests for error conditions, empty states, and boundary values

## Debugging Tests

### View Test Output
```bash
npx vitest --reporter=verbose
```

### Debug Specific Test
```bash
npx vitest --reporter=verbose PaginatedList.test.js -t "search functionality"
```

### Use Console Logging
Add `console.log` statements in tests (they will be captured by Vitest):
```javascript
console.log('Component data:', wrapper.vm.sortedData)
```

## Contributing

When adding new tests:
1. Follow the existing naming conventions
2. Add tests for both happy path and error scenarios
3. Update this README if adding new test patterns
4. Ensure all tests pass before submitting PR
