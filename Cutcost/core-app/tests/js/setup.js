import { vi } from 'vitest'

// Mock global objects that might be used in components
global.console = {
  ...console,
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn()
}

// Mock window object
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    hostname: 'localhost'
  },
  writable: true
})

// Mock axios globally
vi.mock('axios', () => ({
  default: {
    get: vi.fn(() => Promise.resolve({ data: { items: [], next_cursor: null, status: 'nothing' } })),
    post: vi.fn(() => Promise.resolve({ data: {} })),
    put: vi.fn(() => Promise.resolve({ data: {} })),
    delete: vi.fn(() => Promise.resolve({ data: {} }))
  }
}))

// Mock route helper globally
global.route = vi.fn((name) => `/${name}`)

// Mock translation helper
global.__ = vi.fn((key) => key)
