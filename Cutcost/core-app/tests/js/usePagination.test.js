import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { nextTick } from 'vue'
import axios from 'axios'

// Mock the cache store
const mockCache = {
  get: vi.fn(),
  set: vi.fn()
}

// Mock the helpers
const mockDebounce = vi.fn((fn) => fn)

vi.mock('@s', async () => {
  const actual = await vi.importActual('@s')
  return {
    ...actual,
    useCache: () => mockCache,
    useHelpers: () => ({
      debounce: mockDebounce
    })
  }
})

// Import after mocking
const { usePagination } = await import('../../resources/js/Client/Shared/Composables/usePagination.js')

describe('usePagination Composable', () => {
  let pagination

  beforeEach(() => {
    vi.clearAllMocks()
    pagination = usePagination()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('initializes with correct default values', () => {
      expect(pagination.paginationData.data).toEqual([])
      expect(pagination.paginationData.cursor).toBeNull()
      expect(pagination.paginationData.status).toBe(false)
      expect(pagination.loading.value).toBeUndefined()
    })

    it('provides correct computed properties', () => {
      expect(pagination.textNeeded.value).toBe(false)
      expect(pagination.bottomText.value).toBe('Loading')
    })
  })

  describe('Status Management', () => {
    it('updates textNeeded when status is noMore', () => {
      pagination.paginationData.status = 'noMore'
      expect(pagination.textNeeded.value).toBe(true)
      expect(pagination.bottomText.value).toBe('Nothing more')
    })

    it('updates textNeeded when status is nothing', () => {
      pagination.paginationData.status = 'nothing'
      expect(pagination.textNeeded.value).toBe(true)
      expect(pagination.bottomText.value).toBe('Nothing')
    })

    it('shows loading text for other statuses', () => {
      pagination.paginationData.status = 'loading'
      expect(pagination.textNeeded.value).toBe(false)
      expect(pagination.bottomText.value).toBe('Loading')
    })
  })

  describe('Cache Operations', () => {
    it('loads data from cache correctly', () => {
      const cachedData = {
        data: [{ id: 1, name: 'Cached Item' }],
        cursor: 'cached-cursor'
      }
      
      mockCache.get.mockReturnValue(cachedData)
      
      pagination.loadByKey('test-key')
      
      expect(mockCache.get).toHaveBeenCalledWith('test-key')
      expect(pagination.paginationData.data).toEqual(cachedData.data)
      expect(pagination.paginationData.cursor).toBe(cachedData.cursor)
    })

    it('handles missing cache gracefully', () => {
      mockCache.get.mockReturnValue(null)
      
      pagination.loadByKey('missing-key')
      
      expect(pagination.paginationData.data).toEqual([])
      expect(pagination.paginationData.cursor).toBeNull()
    })

    it('saves data to cache correctly', () => {
      pagination.paginationData.data = [{ id: 1, name: 'Test' }]
      pagination.paginationData.cursor = 'test-cursor'
      
      pagination.saveByKey('save-key')
      
      expect(mockCache.set).toHaveBeenCalledWith('save-key', {
        data: [{ id: 1, name: 'Test' }],
        cursor: 'test-cursor'
      })
    })
  })

  describe('Data Loading', () => {
    beforeEach(() => {
      vi.mocked(axios.get).mockResolvedValue({
        data: {
          items: [
            { id: 1, name: 'Item 1' },
            { id: 2, name: 'Item 2' }
          ],
          next_cursor: 'next-cursor',
          status: 'loaded'
        }
      })
    })

    it('loads data successfully', async () => {
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(axios.get).toHaveBeenCalledWith('/api/test', {
        params: {
          type: 'users',
          cursor: null
        }
      })
      
      expect(pagination.paginationData.data).toHaveLength(2)
      expect(pagination.paginationData.cursor).toBe('next-cursor')
      expect(pagination.paginationData.status).toBe('loaded')
    })

    it('appends new data to existing data', async () => {
      // Set initial data
      pagination.paginationData.data = [{ id: 0, name: 'Existing' }]
      
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(pagination.paginationData.data).toHaveLength(3)
      expect(pagination.paginationData.data[0].name).toBe('Existing')
      expect(pagination.paginationData.data[1].name).toBe('Item 1')
    })

    it('handles end of data correctly', async () => {
      vi.mocked(axios.get).mockResolvedValue({
        data: {
          items: [{ id: 3, name: 'Last Item' }],
          next_cursor: null,
          status: 'noMore'
        }
      })
      
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(pagination.paginationData.status).toBe('noMore')
      expect(pagination.textNeeded.value).toBe(true)
    })

    it('includes cursor in subsequent requests', async () => {
      pagination.paginationData.cursor = 'existing-cursor'
      
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(axios.get).toHaveBeenCalledWith('/api/test', {
        params: {
          type: 'users',
          cursor: 'existing-cursor'
        }
      })
    })

    it('handles API errors gracefully', async () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
      vi.mocked(axios.get).mockRejectedValue(new Error('API Error'))
      
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(consoleError).toHaveBeenCalledWith('Error paginating:', expect.any(Error))
      expect(pagination.loading.value).toBe(false)
      
      consoleError.mockRestore()
    })

    it('skips caching when noCache is true', async () => {
      await pagination.loadNext('/api/test', { type: 'users', noCache: true })
      
      expect(mockCache.set).not.toHaveBeenCalled()
    })

    it('saves to cache when noCache is false', async () => {
      // Mock window.location.href
      Object.defineProperty(window, 'location', {
        value: { href: 'http://localhost/test' },
        writable: true
      })
      
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(mockCache.set).toHaveBeenCalledWith(
        'http://localhost/testusers',
        expect.objectContaining({
          data: expect.any(Array),
          cursor: expect.any(String)
        })
      )
    })
  })

  describe('Loading State Management', () => {
    it('sets loading to true during request', async () => {
      const loadPromise = pagination.loadNext('/api/test', { type: 'users' })
      
      expect(pagination.loading.value).toBe(true)
      
      await loadPromise
      
      expect(pagination.loading.value).toBe(false)
    })

    it('sets loading to false after error', async () => {
      vi.mocked(axios.get).mockRejectedValue(new Error('Test error'))
      
      await pagination.loadNext('/api/test', { type: 'users' })
      
      expect(pagination.loading.value).toBe(false)
    })
  })

  describe('Debouncing', () => {
    it('applies debouncing to loadNext function', () => {
      expect(mockDebounce).toHaveBeenCalledWith(expect.any(Function), 500)
    })
  })

  describe('Return Values', () => {
    it('returns all expected properties and methods', () => {
      expect(pagination).toHaveProperty('bottomText')
      expect(pagination).toHaveProperty('loadNext')
      expect(pagination).toHaveProperty('paginationData')
      expect(pagination).toHaveProperty('loading')
      expect(pagination).toHaveProperty('loadByKey')
      expect(pagination).toHaveProperty('textNeeded')
      
      expect(typeof pagination.loadNext).toBe('function')
      expect(typeof pagination.loadByKey).toBe('function')
    })
  })
})
