import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import vue from "@vitejs/plugin-vue";
import tailwindcss from "@tailwindcss/vite";
import path from "path";
// import { visualizer } from "rollup-plugin-visualizer";

export default defineConfig({
  resolve: {
    alias: {
      "ziggy-js": path.resolve("vendor/tightenco/ziggy"),
      "@m": path.resolve(__dirname, "resources/js/Client/Modules"),
      "@s": path.resolve(__dirname, "resources/js/Client/Shared"),
    },
  },
  server: {
    host: "127.0.0.1",
    hmr: {
      host: "127.0.0.1",
    },
    watch: {
      ignored: [
        "**/app/**/*.php",
        "**/routes/**/*.php",
        "**/resources/lang/**/*.php",
        "**/resources/views/**/*.blade.php",
      ],
    },
  },
  plugins: [
    tailwindcss(),
    vue(),
    laravel({
      input: ["resources/js/Client/app.js"],
      refresh: true,
    }),
    // visualizer({
    //   filename: "./stats.html",
    //   open: true,
    //   gzipSize: true,
    //   brotliSize: true,
    // }),
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            if (id.includes("vue")) return "vendor-vue";
            if (id.includes("pinia")) return "vendor-pinia";
            if (id.includes("tippy")) return "vendor-tippy";
            if (id.includes("ziggy-js")) return "vendor-ziggy";
            return "vendor";
          }
        },
      },
    },
  },
});
