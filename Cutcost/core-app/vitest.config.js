import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'happy-dom',
    globals: true,
    setupFiles: ['./tests/js/setup.js']
  },
  resolve: {
    alias: {
      '@s': path.resolve(__dirname, 'resources/js/Client/Shared'),
      '@m': path.resolve(__dirname, 'resources/js/Client/Modules'),
      'ziggy-js': path.resolve('vendor/tightenco/ziggy')
    }
  }
})
